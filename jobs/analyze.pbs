#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1:00:00
#PBS -P gs_spms_psengupta
#PBS -N analyze-Shastry
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Analysis job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load anaconda2025/2025
# 注意：anaconda2025会自动加载cuda/12.2作为依赖
# 如果需要其他CUDA版本，请先卸载cuda/12.2再加载所需版本
module unload cuda/12.2 2>/dev/null || true
# module load cuda/12.8

# 激活conda环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate netket

# 验证环境
echo "Python version: $(python --version)"
echo "Python path: $(which python)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# 开始分析

# 定义要分析的参数组合
# 格式: "L J2 J1"
PARAM_SETS=(
  "5 0.00 0.02"
  "5 0.00 0.03"
  "5 0.00 0.04"
  "5 0.00 0.05"
  "5 0.00 0.06"
)

# 并行任务最大数量
max_tasks=1
current_tasks=0

# 遍历所有参数组合并运行分析
for params in "${PARAM_SETS[@]}"; do
  # 提取参数
  read -r L J2 J1 <<< "$params"

  echo "Starting analysis: L=$L, J2=$J2, J1=$J1 at: $(date)"

  # 获取checkpoint目录
  checkpoint_dir="results/L=$L/J2=$J2/J1=$J1/training/checkpoints"

  # 检查checkpoint目录是否存在
  if [ ! -d "$checkpoint_dir" ]; then
    echo "Warning: Checkpoint directory $checkpoint_dir does not exist, skipping..."
    continue
  fi

  # 获取所有checkpoint文件
  checkpoint_files=($(find "$checkpoint_dir" -name "*.pkl" | sort))

  if [ ${#checkpoint_files[@]} -eq 0 ]; then
    echo "Warning: No checkpoint files found in $checkpoint_dir, skipping..."
    continue
  fi

  echo "Found ${#checkpoint_files[@]} checkpoint files for L=$L, J2=$J2, J1=$J1"

  # 为每个checkpoint运行分析
  for checkpoint_file in "${checkpoint_files[@]}"; do
    # 提取checkpoint名称（不含路径和扩展名）
    checkpoint_name=$(basename "$checkpoint_file" .pkl)

    echo "  Processing checkpoint: $checkpoint_name"

    # 运行分析脚本（后台运行）
    python scripts/analyze.py --L $L --J2 $J2 --J1 $J1 --checkpoint "$checkpoint_name" &

    current_tasks=$((current_tasks + 1))

    # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
    if [ $current_tasks -ge $max_tasks ]; then
      wait
      current_tasks=0
      echo "Batch of analysis tasks completed at: $(date)"
    fi
  done


  echo "Submitted analysis jobs for L=$L, J2=$J2, J1=$J1 (${#checkpoint_files[@]} checkpoints)"
  echo "---------------------------------------"
done

# 等待剩余任务
wait

# 整理结果
echo "Organizing results..."

# 记录磁盘使用情况
echo "Disk usage for results:"
du -sh results/

echo "Job finished at: $(date)"
