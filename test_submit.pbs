#!/bin/sh

#PBS -q gpu_v100
#PBS -l select=1:ncpus=4:ngpus=1
#PBS -l walltime=00:10:00
###PBS -P 12004256
###PBS -P hpc_psengupta_group
#PBS -N test-submit
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "测试作业开始时间: $(date)"
echo "运行节点: $(hostname)"
echo "工作目录: $(pwd)"

echo "==================== 系统信息检查 ===================="

# 检查GPU信息
echo "GPU信息:"
nvidia-smi

echo "==================== 模块加载测试 ===================="

# 加载必要的模块
echo "加载anaconda3/2023模块..."
module load anaconda3/2023
if [ $? -eq 0 ]; then
    echo "✅ anaconda3/2023 模块加载成功"
else
    echo "❌ anaconda3/2023 模块加载失败"
fi

echo "加载cuda/12.8模块..."
module load cuda/12.8
if [ $? -eq 0 ]; then
    echo "✅ cuda/12.8 模块加载成功"
else
    echo "❌ cuda/12.8 模块加载失败"
fi

echo "==================== Conda环境测试 ===================="

# 激活conda环境
echo "激活netket环境..."
source activate netket
if [ $? -eq 0 ]; then
    echo "✅ netket环境激活成功"
else
    echo "❌ netket环境激活失败"
fi

# 验证环境
echo "Python版本: $(python --version 2>&1)"
echo "Python路径: $(which python)"
echo "当前conda环境: $CONDA_DEFAULT_ENV"

echo "==================== Python包检查 ===================="

# 检查关键Python包
echo "检查netket包..."
python -c "import netket; print(f'✅ NetKet版本: {netket.__version__}')" 2>/dev/null || echo "❌ NetKet导入失败"

echo "检查jax包..."
python -c "import jax; print(f'✅ JAX版本: {jax.__version__}')" 2>/dev/null || echo "❌ JAX导入失败"

echo "检查numpy包..."
python -c "import numpy; print(f'✅ NumPy版本: {numpy.__version__}')" 2>/dev/null || echo "❌ NumPy导入失败"

echo "==================== GPU访问测试 ===================="

# 测试GPU访问
echo "测试GPU访问..."
python -c "
import jax
print(f'JAX设备: {jax.devices()}')
print(f'GPU数量: {jax.device_count()}')
print(f'本地设备数量: {jax.local_device_count()}')
if jax.device_count() > 0:
    print('✅ GPU访问正常')
else:
    print('❌ 没有检测到GPU')
" 2>/dev/null || echo "❌ GPU测试失败"

echo "==================== 文件系统测试 ===================="

# 检查脚本文件是否存在
if [ -f "scripts/train.py" ]; then
    echo "✅ scripts/train.py 文件存在"
else
    echo "❌ scripts/train.py 文件不存在"
fi

if [ -f "scripts/analyze.py" ]; then
    echo "✅ scripts/analyze.py 文件存在"
else
    echo "❌ scripts/analyze.py 文件不存在"
fi

# 检查写入权限
echo "检查results目录写入权限..."
mkdir -p results/test_write
if [ $? -eq 0 ]; then
    echo "✅ 目录写入权限正常"
    rmdir results/test_write 2>/dev/null
else
    echo "❌ 目录写入权限有问题"
fi

echo "==================== 测试完成 ===================="
echo "测试作业结束时间: $(date)"
echo "如果看到这条消息，说明PBS作业提交和执行基本正常！"
