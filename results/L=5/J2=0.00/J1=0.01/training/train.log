[2025-08-19 14:44:00] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-08-19 14:44:00]   - 迭代次数: final
[2025-08-19 14:44:00]   - 能量: -83.541487-0.000213j ± 0.114689
[2025-08-19 14:44:00]   - 时间戳: 2025-08-19T13:05:05.622620+08:00
[2025-08-19 14:44:08] ✓ 变分状态参数已从checkpoint恢复
[2025-08-19 14:44:08] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-19 14:44:08] ==================================================
[2025-08-19 14:44:08] GCNN for Shastry-Sutherland Model
[2025-08-19 14:44:08] ==================================================
[2025-08-19 14:44:08] System parameters:
[2025-08-19 14:44:08]   - System size: L=5, N=100
[2025-08-19 14:44:08]   - System parameters: J1=0.01, J2=0.0, Q=1.0
[2025-08-19 14:44:08] --------------------------------------------------
[2025-08-19 14:44:08] Model parameters:
[2025-08-19 14:44:08]   - Number of layers = 4
[2025-08-19 14:44:08]   - Number of features = 4
[2025-08-19 14:44:08]   - Total parameters = 19628
[2025-08-19 14:44:08] --------------------------------------------------
[2025-08-19 14:44:08] Training parameters:
[2025-08-19 14:44:08]   - Learning rate: 0.015
[2025-08-19 14:44:08]   - Total iterations: 1050
[2025-08-19 14:44:08]   - Annealing cycles: 3
[2025-08-19 14:44:08]   - Initial period: 150
[2025-08-19 14:44:08]   - Period multiplier: 2.0
[2025-08-19 14:44:08]   - Temperature range: 0.0-1.0
[2025-08-19 14:44:08]   - Samples: 4096
[2025-08-19 14:44:08]   - Discarded samples: 0
[2025-08-19 14:44:08]   - Chunk size: 2048
[2025-08-19 14:44:08]   - Diagonal shift: 0.2
[2025-08-19 14:44:08]   - Gradient clipping: 1.0
[2025-08-19 14:44:08]   - Checkpoint enabled: interval=100
[2025-08-19 14:44:08]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.01/training/checkpoints
[2025-08-19 14:44:08] --------------------------------------------------
[2025-08-19 14:44:08] Device status:
[2025-08-19 14:44:08]   - Devices model: A100
[2025-08-19 14:44:08]   - Number of devices: 1
[2025-08-19 14:44:08]   - Sharding: True
[2025-08-19 14:44:08] ============================================================
[2025-08-19 14:44:43] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -83.132063+0.092138j
[2025-08-19 14:45:04] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -83.093267+0.071762j
[2025-08-19 14:45:13] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -83.152186+0.013170j
[2025-08-19 14:45:23] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -83.198757+0.025265j
[2025-08-19 14:45:32] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -83.170516+0.062520j
[2025-08-19 14:45:41] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -83.111089+0.071735j
[2025-08-19 14:45:51] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -83.195480+0.061606j
[2025-08-19 14:46:00] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -83.072976+0.041652j
[2025-08-19 14:46:09] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -83.059116+0.024736j
[2025-08-19 14:46:19] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -83.208781+0.049360j
[2025-08-19 14:46:28] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -83.257786+0.025682j
[2025-08-19 14:46:37] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -83.247573+0.024832j
[2025-08-19 14:46:47] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -83.294680+0.006979j
[2025-08-19 14:46:56] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -83.145560+0.009672j
[2025-08-19 14:47:05] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -83.110500+0.029642j
[2025-08-19 14:47:15] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -83.190667-0.003501j
[2025-08-19 14:47:24] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -83.074566-0.024927j
[2025-08-19 14:47:33] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -83.036127+0.004685j
[2025-08-19 14:47:43] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -83.063783-0.044074j
[2025-08-19 14:47:52] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -83.074015-0.021094j
[2025-08-19 14:48:01] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -83.121034-0.008253j
[2025-08-19 14:48:11] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -83.138529-0.022886j
[2025-08-19 14:48:20] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -83.080848+0.019545j
[2025-08-19 14:48:29] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -83.159133-0.000687j
[2025-08-19 14:48:39] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -83.229142+0.014840j
[2025-08-19 14:48:48] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -83.185727-0.010772j
[2025-08-19 14:48:57] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -83.130369+0.046257j
[2025-08-19 14:49:07] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -83.146163+0.025456j
[2025-08-19 14:49:16] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -83.014482+0.059432j
[2025-08-19 14:49:25] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -83.122150+0.002124j
[2025-08-19 14:49:35] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -82.946679-0.005513j
[2025-08-19 14:49:44] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -83.025890+0.016017j
[2025-08-19 14:49:53] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -82.977770-0.011341j
[2025-08-19 14:50:03] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -82.896177+0.009146j
[2025-08-19 14:50:12] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -82.997515+0.010085j
[2025-08-19 14:50:21] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -82.888222-0.015786j
[2025-08-19 14:50:31] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -82.951237+0.027657j
[2025-08-19 14:50:40] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -82.852492-0.004432j
[2025-08-19 14:50:49] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -83.011013-0.042493j
[2025-08-19 14:50:59] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -83.103363-0.007999j
[2025-08-19 14:51:08] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -83.070811-0.012561j
[2025-08-19 14:51:17] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -83.221592+0.009721j
[2025-08-19 14:51:27] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -83.102568+0.008109j
[2025-08-19 14:51:36] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -83.153680+0.005196j
[2025-08-19 14:51:45] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -83.101829-0.024941j
[2025-08-19 14:51:55] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -83.118280+0.006029j
[2025-08-19 14:52:04] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -83.250798-0.001617j
[2025-08-19 14:52:13] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -83.185130-0.007289j
[2025-08-19 14:52:23] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -83.118813+0.007238j
[2025-08-19 14:52:32] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -83.207759-0.000935j
[2025-08-19 14:52:41] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -83.119490-0.000192j
[2025-08-19 14:52:51] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -83.142769-0.005698j
[2025-08-19 14:53:00] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -83.160217+0.009427j
[2025-08-19 14:53:09] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -83.254030-0.008612j
[2025-08-19 14:53:19] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -83.141233-0.023075j
[2025-08-19 14:53:28] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -83.168676-0.021747j
[2025-08-19 14:53:37] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -83.099559+0.015728j
[2025-08-19 14:53:47] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -83.097441-0.016574j
[2025-08-19 14:53:56] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -83.171472-0.005361j
[2025-08-19 14:54:05] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -83.128413+0.003302j
[2025-08-19 14:54:15] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -83.158177+0.000262j
[2025-08-19 14:54:24] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -83.145485+0.001921j
[2025-08-19 14:54:33] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -83.132843+0.010898j
[2025-08-19 14:54:43] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -83.161701-0.005493j
[2025-08-19 14:54:52] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -83.151364-0.012174j
[2025-08-19 14:55:01] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -83.041982+0.011454j
[2025-08-19 14:55:11] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -83.155159-0.003756j
[2025-08-19 14:55:20] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -83.113387-0.010523j
[2025-08-19 14:55:29] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -83.104251+0.009531j
[2025-08-19 14:55:39] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -83.038954+0.002429j
[2025-08-19 14:55:48] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -83.136874-0.010977j
[2025-08-19 14:55:57] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -83.127374+0.009519j
[2025-08-19 14:56:07] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -83.208636+0.013451j
[2025-08-19 14:56:16] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -83.250348-0.013098j
[2025-08-19 14:56:25] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -83.040634+0.007483j
[2025-08-19 14:56:35] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -83.197933-0.003491j
[2025-08-19 14:56:44] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -83.274771-0.000365j
[2025-08-19 14:56:53] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -83.214937-0.007355j
[2025-08-19 14:57:03] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -83.082417+0.020289j
[2025-08-19 14:57:12] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -83.007444-0.007972j
[2025-08-19 14:57:21] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -83.105433-0.000145j
[2025-08-19 14:57:31] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -83.183334+0.002919j
[2025-08-19 14:57:40] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -83.096485+0.011062j
[2025-08-19 14:57:49] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -83.077572+0.006171j
[2025-08-19 14:57:59] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -83.116667-0.007171j
[2025-08-19 14:58:08] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -83.129285+0.020201j
[2025-08-19 14:58:17] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -83.187078+0.024414j
[2025-08-19 14:58:27] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -83.140002-0.000416j
[2025-08-19 14:58:36] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -83.171026+0.004875j
[2025-08-19 14:58:46] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -83.194275-0.006587j
[2025-08-19 14:58:55] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -83.177192-0.029031j
[2025-08-19 14:59:04] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -83.252904-0.027914j
[2025-08-19 14:59:14] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -83.338147-0.028663j
[2025-08-19 14:59:23] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -83.368979-0.003556j
[2025-08-19 14:59:32] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -83.452135+0.011637j
[2025-08-19 14:59:42] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -83.418559-0.008039j
[2025-08-19 14:59:51] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -83.260553-0.012021j
[2025-08-19 15:00:00] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -83.224805+0.000474j
[2025-08-19 15:00:10] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -83.218100-0.017090j
[2025-08-19 15:00:19] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -83.162503-0.016461j
[2025-08-19 15:00:19] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-19 15:00:28] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -83.178060-0.008186j
[2025-08-19 15:00:38] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -83.204031-0.020063j
[2025-08-19 15:00:47] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -83.175281-0.023789j
[2025-08-19 15:00:56] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -83.147365-0.003774j
[2025-08-19 15:01:06] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -83.173723-0.006552j
[2025-08-19 15:01:15] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -82.998086-0.003024j
[2025-08-19 15:01:24] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -83.000956+0.010413j
[2025-08-19 15:01:33] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -83.044664-0.007426j
[2025-08-19 15:01:43] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -82.901214-0.001396j
[2025-08-19 15:01:52] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -82.909764-0.005484j
[2025-08-19 15:02:01] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -82.821602+0.021418j
[2025-08-19 15:02:11] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -83.041288-0.005039j
[2025-08-19 15:02:20] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -83.178588-0.009118j
[2025-08-19 15:02:29] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.236320-0.010372j
[2025-08-19 15:02:39] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -83.320402-0.023125j
[2025-08-19 15:02:48] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -83.130629-0.014409j
[2025-08-19 15:02:57] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -83.137100-0.021201j
[2025-08-19 15:03:07] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -83.227378-0.003781j
[2025-08-19 15:03:16] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -83.239985+0.006324j
[2025-08-19 15:03:25] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -83.248701+0.000645j
[2025-08-19 15:03:35] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -83.206039+0.006017j
[2025-08-19 15:03:44] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -83.097546-0.025567j
[2025-08-19 15:03:53] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -83.169290+0.000585j
[2025-08-19 15:04:03] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -83.183232-0.015240j
[2025-08-19 15:04:12] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -83.047377-0.010258j
[2025-08-19 15:04:21] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -83.155455+0.015567j
[2025-08-19 15:04:31] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -83.132775+0.009255j
[2025-08-19 15:04:40] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -83.131636-0.029457j
[2025-08-19 15:04:49] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -83.058714+0.025449j
[2025-08-19 15:04:59] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -83.058580+0.025797j
[2025-08-19 15:05:08] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -83.042160-0.023764j
[2025-08-19 15:05:17] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -83.137439-0.021021j
[2025-08-19 15:05:27] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -83.122121-0.014692j
[2025-08-19 15:05:36] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -83.058105-0.008867j
[2025-08-19 15:05:46] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -83.025528-0.050080j
[2025-08-19 15:05:55] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -83.124254-0.011157j
[2025-08-19 15:06:04] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -83.107584+0.018937j
[2025-08-19 15:06:14] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -83.192568+0.048308j
[2025-08-19 15:06:23] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -83.166177+0.006973j
[2025-08-19 15:06:32] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -83.094443-0.017204j
[2025-08-19 15:06:42] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -83.022444+0.066911j
[2025-08-19 15:06:51] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -82.981940+0.025127j
[2025-08-19 15:07:00] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -83.040789-0.002046j
[2025-08-19 15:07:10] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -83.165549+0.008206j
[2025-08-19 15:07:19] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -82.889272+0.017856j
[2025-08-19 15:07:28] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -82.930350-0.020890j
[2025-08-19 15:07:38] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -82.933912+0.016138j
[2025-08-19 15:07:47] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -82.980318-0.017838j
[2025-08-19 15:07:56] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -82.880035-0.029152j
[2025-08-19 15:08:06] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -82.817115-0.027780j
[2025-08-19 15:08:06] RESTART #1 | Period: 300
[2025-08-19 15:08:15] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -82.889001-0.035970j
[2025-08-19 15:08:24] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -82.847963+0.014583j
[2025-08-19 15:08:34] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -82.803756-0.002314j
[2025-08-19 15:08:43] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -82.969663+0.006833j
[2025-08-19 15:08:52] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -82.897430-0.033279j
[2025-08-19 15:09:01] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -83.050080+0.019263j
[2025-08-19 15:09:11] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -83.165275-0.002496j
[2025-08-19 15:09:20] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -83.329355+0.012538j
[2025-08-19 15:09:29] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -83.342551-0.004806j
[2025-08-19 15:09:39] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -83.420626+0.002938j
[2025-08-19 15:09:48] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -83.342443+0.028576j
[2025-08-19 15:09:57] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -83.398396-0.032339j
[2025-08-19 15:10:07] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -83.142056+0.018489j
[2025-08-19 15:10:16] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -83.159441+0.005733j
[2025-08-19 15:10:25] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -83.187829+0.008174j
[2025-08-19 15:10:35] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -83.122349+0.029439j
[2025-08-19 15:10:44] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -83.205669+0.018132j
[2025-08-19 15:10:54] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -83.145976+0.032625j
[2025-08-19 15:11:03] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -83.025624-0.039131j
[2025-08-19 15:11:12] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -83.169013+0.017827j
[2025-08-19 15:11:21] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -83.267834-0.020479j
[2025-08-19 15:11:31] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -83.160312-0.017700j
[2025-08-19 15:11:40] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -83.223195+0.006683j
[2025-08-19 15:11:49] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -83.300259-0.008405j
[2025-08-19 15:11:59] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -83.197430-0.001871j
[2025-08-19 15:12:08] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -83.495884+0.020279j
[2025-08-19 15:12:17] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -83.435158+0.004068j
[2025-08-19 15:12:27] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -83.414915-0.025611j
[2025-08-19 15:12:36] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -83.365471-0.020147j
[2025-08-19 15:12:46] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.312318+0.001745j
[2025-08-19 15:12:55] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -83.182721-0.016141j
[2025-08-19 15:13:04] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -83.220881-0.022951j
[2025-08-19 15:13:14] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -83.021095+0.009593j
[2025-08-19 15:13:23] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -83.192236+0.007825j
[2025-08-19 15:13:32] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -83.192174-0.032250j
[2025-08-19 15:13:42] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -83.136992+0.022687j
[2025-08-19 15:13:51] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -83.063135+0.007944j
[2025-08-19 15:14:00] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -83.140452+0.006932j
[2025-08-19 15:14:10] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -83.223019+0.021947j
[2025-08-19 15:14:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -83.276361+0.001675j
[2025-08-19 15:14:28] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -83.190377-0.023472j
[2025-08-19 15:14:38] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -83.093739+0.016681j
[2025-08-19 15:14:47] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -83.257901-0.013136j
[2025-08-19 15:14:56] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -83.214882-0.011743j
[2025-08-19 15:15:06] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -83.256102-0.014807j
[2025-08-19 15:15:15] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -83.316383+0.007000j
[2025-08-19 15:15:24] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -83.396342+0.041323j
[2025-08-19 15:15:33] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -83.444990+0.025875j
[2025-08-19 15:15:43] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -83.230083+0.007008j
[2025-08-19 15:15:52] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -83.161425-0.006991j
[2025-08-19 15:15:52] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-19 15:16:01] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -83.231896+0.052139j
[2025-08-19 15:16:11] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -83.267554+0.014344j
[2025-08-19 15:16:20] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -83.207360-0.048193j
[2025-08-19 15:16:29] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -83.272588+0.014584j
[2025-08-19 15:16:39] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -83.030584+0.015372j
[2025-08-19 15:16:48] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -83.173617+0.006162j
[2025-08-19 15:16:57] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -83.117651-0.002195j
[2025-08-19 15:17:07] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -83.049266-0.006688j
[2025-08-19 15:17:16] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -83.158294+0.007482j
[2025-08-19 15:17:25] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -83.152082+0.014463j
[2025-08-19 15:17:35] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -83.069417-0.011889j
[2025-08-19 15:17:44] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -82.864702+0.023753j
[2025-08-19 15:17:54] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -82.880535-0.012507j
[2025-08-19 15:18:03] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -82.994494+0.002244j
[2025-08-19 15:18:12] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -83.045715+0.033112j
[2025-08-19 15:18:22] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -83.015351+0.033451j
[2025-08-19 15:18:31] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -82.945571+0.035100j
[2025-08-19 15:18:40] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -82.997730+0.003750j
[2025-08-19 15:18:49] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -83.093226+0.008693j
[2025-08-19 15:18:59] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -83.217009-0.006841j
[2025-08-19 15:19:08] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -83.181830+0.010925j
[2025-08-19 15:19:17] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -83.098599+0.008469j
[2025-08-19 15:19:27] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -83.028358+0.014131j
[2025-08-19 15:19:36] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -83.046183+0.008801j
[2025-08-19 15:19:46] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -83.065461-0.006629j
[2025-08-19 15:19:55] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -83.199769+0.013958j
[2025-08-19 15:20:04] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -83.047248+0.007025j
[2025-08-19 15:20:14] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -83.093370-0.026649j
[2025-08-19 15:20:23] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -83.046168-0.019225j
[2025-08-19 15:20:32] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -83.094593+0.005280j
[2025-08-19 15:20:42] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -83.194501+0.000445j
[2025-08-19 15:20:51] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -83.200113-0.000573j
[2025-08-19 15:21:00] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -83.088797-0.000147j
[2025-08-19 15:21:10] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -83.064312+0.010535j
[2025-08-19 15:21:19] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -82.936140+0.011537j
[2025-08-19 15:21:28] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -83.019522+0.005950j
[2025-08-19 15:21:38] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -82.962812+0.008342j
[2025-08-19 15:21:47] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -83.007727-0.003855j
[2025-08-19 15:21:56] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -82.873466-0.013541j
[2025-08-19 15:22:06] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -83.112234-0.001982j
[2025-08-19 15:22:15] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -83.157241+0.021070j
[2025-08-19 15:22:24] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -83.181797-0.012620j
[2025-08-19 15:22:33] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -83.143572+0.002744j
[2025-08-19 15:22:43] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -83.133990+0.001430j
[2025-08-19 15:22:52] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -83.168095-0.027820j
[2025-08-19 15:23:01] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -83.302116+0.006971j
[2025-08-19 15:23:11] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -83.307105-0.004659j
[2025-08-19 15:23:20] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -83.301442-0.010765j
[2025-08-19 15:23:29] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -83.307266-0.028881j
[2025-08-19 15:23:39] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -83.302452-0.012190j
[2025-08-19 15:23:48] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -83.302800-0.028172j
[2025-08-19 15:23:57] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -83.154740-0.009274j
[2025-08-19 15:24:07] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -83.129392+0.025070j
[2025-08-19 15:24:16] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -83.120478-0.018845j
[2025-08-19 15:24:25] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -83.053460-0.001558j
[2025-08-19 15:24:35] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -83.066290-0.011223j
[2025-08-19 15:24:44] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -83.024997-0.026549j
[2025-08-19 15:24:53] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -83.058806-0.010940j
[2025-08-19 15:25:03] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -82.939414+0.026305j
[2025-08-19 15:25:12] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -82.996115+0.011293j
[2025-08-19 15:25:21] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -83.018081-0.041729j
[2025-08-19 15:25:31] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -83.151930-0.015889j
[2025-08-19 15:25:40] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -83.152462+0.006863j
[2025-08-19 15:25:49] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -83.117411+0.000801j
[2025-08-19 15:25:59] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -82.952463-0.010972j
[2025-08-19 15:26:08] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -83.264601+0.006044j
[2025-08-19 15:26:17] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -83.351272-0.056803j
[2025-08-19 15:26:27] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -83.264949+0.013092j
[2025-08-19 15:26:36] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -83.272186+0.000951j
[2025-08-19 15:26:45] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -83.200599-0.006089j
[2025-08-19 15:26:55] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -83.214520-0.004680j
[2025-08-19 15:27:04] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -83.098645-0.006374j
[2025-08-19 15:27:13] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -83.071317+0.003058j
[2025-08-19 15:27:23] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -83.153007+0.027801j
[2025-08-19 15:27:32] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -83.088924-0.022601j
[2025-08-19 15:27:41] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -83.110233+0.018725j
[2025-08-19 15:27:51] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -83.092128+0.004987j
[2025-08-19 15:28:00] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -83.126494-0.018697j
[2025-08-19 15:28:09] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -83.176242+0.012543j
[2025-08-19 15:28:19] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -83.166168-0.011242j
[2025-08-19 15:28:28] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -83.134543-0.006147j
[2025-08-19 15:28:37] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -83.163507+0.035272j
[2025-08-19 15:28:47] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -83.098235-0.020241j
[2025-08-19 15:28:56] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -82.899919-0.009727j
[2025-08-19 15:29:05] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -83.044041+0.011822j
[2025-08-19 15:29:15] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -83.064334+0.025349j
[2025-08-19 15:29:24] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -83.045526-0.005767j
[2025-08-19 15:29:33] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -83.070846-0.006071j
[2025-08-19 15:29:43] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -83.000531+0.020782j
[2025-08-19 15:29:52] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -83.067875+0.001943j
[2025-08-19 15:30:01] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -82.967423-0.008782j
[2025-08-19 15:30:11] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -83.181414+0.018472j
[2025-08-19 15:30:20] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -83.113754-0.027778j
[2025-08-19 15:30:29] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -83.114491-0.028866j
[2025-08-19 15:30:39] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -83.102770-0.025004j
[2025-08-19 15:30:48] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -83.210424-0.008184j
[2025-08-19 15:30:57] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -83.204995-0.039595j
[2025-08-19 15:31:07] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -83.049556-0.004961j
[2025-08-19 15:31:16] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -82.982549-0.048233j
[2025-08-19 15:31:25] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -82.967702-0.006803j
[2025-08-19 15:31:25] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-19 15:31:35] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -83.090557-0.007726j
[2025-08-19 15:31:44] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -83.085301-0.002238j
[2025-08-19 15:31:53] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -83.022541-0.004809j
[2025-08-19 15:32:03] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -83.220789-0.003573j
[2025-08-19 15:32:12] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -83.133818+0.019622j
[2025-08-19 15:32:21] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -83.187774-0.034761j
[2025-08-19 15:32:31] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -83.192761-0.026367j
[2025-08-19 15:32:40] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -83.298255+0.015276j
[2025-08-19 15:32:49] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -83.199272+0.012465j
[2025-08-19 15:32:59] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -83.225239+0.033912j
[2025-08-19 15:33:08] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -83.213741+0.010388j
[2025-08-19 15:33:17] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -83.342081+0.021444j
[2025-08-19 15:33:27] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -83.187232-0.011459j
[2025-08-19 15:33:36] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -83.169714-0.008595j
[2025-08-19 15:33:46] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -83.089587+0.031734j
[2025-08-19 15:33:55] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -83.046604-0.003982j
[2025-08-19 15:34:04] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -83.081758+0.003429j
[2025-08-19 15:34:14] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -83.223301+0.007771j
[2025-08-19 15:34:23] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.208557-0.013522j
[2025-08-19 15:34:32] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -83.123523-0.001548j
[2025-08-19 15:34:42] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -82.984047+0.029047j
[2025-08-19 15:34:51] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -82.945553+0.018162j
[2025-08-19 15:35:00] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -82.964225+0.015909j
[2025-08-19 15:35:10] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -83.083914+0.026363j
[2025-08-19 15:35:19] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -83.056182-0.009365j
[2025-08-19 15:35:28] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -83.102299+0.018339j
[2025-08-19 15:35:38] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -83.059306+0.011115j
[2025-08-19 15:35:47] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -83.039732+0.000306j
[2025-08-19 15:35:56] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -83.066213-0.001116j
[2025-08-19 15:36:06] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -82.967534-0.026912j
[2025-08-19 15:36:15] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -83.014501+0.005843j
[2025-08-19 15:36:24] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -83.035142-0.012898j
[2025-08-19 15:36:34] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -83.021114+0.013135j
[2025-08-19 15:36:43] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -83.143432+0.011193j
[2025-08-19 15:36:52] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -83.066524+0.006150j
[2025-08-19 15:37:02] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -82.941090+0.001036j
[2025-08-19 15:37:11] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -82.895206-0.011398j
[2025-08-19 15:37:20] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -83.009439-0.010707j
[2025-08-19 15:37:29] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -83.046266-0.002997j
[2025-08-19 15:37:39] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -82.888031+0.006493j
[2025-08-19 15:37:48] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -82.873340+0.015805j
[2025-08-19 15:37:57] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -82.986939-0.006170j
[2025-08-19 15:38:07] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -82.986901-0.007601j
[2025-08-19 15:38:16] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -83.021981+0.021953j
[2025-08-19 15:38:25] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -82.954992-0.015842j
[2025-08-19 15:38:35] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -83.054391-0.000304j
[2025-08-19 15:38:44] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -82.992367+0.019690j
[2025-08-19 15:38:54] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -82.828985-0.003522j
[2025-08-19 15:39:03] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -82.856842+0.023430j
[2025-08-19 15:39:12] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -82.923628-0.019247j
[2025-08-19 15:39:21] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -83.015734+0.032431j
[2025-08-19 15:39:31] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -82.979830+0.019194j
[2025-08-19 15:39:40] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -82.943180+0.012805j
[2025-08-19 15:39:49] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -83.111792+0.001772j
[2025-08-19 15:39:59] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -83.105935-0.016575j
[2025-08-19 15:40:08] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -83.208714+0.010384j
[2025-08-19 15:40:17] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -83.048156+0.031646j
[2025-08-19 15:40:27] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -83.044257+0.010829j
[2025-08-19 15:40:36] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -82.988123+0.011291j
[2025-08-19 15:40:46] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -82.892331+0.021022j
[2025-08-19 15:40:55] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -83.017249+0.006425j
[2025-08-19 15:41:04] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -83.190112+0.002445j
[2025-08-19 15:41:14] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -83.084870-0.033846j
[2025-08-19 15:41:23] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -83.222955-0.023523j
[2025-08-19 15:41:32] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -83.070890+0.003163j
[2025-08-19 15:41:42] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -83.181448-0.000353j
[2025-08-19 15:41:51] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -83.128831+0.001365j
[2025-08-19 15:42:00] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -83.124419-0.013941j
[2025-08-19 15:42:10] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -83.212032+0.008194j
[2025-08-19 15:42:19] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -83.180167+0.014706j
[2025-08-19 15:42:28] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -83.140394-0.025921j
[2025-08-19 15:42:38] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -83.057445+0.009339j
[2025-08-19 15:42:47] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -83.152674+0.012486j
[2025-08-19 15:42:56] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -83.092690+0.013394j
[2025-08-19 15:43:06] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -83.152838-0.043107j
[2025-08-19 15:43:15] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -83.222753-0.029108j
[2025-08-19 15:43:24] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -83.101319-0.055900j
[2025-08-19 15:43:34] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -82.919523-0.001962j
[2025-08-19 15:43:43] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -83.010852-0.042609j
[2025-08-19 15:43:52] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -82.921604+0.008866j
[2025-08-19 15:44:01] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -83.011492-0.020155j
[2025-08-19 15:44:11] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -83.028593+0.017943j
[2025-08-19 15:44:20] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -82.914806+0.052974j
[2025-08-19 15:44:29] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -83.024544-0.028420j
[2025-08-19 15:44:39] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -82.942387-0.055187j
[2025-08-19 15:44:48] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -82.982339-0.001115j
[2025-08-19 15:44:57] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -83.111000-0.007285j
[2025-08-19 15:45:07] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -83.188390-0.029247j
[2025-08-19 15:45:16] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -83.132530-0.009146j
[2025-08-19 15:45:25] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -83.205781+0.027397j
[2025-08-19 15:45:35] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -83.157510+0.000387j
[2025-08-19 15:45:44] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -83.394876-0.031985j
[2025-08-19 15:45:53] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -83.361749-0.020245j
[2025-08-19 15:46:03] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -83.222503+0.031802j
[2025-08-19 15:46:12] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -83.181509-0.020670j
[2025-08-19 15:46:21] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -83.110604+0.004873j
[2025-08-19 15:46:31] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -83.104343-0.041189j
[2025-08-19 15:46:40] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -83.028301-0.009379j
[2025-08-19 15:46:49] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -82.927198+0.052037j
[2025-08-19 15:46:59] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -83.128655+0.006943j
[2025-08-19 15:46:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-19 15:47:08] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -83.003946-0.007234j
[2025-08-19 15:47:17] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -83.123787-0.000997j
[2025-08-19 15:47:27] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -83.014706+0.005921j
[2025-08-19 15:47:36] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -83.057731-0.011752j
[2025-08-19 15:47:46] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -82.887585-0.011315j
[2025-08-19 15:47:55] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -82.953529-0.014215j
[2025-08-19 15:48:04] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -83.064212-0.006932j
[2025-08-19 15:48:14] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -82.953488-0.007289j
[2025-08-19 15:48:23] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -83.019401-0.035794j
[2025-08-19 15:48:32] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -83.174643-0.017014j
[2025-08-19 15:48:42] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -83.122721-0.021119j
[2025-08-19 15:48:51] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -83.020065-0.027939j
[2025-08-19 15:49:00] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -82.987336-0.016453j
[2025-08-19 15:49:10] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -83.056325-0.014846j
[2025-08-19 15:49:19] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -83.034818-0.046446j
[2025-08-19 15:49:28] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -83.037847+0.006777j
[2025-08-19 15:49:38] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -82.903850+0.001210j
[2025-08-19 15:49:47] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -82.926646+0.025569j
[2025-08-19 15:49:56] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -82.978834-0.004619j
[2025-08-19 15:50:06] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -83.003627-0.011506j
[2025-08-19 15:50:15] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -82.827355+0.025420j
[2025-08-19 15:50:24] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -82.960207-0.011150j
[2025-08-19 15:50:34] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -82.970573-0.015539j
[2025-08-19 15:50:43] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -83.063077+0.016663j
[2025-08-19 15:50:52] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -83.002484-0.026623j
[2025-08-19 15:51:02] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -82.947759-0.004084j
[2025-08-19 15:51:11] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -83.015574+0.004789j
[2025-08-19 15:51:20] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -82.979052+0.010577j
[2025-08-19 15:51:29] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -83.119360+0.005315j
[2025-08-19 15:51:39] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -82.927036+0.007270j
[2025-08-19 15:51:48] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -82.975877-0.003103j
[2025-08-19 15:51:57] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -82.979704+0.007871j
[2025-08-19 15:52:07] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -82.902667+0.011863j
[2025-08-19 15:52:16] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -82.900702+0.001657j
[2025-08-19 15:52:25] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -82.973090+0.009072j
[2025-08-19 15:52:35] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -82.979473+0.007939j
[2025-08-19 15:52:44] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -83.036312+0.000956j
[2025-08-19 15:52:54] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -83.045006+0.009913j
[2025-08-19 15:53:03] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -83.236472+0.017857j
[2025-08-19 15:53:12] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -83.098462-0.002353j
[2025-08-19 15:53:22] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -83.162094+0.023544j
[2025-08-19 15:53:31] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -83.101172+0.010888j
[2025-08-19 15:53:40] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -83.043049+0.028943j
[2025-08-19 15:53:50] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -83.042671+0.016315j
[2025-08-19 15:53:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -83.215855-0.009709j
[2025-08-19 15:54:08] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -83.272028+0.004566j
[2025-08-19 15:54:17] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -83.250693-0.001103j
[2025-08-19 15:54:27] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -83.074415-0.009539j
[2025-08-19 15:54:36] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -83.179225-0.008183j
[2025-08-19 15:54:46] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -83.262604+0.005595j
[2025-08-19 15:54:46] RESTART #2 | Period: 600
[2025-08-19 15:54:55] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -83.223133-0.012361j
[2025-08-19 15:55:04] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -83.334010-0.010594j
[2025-08-19 15:55:14] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -83.331157-0.013643j
[2025-08-19 15:55:23] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -83.344626-0.029067j
[2025-08-19 15:55:32] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -83.279259-0.016392j
[2025-08-19 15:55:42] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -83.231806-0.014172j
[2025-08-19 15:55:51] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -83.279883+0.007516j
[2025-08-19 15:56:00] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -83.164592-0.003303j
[2025-08-19 15:56:10] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -83.139939+0.013803j
[2025-08-19 15:56:19] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -83.177725+0.011738j
[2025-08-19 15:56:28] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -83.221814-0.014342j
[2025-08-19 15:56:38] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -83.211950-0.006103j
[2025-08-19 15:56:47] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.050034+0.002431j
[2025-08-19 15:56:56] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -83.029971+0.006664j
[2025-08-19 15:57:06] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -83.049468+0.010133j
[2025-08-19 15:57:15] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -83.099022+0.000346j
[2025-08-19 15:57:24] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -83.155704-0.023497j
[2025-08-19 15:57:34] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -83.153263-0.007111j
[2025-08-19 15:57:43] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -83.294221+0.000032j
[2025-08-19 15:57:52] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -83.121855-0.002663j
[2025-08-19 15:58:01] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -83.180353-0.014870j
[2025-08-19 15:58:11] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -83.239684+0.007426j
[2025-08-19 15:58:20] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -83.063801-0.011352j
[2025-08-19 15:58:29] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -83.094909+0.000852j
[2025-08-19 15:58:39] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -83.096690-0.001169j
[2025-08-19 15:58:48] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -83.046201+0.004135j
[2025-08-19 15:58:57] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -83.021634+0.010283j
[2025-08-19 15:59:07] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -83.023111-0.002405j
[2025-08-19 15:59:16] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -82.942889+0.001957j
[2025-08-19 15:59:25] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -83.063160-0.009774j
[2025-08-19 15:59:35] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -83.033739+0.009969j
[2025-08-19 15:59:44] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -83.060384+0.003176j
[2025-08-19 15:59:54] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -82.994295+0.020991j
[2025-08-19 16:00:03] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -82.998702+0.023536j
[2025-08-19 16:00:12] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -83.043668+0.003831j
[2025-08-19 16:00:21] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -82.873486-0.001954j
[2025-08-19 16:00:31] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -82.900156+0.006896j
[2025-08-19 16:00:40] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -82.906109+0.002062j
[2025-08-19 16:00:49] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -83.009210+0.000602j
[2025-08-19 16:00:59] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -82.921134-0.004751j
[2025-08-19 16:01:08] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -82.789857+0.004946j
[2025-08-19 16:01:17] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -82.929799-0.009403j
[2025-08-19 16:01:27] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -82.904619+0.012231j
[2025-08-19 16:01:36] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -82.827770+0.009093j
[2025-08-19 16:01:46] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -82.922510+0.012084j
[2025-08-19 16:01:55] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -82.937986+0.014139j
[2025-08-19 16:02:04] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -83.031667+0.013573j
[2025-08-19 16:02:14] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -83.105352+0.000916j
[2025-08-19 16:02:23] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -82.886508+0.001819j
[2025-08-19 16:02:32] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -82.976092+0.018338j
[2025-08-19 16:02:32] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-19 16:02:42] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -82.888485+0.004697j
[2025-08-19 16:02:51] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -83.013775+0.025330j
[2025-08-19 16:03:00] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -83.019253+0.013490j
[2025-08-19 16:03:10] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -82.993708+0.002221j
[2025-08-19 16:03:19] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -82.973616+0.005087j
[2025-08-19 16:03:28] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -82.906727+0.008396j
[2025-08-19 16:03:38] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -82.919242+0.003918j
[2025-08-19 16:03:47] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -82.954670-0.025623j
[2025-08-19 16:03:56] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -82.928817+0.003896j
[2025-08-19 16:04:06] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -83.067309-0.004379j
[2025-08-19 16:04:15] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -82.997902-0.009501j
[2025-08-19 16:04:24] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -83.036760+0.003252j
[2025-08-19 16:04:34] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -83.158297+0.009065j
[2025-08-19 16:04:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -83.123727-0.005230j
[2025-08-19 16:04:52] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -83.094730-0.002808j
[2025-08-19 16:05:02] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -83.070873-0.015587j
[2025-08-19 16:05:11] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -83.010904+0.000293j
[2025-08-19 16:05:20] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -83.081303+0.012579j
[2025-08-19 16:05:29] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -83.261051-0.011826j
[2025-08-19 16:05:39] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -83.236713+0.010778j
[2025-08-19 16:05:48] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -83.267430-0.002292j
[2025-08-19 16:05:57] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -83.253891-0.017376j
[2025-08-19 16:06:07] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -83.202659+0.013275j
[2025-08-19 16:06:16] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -83.123697+0.017305j
[2025-08-19 16:06:25] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -83.098677+0.009212j
[2025-08-19 16:06:35] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -83.078372+0.000813j
[2025-08-19 16:06:44] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -83.118293-0.002567j
[2025-08-19 16:06:54] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -83.094535-0.013785j
[2025-08-19 16:07:03] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -83.047373+0.014016j
[2025-08-19 16:07:12] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -82.998194+0.008434j
[2025-08-19 16:07:22] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -83.195941-0.005814j
[2025-08-19 16:07:31] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -83.140706+0.027598j
[2025-08-19 16:07:40] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -83.115807-0.001471j
[2025-08-19 16:07:49] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -83.132692+0.018353j
[2025-08-19 16:07:59] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -83.161977+0.002871j
[2025-08-19 16:08:08] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -83.025399+0.026369j
[2025-08-19 16:08:17] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -83.180025+0.006933j
[2025-08-19 16:08:27] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -83.032670-0.021888j
[2025-08-19 16:08:36] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -83.026567+0.009492j
[2025-08-19 16:08:46] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -83.036289+0.008779j
[2025-08-19 16:08:55] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -82.888911+0.005090j
[2025-08-19 16:09:04] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -82.911317+0.009768j
[2025-08-19 16:09:14] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -83.010415+0.001078j
[2025-08-19 16:09:23] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -83.000713-0.006483j
[2025-08-19 16:09:32] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -83.053569+0.000763j
[2025-08-19 16:09:42] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -83.045668-0.003307j
[2025-08-19 16:09:51] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -82.944794-0.016154j
[2025-08-19 16:10:00] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -82.895295-0.017026j
[2025-08-19 16:10:10] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -83.088754-0.006404j
[2025-08-19 16:10:19] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -83.085819-0.001825j
[2025-08-19 16:10:28] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -83.037981+0.000694j
[2025-08-19 16:10:38] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -83.139007-0.001533j
[2025-08-19 16:10:47] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -83.117708-0.010270j
[2025-08-19 16:10:56] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -83.042205-0.023330j
[2025-08-19 16:11:06] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -82.980804-0.014845j
[2025-08-19 16:11:15] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -83.169357+0.012735j
[2025-08-19 16:11:24] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -83.180245-0.005278j
[2025-08-19 16:11:34] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -83.119405-0.012301j
[2025-08-19 16:11:43] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -83.029752-0.011347j
[2025-08-19 16:11:52] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -83.125513-0.011813j
[2025-08-19 16:12:01] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -83.015861-0.003835j
[2025-08-19 16:12:11] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -83.050638+0.000109j
[2025-08-19 16:12:20] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -83.023222-0.007622j
[2025-08-19 16:12:29] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -83.173517+0.000844j
[2025-08-19 16:12:39] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -83.111073-0.002869j
[2025-08-19 16:12:48] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -83.061805-0.010021j
[2025-08-19 16:12:57] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -83.193353-0.016589j
[2025-08-19 16:13:07] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -83.089517-0.011197j
[2025-08-19 16:13:16] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -83.201361-0.013037j
[2025-08-19 16:13:25] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -83.153015-0.003445j
[2025-08-19 16:13:35] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -83.151468-0.005030j
[2025-08-19 16:13:44] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -83.188758-0.019597j
[2025-08-19 16:13:54] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -83.294836-0.003228j
[2025-08-19 16:14:03] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -83.175805-0.002076j
[2025-08-19 16:14:12] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -83.349643+0.002056j
[2025-08-19 16:14:22] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -83.307428-0.002526j
[2025-08-19 16:14:31] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -83.195124+0.002936j
[2025-08-19 16:14:40] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -83.313203-0.009003j
[2025-08-19 16:14:49] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -83.329375-0.006848j
[2025-08-19 16:14:59] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -83.214125-0.009279j
[2025-08-19 16:15:08] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -83.119714+0.008807j
[2025-08-19 16:15:17] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -83.168549+0.014266j
[2025-08-19 16:15:27] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -83.130630-0.005056j
[2025-08-19 16:15:36] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -83.186040-0.000560j
[2025-08-19 16:15:46] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -83.122156+0.003173j
[2025-08-19 16:15:55] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -83.044027+0.001301j
[2025-08-19 16:16:04] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -83.148477-0.010941j
[2025-08-19 16:16:14] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -83.079693+0.006291j
[2025-08-19 16:16:23] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -83.165067+0.002686j
[2025-08-19 16:16:32] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -83.108214+0.002789j
[2025-08-19 16:16:42] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -83.039727-0.007891j
[2025-08-19 16:16:51] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -83.119624-0.010312j
[2025-08-19 16:17:00] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -83.230300+0.006002j
[2025-08-19 16:17:10] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -83.283741-0.016635j
[2025-08-19 16:17:19] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -83.282743-0.002474j
[2025-08-19 16:17:28] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -83.207366-0.013038j
[2025-08-19 16:17:38] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -83.335296-0.017048j
[2025-08-19 16:17:47] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -83.289481+0.004390j
[2025-08-19 16:17:56] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -83.352696+0.008360j
[2025-08-19 16:18:06] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -83.257344+0.009113j
[2025-08-19 16:18:06] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-19 16:18:15] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -83.320955+0.012156j
[2025-08-19 16:18:24] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -83.234246+0.013566j
[2025-08-19 16:18:34] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -83.188997-0.028032j
[2025-08-19 16:18:43] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -83.269864+0.021498j
[2025-08-19 16:18:52] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -83.166721+0.013538j
[2025-08-19 16:19:02] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -83.153860+0.018331j
[2025-08-19 16:19:11] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -83.103358+0.024634j
[2025-08-19 16:19:20] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -83.002232+0.001136j
[2025-08-19 16:19:29] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -83.141898-0.021528j
[2025-08-19 16:19:39] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -83.180480+0.004156j
[2025-08-19 16:19:48] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -83.045140+0.049703j
[2025-08-19 16:19:57] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -83.217878+0.024198j
[2025-08-19 16:20:07] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -83.203800+0.026170j
[2025-08-19 16:20:16] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -83.254724-0.008846j
[2025-08-19 16:20:25] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -83.173630+0.012365j
[2025-08-19 16:20:35] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -83.343399-0.018982j
[2025-08-19 16:20:44] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -83.353862-0.012910j
[2025-08-19 16:20:54] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -83.358173+0.024543j
[2025-08-19 16:21:03] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -83.301096-0.015935j
[2025-08-19 16:21:12] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -83.442492+0.040152j
[2025-08-19 16:21:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -83.398111+0.001884j
[2025-08-19 16:21:31] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -83.313906+0.014455j
[2025-08-19 16:21:40] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -83.117433+0.043745j
[2025-08-19 16:21:49] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -83.324414+0.021265j
[2025-08-19 16:21:59] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -83.208600+0.047203j
[2025-08-19 16:22:08] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -83.438526+0.022132j
[2025-08-19 16:22:17] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -83.234486+0.019946j
[2025-08-19 16:22:27] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -83.112130+0.021958j
[2025-08-19 16:22:36] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -83.204721+0.039707j
[2025-08-19 16:22:46] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -83.022752-0.014174j
[2025-08-19 16:22:55] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -83.074802+0.018259j
[2025-08-19 16:23:04] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -83.252542-0.002338j
[2025-08-19 16:23:14] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -83.182408+0.018713j
[2025-08-19 16:23:23] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -83.129191-0.022295j
[2025-08-19 16:23:32] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -83.197929+0.007957j
[2025-08-19 16:23:42] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -83.220054-0.008283j
[2025-08-19 16:23:51] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -83.285270-0.001873j
[2025-08-19 16:24:00] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -83.334389+0.001374j
[2025-08-19 16:24:10] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -83.310287+0.030678j
[2025-08-19 16:24:19] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -83.302197-0.006161j
[2025-08-19 16:24:28] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -83.089015+0.062560j
[2025-08-19 16:24:38] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -83.160393-0.015281j
[2025-08-19 16:24:47] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -83.082976+0.015742j
[2025-08-19 16:24:56] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -83.116960+0.007424j
[2025-08-19 16:25:06] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -83.056477-0.013762j
[2025-08-19 16:25:15] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -83.080092-0.012119j
[2025-08-19 16:25:24] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -83.045881-0.003923j
[2025-08-19 16:25:34] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -83.054463-0.026006j
[2025-08-19 16:25:43] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -83.007809-0.014332j
[2025-08-19 16:25:52] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -83.126991-0.025431j
[2025-08-19 16:26:01] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -83.214072-0.015081j
[2025-08-19 16:26:11] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -83.249415-0.023436j
[2025-08-19 16:26:20] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -83.225126-0.009948j
[2025-08-19 16:26:29] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -83.183725-0.008660j
[2025-08-19 16:26:39] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -83.170691-0.010602j
[2025-08-19 16:26:48] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -83.181176-0.011854j
[2025-08-19 16:26:57] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -83.242383-0.009270j
[2025-08-19 16:27:07] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -83.151595+0.009881j
[2025-08-19 16:27:16] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -83.083315+0.021617j
[2025-08-19 16:27:25] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -83.069150+0.005701j
[2025-08-19 16:27:35] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -82.979597+0.009895j
[2025-08-19 16:27:44] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -83.014209+0.002127j
[2025-08-19 16:27:54] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -83.004364-0.000367j
[2025-08-19 16:28:03] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -83.142975+0.009206j
[2025-08-19 16:28:12] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -83.243784+0.021728j
[2025-08-19 16:28:21] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -83.222387+0.008477j
[2025-08-19 16:28:31] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -83.318552+0.012448j
[2025-08-19 16:28:40] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -83.312643+0.015570j
[2025-08-19 16:28:49] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -83.222573+0.011938j
[2025-08-19 16:28:59] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -83.213111+0.012461j
[2025-08-19 16:29:08] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -83.352453+0.010466j
[2025-08-19 16:29:17] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -83.270615+0.015546j
[2025-08-19 16:29:27] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -83.092939+0.011207j
[2025-08-19 16:29:36] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -83.080966+0.002800j
[2025-08-19 16:29:46] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -83.234501+0.007878j
[2025-08-19 16:29:55] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -83.254935-0.000610j
[2025-08-19 16:30:04] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -83.105474+0.007868j
[2025-08-19 16:30:14] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -83.213417+0.000122j
[2025-08-19 16:30:23] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -83.125976+0.007185j
[2025-08-19 16:30:32] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -83.228504-0.006652j
[2025-08-19 16:30:42] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -83.205934-0.002244j
[2025-08-19 16:30:51] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -83.135062+0.005060j
[2025-08-19 16:31:00] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -83.197755+0.002355j
[2025-08-19 16:31:10] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -83.303393+0.001640j
[2025-08-19 16:31:19] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -83.280560-0.003342j
[2025-08-19 16:31:28] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -83.223026-0.001101j
[2025-08-19 16:31:38] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -83.340942+0.009586j
[2025-08-19 16:31:47] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -83.143555-0.006055j
[2025-08-19 16:31:56] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -83.291648-0.014263j
[2025-08-19 16:32:06] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -83.203615+0.000599j
[2025-08-19 16:32:15] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -83.308287+0.013749j
[2025-08-19 16:32:24] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -83.265173+0.005994j
[2025-08-19 16:32:34] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -83.358638+0.015943j
[2025-08-19 16:32:43] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -83.233661-0.001019j
[2025-08-19 16:32:52] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -83.170250+0.011410j
[2025-08-19 16:33:01] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -83.086401-0.001171j
[2025-08-19 16:33:11] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -83.227928+0.004746j
[2025-08-19 16:33:20] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -83.162606+0.012597j
[2025-08-19 16:33:29] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -83.189220+0.019397j
[2025-08-19 16:33:39] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -83.150640-0.006640j
[2025-08-19 16:33:39] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-19 16:33:48] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -83.156229+0.020271j
[2025-08-19 16:33:57] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -83.178497-0.008169j
[2025-08-19 16:34:07] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -83.232630+0.009340j
[2025-08-19 16:34:16] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -83.237517-0.003422j
[2025-08-19 16:34:25] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -83.276964+0.005658j
[2025-08-19 16:34:35] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -83.335670-0.021768j
[2025-08-19 16:34:44] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -83.350886-0.017517j
[2025-08-19 16:34:54] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -83.300110-0.005506j
[2025-08-19 16:35:03] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -83.391399-0.003020j
[2025-08-19 16:35:12] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -83.420264-0.009355j
[2025-08-19 16:35:22] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -83.338728-0.000155j
[2025-08-19 16:35:31] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -83.343952-0.001317j
[2025-08-19 16:35:40] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -83.288038+0.003533j
[2025-08-19 16:35:49] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -83.243220+0.019044j
[2025-08-19 16:35:59] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -83.190739+0.014949j
[2025-08-19 16:36:08] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -83.263666+0.021808j
[2025-08-19 16:36:17] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -83.224483-0.017324j
[2025-08-19 16:36:27] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -83.222600-0.000320j
[2025-08-19 16:36:36] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -83.267353+0.007307j
[2025-08-19 16:36:46] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -83.293182-0.003308j
[2025-08-19 16:36:55] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -83.245105+0.009052j
[2025-08-19 16:37:04] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -83.120552+0.010051j
[2025-08-19 16:37:13] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -83.119291+0.001424j
[2025-08-19 16:37:23] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -83.120288-0.001893j
[2025-08-19 16:37:32] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -82.999995+0.007340j
[2025-08-19 16:37:41] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -83.143076-0.015995j
[2025-08-19 16:37:51] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -83.117771-0.010950j
[2025-08-19 16:38:00] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -83.350058+0.000338j
[2025-08-19 16:38:09] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -83.200033-0.020975j
[2025-08-19 16:38:19] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -83.206901-0.013663j
[2025-08-19 16:38:28] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -83.269904+0.000323j
[2025-08-19 16:38:37] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -83.294687+0.000233j
[2025-08-19 16:38:47] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -83.176260+0.002159j
[2025-08-19 16:38:56] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -83.350297+0.012961j
[2025-08-19 16:39:05] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -83.489651-0.003373j
[2025-08-19 16:39:15] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -83.390646-0.002322j
[2025-08-19 16:39:24] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -83.275438-0.011767j
[2025-08-19 16:39:33] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -83.297544-0.009219j
[2025-08-19 16:39:43] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -83.207681+0.030125j
[2025-08-19 16:39:52] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -83.175287-0.008039j
[2025-08-19 16:40:01] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -83.197558-0.014316j
[2025-08-19 16:40:11] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -83.193303+0.006490j
[2025-08-19 16:40:20] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -83.189739+0.010380j
[2025-08-19 16:40:29] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -83.214348-0.007113j
[2025-08-19 16:40:39] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -83.188980-0.010757j
[2025-08-19 16:40:48] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -83.250643-0.005115j
[2025-08-19 16:40:57] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -83.328969+0.019591j
[2025-08-19 16:41:07] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -83.120139-0.011936j
[2025-08-19 16:41:16] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -83.187229+0.002073j
[2025-08-19 16:41:25] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -83.155939+0.005949j
[2025-08-19 16:41:35] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -83.087043+0.006968j
[2025-08-19 16:41:44] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -83.271162+0.007395j
[2025-08-19 16:41:53] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -83.230962-0.018234j
[2025-08-19 16:42:03] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -83.193941-0.016323j
[2025-08-19 16:42:12] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -83.137621-0.010092j
[2025-08-19 16:42:21] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -83.173943-0.007831j
[2025-08-19 16:42:31] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -83.133577-0.005930j
[2025-08-19 16:42:40] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -83.129642-0.001660j
[2025-08-19 16:42:49] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -83.162214+0.005625j
[2025-08-19 16:42:59] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -83.175115-0.001071j
[2025-08-19 16:43:08] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -83.119833-0.002793j
[2025-08-19 16:43:17] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -83.152916-0.017619j
[2025-08-19 16:43:27] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -83.272062+0.001467j
[2025-08-19 16:43:36] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -83.277637+0.015688j
[2025-08-19 16:43:45] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -83.209151+0.003888j
[2025-08-19 16:43:55] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -83.017306+0.006310j
[2025-08-19 16:44:04] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -83.063474+0.006288j
[2025-08-19 16:44:13] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -82.982331+0.013269j
[2025-08-19 16:44:23] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -83.009359+0.003959j
[2025-08-19 16:44:32] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -83.032854+0.021099j
[2025-08-19 16:44:41] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -83.038029+0.002531j
[2025-08-19 16:44:51] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -83.034575+0.002381j
[2025-08-19 16:45:00] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -83.086740-0.011250j
[2025-08-19 16:45:09] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -83.119758-0.006032j
[2025-08-19 16:45:19] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -83.077262+0.006843j
[2025-08-19 16:45:28] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -83.161569-0.009285j
[2025-08-19 16:45:37] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -83.114953+0.004627j
[2025-08-19 16:45:47] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -83.114025-0.007220j
[2025-08-19 16:45:56] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -83.196014+0.011854j
[2025-08-19 16:46:05] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -83.103907-0.007978j
[2025-08-19 16:46:15] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -83.027596+0.007646j
[2025-08-19 16:46:24] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -82.957016-0.005913j
[2025-08-19 16:46:33] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -83.011317-0.008311j
[2025-08-19 16:46:43] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -83.081068+0.005953j
[2025-08-19 16:46:52] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -83.082126-0.003133j
[2025-08-19 16:47:01] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -83.130751-0.011566j
[2025-08-19 16:47:11] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -83.204004-0.010426j
[2025-08-19 16:47:20] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -83.106268-0.008197j
[2025-08-19 16:47:29] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -83.153379-0.008570j
[2025-08-19 16:47:39] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -83.146624-0.007145j
[2025-08-19 16:47:48] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -83.122318-0.003957j
[2025-08-19 16:47:57] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -83.200259-0.001392j
[2025-08-19 16:48:07] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -83.090280-0.007731j
[2025-08-19 16:48:16] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -83.067008-0.004832j
[2025-08-19 16:48:25] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -83.065318+0.005339j
[2025-08-19 16:48:35] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -83.182432+0.001447j
[2025-08-19 16:48:44] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -83.230112+0.004821j
[2025-08-19 16:48:53] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -83.199458+0.001054j
[2025-08-19 16:49:03] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -83.044392-0.003713j
[2025-08-19 16:49:12] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -83.089161+0.010294j
[2025-08-19 16:49:12] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-19 16:49:21] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -83.112008+0.002215j
[2025-08-19 16:49:31] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -83.113127+0.004253j
[2025-08-19 16:49:40] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -83.040233+0.003909j
[2025-08-19 16:49:49] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -83.090638+0.012938j
[2025-08-19 16:49:59] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -83.115160-0.001074j
[2025-08-19 16:50:08] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -83.010411+0.002792j
[2025-08-19 16:50:17] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -83.173731+0.006410j
[2025-08-19 16:50:27] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -83.074849-0.015394j
[2025-08-19 16:50:36] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -83.020350-0.002905j
[2025-08-19 16:50:45] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -83.087344-0.001765j
[2025-08-19 16:50:55] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -83.079520-0.006118j
[2025-08-19 16:51:04] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -83.183512-0.000433j
[2025-08-19 16:51:13] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -82.999928+0.002141j
[2025-08-19 16:51:23] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -83.072032+0.009192j
[2025-08-19 16:51:32] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -83.066735-0.004664j
[2025-08-19 16:51:41] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -83.134528-0.007220j
[2025-08-19 16:51:51] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -83.073961+0.005756j
[2025-08-19 16:52:00] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -83.178200-0.017331j
[2025-08-19 16:52:09] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -83.197876-0.011068j
[2025-08-19 16:52:19] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -83.228783+0.006882j
[2025-08-19 16:52:28] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -83.242634+0.004719j
[2025-08-19 16:52:37] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -83.236101+0.001977j
[2025-08-19 16:52:47] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -83.161681+0.002339j
[2025-08-19 16:52:56] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -83.046415-0.007985j
[2025-08-19 16:53:05] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -83.137398+0.002067j
[2025-08-19 16:53:15] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -83.125062+0.006848j
[2025-08-19 16:53:24] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -83.242003+0.002349j
[2025-08-19 16:53:33] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -83.145783-0.013754j
[2025-08-19 16:53:43] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -83.123148-0.002077j
[2025-08-19 16:53:52] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -83.138383-0.003369j
[2025-08-19 16:54:01] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -82.973605-0.012012j
[2025-08-19 16:54:11] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -82.984547-0.009877j
[2025-08-19 16:54:20] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -83.004739-0.005904j
[2025-08-19 16:54:29] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -82.959198-0.031687j
[2025-08-19 16:54:39] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -82.787483-0.003932j
[2025-08-19 16:54:48] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -83.004381+0.004234j
[2025-08-19 16:54:57] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -82.994781+0.005749j
[2025-08-19 16:55:07] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -83.017004+0.010335j
[2025-08-19 16:55:16] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -83.094232+0.005094j
[2025-08-19 16:55:25] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -83.008424-0.008786j
[2025-08-19 16:55:35] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -83.129104-0.000811j
[2025-08-19 16:55:44] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -83.128811+0.015257j
[2025-08-19 16:55:53] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -83.170824-0.001876j
[2025-08-19 16:56:03] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -83.250093-0.014869j
[2025-08-19 16:56:12] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -83.301603-0.007700j
[2025-08-19 16:56:21] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -83.267980-0.013780j
[2025-08-19 16:56:31] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -83.164053+0.000005j
[2025-08-19 16:56:40] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -83.129176-0.005009j
[2025-08-19 16:56:49] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -83.158077-0.009834j
[2025-08-19 16:56:59] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -83.171666-0.012992j
[2025-08-19 16:57:08] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -83.261343+0.013558j
[2025-08-19 16:57:17] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -83.171319+0.005565j
[2025-08-19 16:57:27] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -83.406633+0.008099j
[2025-08-19 16:57:36] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -83.178420+0.004402j
[2025-08-19 16:57:45] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -83.165661+0.003407j
[2025-08-19 16:57:55] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -83.173862-0.006318j
[2025-08-19 16:58:04] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -83.047492-0.009902j
[2025-08-19 16:58:13] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -83.156847+0.011440j
[2025-08-19 16:58:23] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -83.200394+0.002788j
[2025-08-19 16:58:32] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -83.245820+0.001208j
[2025-08-19 16:58:41] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -83.238428-0.010151j
[2025-08-19 16:58:51] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -83.191341+0.000658j
[2025-08-19 16:59:00] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -83.110864+0.020585j
[2025-08-19 16:59:09] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -83.128935+0.009961j
[2025-08-19 16:59:19] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -83.106776+0.004568j
[2025-08-19 16:59:28] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -83.204083-0.000500j
[2025-08-19 16:59:37] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -83.219246+0.000152j
[2025-08-19 16:59:47] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -83.168061+0.022806j
[2025-08-19 16:59:56] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -83.052975+0.018337j
[2025-08-19 17:00:05] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -83.146717+0.005215j
[2025-08-19 17:00:15] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -83.058233-0.022720j
[2025-08-19 17:00:24] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -82.919980-0.008581j
[2025-08-19 17:00:33] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -83.092598+0.005109j
[2025-08-19 17:00:43] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -83.205444+0.005825j
[2025-08-19 17:00:52] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -83.009895-0.000360j
[2025-08-19 17:01:01] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -83.074761+0.003298j
[2025-08-19 17:01:11] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -83.014570+0.000126j
[2025-08-19 17:01:20] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -82.956604+0.014593j
[2025-08-19 17:01:29] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -83.160669+0.019024j
[2025-08-19 17:01:39] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -83.194685-0.003200j
[2025-08-19 17:01:48] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -83.298411+0.003622j
[2025-08-19 17:01:57] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -83.324893+0.001906j
[2025-08-19 17:02:07] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -83.241750+0.012295j
[2025-08-19 17:02:16] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -83.185332+0.006537j
[2025-08-19 17:02:25] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -83.226772-0.013056j
[2025-08-19 17:02:35] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -83.180860+0.004498j
[2025-08-19 17:02:44] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -83.344725+0.001649j
[2025-08-19 17:02:53] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -83.247968+0.019840j
[2025-08-19 17:03:03] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -83.078076+0.021404j
[2025-08-19 17:03:12] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -83.198052+0.012176j
[2025-08-19 17:03:21] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -83.168942+0.024064j
[2025-08-19 17:03:31] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -83.031442+0.004694j
[2025-08-19 17:03:40] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -83.159250+0.013298j
[2025-08-19 17:03:49] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -83.255791+0.011301j
[2025-08-19 17:03:59] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -83.252054-0.005364j
[2025-08-19 17:04:08] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -83.241830+0.007926j
[2025-08-19 17:04:17] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -83.176364-0.000779j
[2025-08-19 17:04:27] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -83.285192+0.009045j
[2025-08-19 17:04:36] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -83.282854+0.011782j
[2025-08-19 17:04:46] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -83.224589-0.003256j
[2025-08-19 17:04:46] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-19 17:04:55] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -83.241139-0.005557j
[2025-08-19 17:05:04] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -83.324266+0.019812j
[2025-08-19 17:05:14] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -83.218415+0.004212j
[2025-08-19 17:05:23] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -83.247450-0.001971j
[2025-08-19 17:05:32] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -83.193170-0.001667j
[2025-08-19 17:05:42] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -83.251880+0.002387j
[2025-08-19 17:05:51] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -83.223087+0.007091j
[2025-08-19 17:06:00] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -83.259576+0.000614j
[2025-08-19 17:06:10] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -83.235318+0.017916j
[2025-08-19 17:06:19] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -83.149764-0.000805j
[2025-08-19 17:06:28] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -83.118319-0.010387j
[2025-08-19 17:06:38] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -83.160095+0.009449j
[2025-08-19 17:06:47] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -83.169586-0.002327j
[2025-08-19 17:06:56] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -83.265942-0.002665j
[2025-08-19 17:07:06] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -83.400608+0.011822j
[2025-08-19 17:07:15] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -83.315567-0.002981j
[2025-08-19 17:07:24] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -83.220660-0.005376j
[2025-08-19 17:07:34] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -83.175499-0.003346j
[2025-08-19 17:07:43] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -83.178431-0.009140j
[2025-08-19 17:07:52] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -83.219647+0.000661j
[2025-08-19 17:08:02] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -83.283682-0.003948j
[2025-08-19 17:08:11] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -83.287218-0.005115j
[2025-08-19 17:08:20] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -83.222754+0.002029j
[2025-08-19 17:08:29] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -83.168565-0.002428j
[2025-08-19 17:08:39] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -83.194286-0.004722j
[2025-08-19 17:08:48] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -83.199882+0.008333j
[2025-08-19 17:08:57] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -83.328794-0.005627j
[2025-08-19 17:09:07] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -83.311249+0.000339j
[2025-08-19 17:09:16] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -83.207427+0.019035j
[2025-08-19 17:09:25] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -83.273196+0.008244j
[2025-08-19 17:09:35] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -83.248482-0.005505j
[2025-08-19 17:09:44] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -83.303596+0.000670j
[2025-08-19 17:09:54] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -83.241668+0.004117j
[2025-08-19 17:10:03] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -83.263391+0.009530j
[2025-08-19 17:10:12] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -83.072923+0.009513j
[2025-08-19 17:10:22] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -83.218079+0.009193j
[2025-08-19 17:10:31] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -83.085418-0.000040j
[2025-08-19 17:10:40] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -83.182909+0.000451j
[2025-08-19 17:10:49] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -83.234045-0.013216j
[2025-08-19 17:10:59] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -83.275558-0.014248j
[2025-08-19 17:11:08] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -83.386534+0.011896j
[2025-08-19 17:11:17] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -83.250643+0.010644j
[2025-08-19 17:11:27] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -83.328470+0.011226j
[2025-08-19 17:11:36] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -83.473822-0.007029j
[2025-08-19 17:11:46] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -83.402432+0.003188j
[2025-08-19 17:11:55] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -83.354678-0.011870j
[2025-08-19 17:12:04] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -83.444406-0.014269j
[2025-08-19 17:12:14] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -83.490599+0.001077j
[2025-08-19 17:12:23] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -83.472434-0.011666j
[2025-08-19 17:12:32] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -83.443960-0.010500j
[2025-08-19 17:12:42] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -83.471268+0.028170j
[2025-08-19 17:12:51] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -83.291322+0.002610j
[2025-08-19 17:13:00] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -83.302316+0.000668j
[2025-08-19 17:13:10] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -83.419506-0.007465j
[2025-08-19 17:13:19] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -83.443112-0.014287j
[2025-08-19 17:13:28] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -83.355311+0.003347j
[2025-08-19 17:13:38] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -83.296510+0.010435j
[2025-08-19 17:13:47] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -83.306333+0.022850j
[2025-08-19 17:13:56] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -83.327047+0.008467j
[2025-08-19 17:14:06] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -83.205185+0.011371j
[2025-08-19 17:14:15] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -83.350309-0.007352j
[2025-08-19 17:14:24] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -83.274483+0.020351j
[2025-08-19 17:14:34] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -83.259588-0.001837j
[2025-08-19 17:14:43] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -83.216870+0.003988j
[2025-08-19 17:14:52] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -83.090328+0.002342j
[2025-08-19 17:15:01] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -83.186310-0.002248j
[2025-08-19 17:15:11] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -83.271587+0.010919j
[2025-08-19 17:15:20] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -83.192370-0.002110j
[2025-08-19 17:15:29] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -83.246651-0.001668j
[2025-08-19 17:15:39] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -83.337757-0.005270j
[2025-08-19 17:15:48] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -83.137551-0.008045j
[2025-08-19 17:15:57] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -83.206584+0.013075j
[2025-08-19 17:16:07] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -83.184584-0.007191j
[2025-08-19 17:16:16] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -83.381532+0.001630j
[2025-08-19 17:16:25] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -83.400119-0.014205j
[2025-08-19 17:16:35] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -83.416339+0.010646j
[2025-08-19 17:16:44] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -83.355231+0.008322j
[2025-08-19 17:16:54] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -83.276218+0.003742j
[2025-08-19 17:17:03] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -83.381779-0.011253j
[2025-08-19 17:17:12] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -83.387217+0.004132j
[2025-08-19 17:17:22] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -83.511193+0.006720j
[2025-08-19 17:17:31] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -83.499932-0.000496j
[2025-08-19 17:17:40] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -83.383808-0.005744j
[2025-08-19 17:17:49] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -83.416309-0.012973j
[2025-08-19 17:17:59] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -83.437147-0.009960j
[2025-08-19 17:18:08] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -83.422329-0.001252j
[2025-08-19 17:18:17] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -83.313528-0.012577j
[2025-08-19 17:18:27] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -83.236814+0.000594j
[2025-08-19 17:18:36] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -83.265633-0.002069j
[2025-08-19 17:18:46] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -83.198361-0.003372j
[2025-08-19 17:18:55] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -83.288139-0.012241j
[2025-08-19 17:19:04] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -83.203634-0.001477j
[2025-08-19 17:19:14] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -83.279196-0.013341j
[2025-08-19 17:19:23] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -83.062847-0.017016j
[2025-08-19 17:19:32] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -83.074703-0.006198j
[2025-08-19 17:19:42] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -83.258114-0.007875j
[2025-08-19 17:19:51] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -83.227801+0.017752j
[2025-08-19 17:20:00] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.342458-0.001505j
[2025-08-19 17:20:10] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -83.198389+0.005052j
[2025-08-19 17:20:19] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -83.296322+0.013284j
[2025-08-19 17:20:19] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-19 17:20:28] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -83.285108-0.008721j
[2025-08-19 17:20:38] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -83.319499+0.000644j
[2025-08-19 17:20:47] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -83.188368+0.007530j
[2025-08-19 17:20:56] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -83.278899+0.009083j
[2025-08-19 17:21:06] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -83.072845+0.003458j
[2025-08-19 17:21:15] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -83.077435-0.005789j
[2025-08-19 17:21:24] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -83.029993-0.017515j
[2025-08-19 17:21:34] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -83.014935+0.000383j
[2025-08-19 17:21:43] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -83.106368-0.018439j
[2025-08-19 17:21:52] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -83.201345-0.022349j
[2025-08-19 17:22:02] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -83.211074-0.010777j
[2025-08-19 17:22:11] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -83.217025+0.000457j
[2025-08-19 17:22:20] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -83.188695-0.004028j
[2025-08-19 17:22:30] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -83.124103+0.008359j
[2025-08-19 17:22:39] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -83.011535+0.039474j
[2025-08-19 17:22:48] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -83.117041+0.000063j
[2025-08-19 17:22:58] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -83.024199+0.004495j
[2025-08-19 17:23:07] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -83.000699-0.000920j
[2025-08-19 17:23:16] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -83.148341-0.000539j
[2025-08-19 17:23:26] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -83.112456+0.003021j
[2025-08-19 17:23:35] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -83.198648+0.006054j
[2025-08-19 17:23:44] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -83.105722+0.005356j
[2025-08-19 17:23:54] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -83.275553+0.002655j
[2025-08-19 17:24:03] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -83.073917+0.005089j
[2025-08-19 17:24:12] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -83.106517+0.007254j
[2025-08-19 17:24:22] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -83.116850+0.005957j
[2025-08-19 17:24:31] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -83.082095-0.010536j
[2025-08-19 17:24:40] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -83.173935-0.009827j
[2025-08-19 17:24:50] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -83.184760-0.003740j
[2025-08-19 17:24:59] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -83.008360+0.002789j
[2025-08-19 17:25:08] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -83.176846+0.011557j
[2025-08-19 17:25:18] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -83.035544+0.000964j
[2025-08-19 17:25:27] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -83.012006-0.002239j
[2025-08-19 17:25:36] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -83.103031-0.004117j
[2025-08-19 17:25:46] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -83.194542+0.011496j
[2025-08-19 17:25:55] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -83.240444+0.000590j
[2025-08-19 17:26:04] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -83.209326-0.009914j
[2025-08-19 17:26:14] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -83.145907-0.016001j
[2025-08-19 17:26:23] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -83.142124-0.009647j
[2025-08-19 17:26:32] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -83.149486+0.004970j
[2025-08-19 17:26:42] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -83.120826+0.004585j
[2025-08-19 17:26:51] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -83.178364+0.026255j
[2025-08-19 17:27:00] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -83.102153-0.000418j
[2025-08-19 17:27:10] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -83.309500-0.011237j
[2025-08-19 17:27:19] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -83.199456+0.005981j
[2025-08-19 17:27:28] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -83.105730+0.005268j
[2025-08-19 17:27:38] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -83.146796+0.002738j
[2025-08-19 17:27:47] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -83.224244-0.003108j
[2025-08-19 17:27:56] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -83.137403+0.006133j
[2025-08-19 17:28:06] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -83.150080+0.000615j
[2025-08-19 17:28:06] ✅ Training completed | Restarts: 2
[2025-08-19 17:28:06] ============================================================
[2025-08-19 17:28:06] Training completed | Runtime: 9837.4s
[2025-08-19 17:28:19] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-19 17:28:19] ============================================================
