[2025-08-19 10:23:17] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-08-19 10:23:17]   - 迭代次数: final
[2025-08-19 10:23:17]   - 能量: -85.660117-0.008876j ± 0.105982
[2025-08-19 10:23:17]   - 时间戳: 2025-08-19T02:34:42.780231+08:00
[2025-08-19 10:23:25] ✓ 变分状态参数已从checkpoint恢复
[2025-08-19 10:23:25] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-19 10:23:25] ==================================================
[2025-08-19 10:23:25] GCNN for Shastry-Sutherland Model
[2025-08-19 10:23:25] ==================================================
[2025-08-19 10:23:25] System parameters:
[2025-08-19 10:23:25]   - System size: L=5, N=100
[2025-08-19 10:23:25]   - System parameters: J1=0.06, J2=0.0, Q=1.0
[2025-08-19 10:23:25] --------------------------------------------------
[2025-08-19 10:23:25] Model parameters:
[2025-08-19 10:23:25]   - Number of layers = 4
[2025-08-19 10:23:25]   - Number of features = 4
[2025-08-19 10:23:25]   - Total parameters = 19628
[2025-08-19 10:23:25] --------------------------------------------------
[2025-08-19 10:23:25] Training parameters:
[2025-08-19 10:23:25]   - Learning rate: 0.015
[2025-08-19 10:23:25]   - Total iterations: 1050
[2025-08-19 10:23:25]   - Annealing cycles: 3
[2025-08-19 10:23:25]   - Initial period: 150
[2025-08-19 10:23:25]   - Period multiplier: 2.0
[2025-08-19 10:23:25]   - Temperature range: 0.0-1.0
[2025-08-19 10:23:25]   - Samples: 4096
[2025-08-19 10:23:25]   - Discarded samples: 0
[2025-08-19 10:23:25]   - Chunk size: 2048
[2025-08-19 10:23:25]   - Diagonal shift: 0.2
[2025-08-19 10:23:25]   - Gradient clipping: 1.0
[2025-08-19 10:23:25]   - Checkpoint enabled: interval=100
[2025-08-19 10:23:25]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.06/training/checkpoints
[2025-08-19 10:23:25] --------------------------------------------------
[2025-08-19 10:23:25] Device status:
[2025-08-19 10:23:25]   - Devices model: A100
[2025-08-19 10:23:25]   - Number of devices: 1
[2025-08-19 10:23:25]   - Sharding: True
[2025-08-19 10:23:25] ============================================================
[2025-08-19 10:23:58] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -86.331290+0.104085j
[2025-08-19 10:24:20] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -86.203504+0.070532j
[2025-08-19 10:24:30] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -86.255064+0.047818j
[2025-08-19 10:24:39] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -86.217748+0.026679j
[2025-08-19 10:24:48] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -86.179694+0.004795j
[2025-08-19 10:24:57] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -86.211344-0.005884j
[2025-08-19 10:25:07] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -86.123197-0.001729j
[2025-08-19 10:25:16] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -86.199616-0.005162j
[2025-08-19 10:25:25] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -86.242779-0.017862j
[2025-08-19 10:25:35] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -86.299645-0.006651j
[2025-08-19 10:25:44] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -86.314674-0.010808j
[2025-08-19 10:25:53] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -86.311344-0.011620j
[2025-08-19 10:26:03] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -86.288874-0.003689j
[2025-08-19 10:26:12] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -86.254708-0.047508j
[2025-08-19 10:26:21] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -86.236997+0.007165j
[2025-08-19 10:26:30] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -86.191550-0.027762j
[2025-08-19 10:26:40] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -86.267609+0.022491j
[2025-08-19 10:26:49] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -86.156310+0.018547j
[2025-08-19 10:26:58] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -86.327355+0.015324j
[2025-08-19 10:27:08] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -86.258325+0.020849j
[2025-08-19 10:27:17] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -86.221886-0.016995j
[2025-08-19 10:27:26] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -86.284140-0.043084j
[2025-08-19 10:27:35] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -86.310083-0.014760j
[2025-08-19 10:27:45] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -86.115935+0.026316j
[2025-08-19 10:27:54] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -86.171590+0.000676j
[2025-08-19 10:28:03] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -86.183873+0.009120j
[2025-08-19 10:28:13] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -86.228485+0.010440j
[2025-08-19 10:28:22] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -86.203216+0.035542j
[2025-08-19 10:28:31] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -86.147714+0.024854j
[2025-08-19 10:28:41] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -86.308606-0.046527j
[2025-08-19 10:28:50] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -86.237355+0.020407j
[2025-08-19 10:28:59] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -86.218856+0.008344j
[2025-08-19 10:29:08] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -86.225828+0.015979j
[2025-08-19 10:29:18] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -86.209537-0.012871j
[2025-08-19 10:29:27] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -86.242129-0.002799j
[2025-08-19 10:29:36] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -86.295652+0.007048j
[2025-08-19 10:29:46] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -86.315746+0.012665j
[2025-08-19 10:29:55] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -86.314769+0.015209j
[2025-08-19 10:30:04] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -86.283563+0.018518j
[2025-08-19 10:30:14] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -86.262748-0.002145j
[2025-08-19 10:30:23] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -86.276097+0.012007j
[2025-08-19 10:30:32] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -86.249832+0.015137j
[2025-08-19 10:30:41] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -86.219415+0.003667j
[2025-08-19 10:30:51] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -86.288426-0.001241j
[2025-08-19 10:31:00] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -86.318083-0.002357j
[2025-08-19 10:31:09] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -86.431244+0.015088j
[2025-08-19 10:31:19] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -86.476050+0.012289j
[2025-08-19 10:31:28] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -86.352220+0.025330j
[2025-08-19 10:31:37] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -86.331434+0.000421j
[2025-08-19 10:31:46] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -86.283383+0.017755j
[2025-08-19 10:31:56] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -86.213379+0.004717j
[2025-08-19 10:32:05] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -86.188936+0.013278j
[2025-08-19 10:32:14] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -86.178699+0.012990j
[2025-08-19 10:32:24] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -86.123002+0.003842j
[2025-08-19 10:32:33] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -86.069823+0.006894j
[2025-08-19 10:32:42] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -86.027525-0.008190j
[2025-08-19 10:32:51] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -86.081511-0.004278j
[2025-08-19 10:33:01] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -86.173237+0.006017j
[2025-08-19 10:33:10] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -85.990576-0.006139j
[2025-08-19 10:33:19] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -86.063687+0.011258j
[2025-08-19 10:33:29] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -86.191881+0.001152j
[2025-08-19 10:33:38] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -86.265420+0.029316j
[2025-08-19 10:33:47] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -86.257757+0.004014j
[2025-08-19 10:33:57] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -86.315915+0.005766j
[2025-08-19 10:34:06] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -86.363607-0.006364j
[2025-08-19 10:34:15] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -86.240708-0.019039j
[2025-08-19 10:34:24] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -86.252642-0.003886j
[2025-08-19 10:34:34] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -86.263309+0.002105j
[2025-08-19 10:34:43] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -86.030670-0.016995j
[2025-08-19 10:34:52] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -86.078177-0.009603j
[2025-08-19 10:35:02] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -85.995262-0.005344j
[2025-08-19 10:35:11] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -86.175264+0.000869j
[2025-08-19 10:35:20] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -86.323326+0.008092j
[2025-08-19 10:35:30] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -86.129182-0.002317j
[2025-08-19 10:35:39] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -86.269613+0.009678j
[2025-08-19 10:35:48] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -86.160750-0.006471j
[2025-08-19 10:35:57] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -86.157568-0.008288j
[2025-08-19 10:36:07] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -86.263808-0.007654j
[2025-08-19 10:36:16] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -86.376553+0.002716j
[2025-08-19 10:36:25] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -86.334363+0.006221j
[2025-08-19 10:36:35] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -86.299954-0.002600j
[2025-08-19 10:36:44] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -86.286963+0.010379j
[2025-08-19 10:36:53] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -86.395760+0.006711j
[2025-08-19 10:37:02] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -86.211367-0.008997j
[2025-08-19 10:37:12] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -86.129122+0.006521j
[2025-08-19 10:37:21] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -86.164825+0.006369j
[2025-08-19 10:37:30] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -86.151589-0.006109j
[2025-08-19 10:37:40] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -86.243522+0.005944j
[2025-08-19 10:37:49] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -86.157939+0.020396j
[2025-08-19 10:37:58] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -86.028255-0.007970j
[2025-08-19 10:38:07] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -86.285088-0.027988j
[2025-08-19 10:38:17] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -86.218598-0.009194j
[2025-08-19 10:38:26] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -86.204715+0.011741j
[2025-08-19 10:38:35] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -86.233832+0.026197j
[2025-08-19 10:38:45] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -86.207868+0.018982j
[2025-08-19 10:38:54] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -86.208593-0.034136j
[2025-08-19 10:39:03] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -86.171885+0.008382j
[2025-08-19 10:39:13] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -86.219989+0.021237j
[2025-08-19 10:39:22] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -86.190186-0.035409j
[2025-08-19 10:39:31] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -86.205645-0.001959j
[2025-08-19 10:39:31] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-19 10:39:40] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -86.148130+0.023040j
[2025-08-19 10:39:50] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -86.081930+0.021454j
[2025-08-19 10:39:59] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -86.137658+0.012676j
[2025-08-19 10:40:08] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -85.962364-0.034830j
[2025-08-19 10:40:18] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -86.081710-0.008532j
[2025-08-19 10:40:27] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -86.011608+0.030411j
[2025-08-19 10:40:36] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -86.250821-0.007922j
[2025-08-19 10:40:45] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -86.164910-0.037513j
[2025-08-19 10:40:55] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -86.177081+0.011606j
[2025-08-19 10:41:04] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -86.136513-0.004112j
[2025-08-19 10:41:13] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -86.104371-0.002709j
[2025-08-19 10:41:23] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -86.122327+0.009567j
[2025-08-19 10:41:32] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -86.073620+0.003861j
[2025-08-19 10:41:41] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -86.079683+0.012515j
[2025-08-19 10:41:51] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -86.057749+0.009799j
[2025-08-19 10:42:00] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -86.174369+0.008212j
[2025-08-19 10:42:09] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -86.134167-0.002462j
[2025-08-19 10:42:18] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -86.040128-0.005038j
[2025-08-19 10:42:28] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -86.144391-0.004392j
[2025-08-19 10:42:37] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -86.046525-0.009773j
[2025-08-19 10:42:46] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -86.177319+0.003239j
[2025-08-19 10:42:56] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -86.310763+0.003062j
[2025-08-19 10:43:05] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -86.210746-0.006035j
[2025-08-19 10:43:14] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -86.234591+0.008211j
[2025-08-19 10:43:23] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -86.337760-0.008582j
[2025-08-19 10:43:33] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -86.257509-0.005055j
[2025-08-19 10:43:42] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -86.222507+0.000648j
[2025-08-19 10:43:51] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -86.301701-0.002501j
[2025-08-19 10:44:01] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -86.188428+0.001854j
[2025-08-19 10:44:10] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -86.050442+0.011542j
[2025-08-19 10:44:19] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -86.032391+0.004289j
[2025-08-19 10:44:29] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -86.122127-0.002713j
[2025-08-19 10:44:38] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -86.087186+0.006375j
[2025-08-19 10:44:47] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -86.192973-0.015777j
[2025-08-19 10:44:56] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -86.133815+0.000832j
[2025-08-19 10:45:06] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -86.129588-0.003155j
[2025-08-19 10:45:15] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -86.089780+0.015174j
[2025-08-19 10:45:24] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -85.996874-0.007321j
[2025-08-19 10:45:34] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -86.160836+0.001287j
[2025-08-19 10:45:43] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -86.086108+0.004534j
[2025-08-19 10:45:52] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -86.050206+0.005105j
[2025-08-19 10:46:01] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -86.079110+0.008959j
[2025-08-19 10:46:11] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -86.190494-0.000682j
[2025-08-19 10:46:20] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -86.055455+0.012092j
[2025-08-19 10:46:29] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -86.287471+0.008512j
[2025-08-19 10:46:39] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -86.345965-0.001237j
[2025-08-19 10:46:48] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -86.286765-0.000400j
[2025-08-19 10:46:57] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -86.306708-0.000417j
[2025-08-19 10:47:07] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -86.314118+0.010281j
[2025-08-19 10:47:16] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -86.270959+0.016386j
[2025-08-19 10:47:16] RESTART #1 | Period: 300
[2025-08-19 10:47:25] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -86.259520-0.010411j
[2025-08-19 10:47:34] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -86.408072+0.003005j
[2025-08-19 10:47:44] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -86.370448-0.003635j
[2025-08-19 10:47:53] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -86.283418+0.012434j
[2025-08-19 10:48:02] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -86.331788+0.011151j
[2025-08-19 10:48:12] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -86.248159+0.013203j
[2025-08-19 10:48:21] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -86.305965-0.004696j
[2025-08-19 10:48:30] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -86.233696-0.009345j
[2025-08-19 10:48:39] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -86.067121-0.008924j
[2025-08-19 10:48:49] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -86.292630-0.027725j
[2025-08-19 10:48:58] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -86.156281-0.005859j
[2025-08-19 10:49:07] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -86.123766-0.012485j
[2025-08-19 10:49:17] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -86.032267-0.006329j
[2025-08-19 10:49:26] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -86.143104-0.019458j
[2025-08-19 10:49:35] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -86.172689+0.007613j
[2025-08-19 10:49:45] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -86.177179+0.011630j
[2025-08-19 10:49:54] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -86.153407+0.036728j
[2025-08-19 10:50:03] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -86.176039+0.034330j
[2025-08-19 10:50:13] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -86.142545-0.007483j
[2025-08-19 10:50:22] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -86.152580-0.028493j
[2025-08-19 10:50:31] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -86.150650+0.018150j
[2025-08-19 10:50:40] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -86.229613+0.019741j
[2025-08-19 10:50:50] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -86.169657-0.001494j
[2025-08-19 10:50:59] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -86.058975-0.026545j
[2025-08-19 10:51:08] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -86.193555-0.050396j
[2025-08-19 10:51:18] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -86.127877-0.051370j
[2025-08-19 10:51:27] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -86.145908-0.006489j
[2025-08-19 10:51:36] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -86.157627+0.008890j
[2025-08-19 10:51:45] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -86.272107+0.022083j
[2025-08-19 10:51:55] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -86.125563-0.008232j
[2025-08-19 10:52:04] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -86.260711-0.009413j
[2025-08-19 10:52:13] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -86.229365-0.006903j
[2025-08-19 10:52:23] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -86.177895+0.008786j
[2025-08-19 10:52:32] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -86.052962+0.018987j
[2025-08-19 10:52:41] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -86.029133-0.021307j
[2025-08-19 10:52:50] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -86.004321-0.022813j
[2025-08-19 10:53:00] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -86.071240+0.027983j
[2025-08-19 10:53:09] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -86.088575-0.065904j
[2025-08-19 10:53:18] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -86.092082+0.004625j
[2025-08-19 10:53:28] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -86.137853-0.010460j
[2025-08-19 10:53:37] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -86.034609-0.008027j
[2025-08-19 10:53:46] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -86.170627+0.019422j
[2025-08-19 10:53:56] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -86.081255+0.011081j
[2025-08-19 10:54:05] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -86.158481+0.001231j
[2025-08-19 10:54:14] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -86.163066+0.021406j
[2025-08-19 10:54:23] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -86.179378-0.016653j
[2025-08-19 10:54:33] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -86.192418-0.019683j
[2025-08-19 10:54:42] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -86.192473-0.001444j
[2025-08-19 10:54:51] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -86.365443-0.012056j
[2025-08-19 10:55:01] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -86.086246-0.000761j
[2025-08-19 10:55:01] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-19 10:55:10] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -86.054705+0.003039j
[2025-08-19 10:55:19] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -86.001282+0.035902j
[2025-08-19 10:55:29] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -86.234135+0.017175j
[2025-08-19 10:55:38] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -86.260791+0.016061j
[2025-08-19 10:55:47] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -86.137789-0.026972j
[2025-08-19 10:55:56] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -86.258989-0.037210j
[2025-08-19 10:56:06] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -86.101985-0.010426j
[2025-08-19 10:56:15] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -86.170885+0.003005j
[2025-08-19 10:56:24] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -86.180472+0.009611j
[2025-08-19 10:56:34] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -86.179632+0.005332j
[2025-08-19 10:56:43] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -86.116777+0.023207j
[2025-08-19 10:56:52] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -86.083926+0.001315j
[2025-08-19 10:57:01] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -86.120850-0.014150j
[2025-08-19 10:57:11] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -86.238785-0.001767j
[2025-08-19 10:57:20] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -86.189553+0.016037j
[2025-08-19 10:57:29] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -86.228818+0.001933j
[2025-08-19 10:57:39] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -86.199939+0.015698j
[2025-08-19 10:57:48] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -86.333651+0.015456j
[2025-08-19 10:57:57] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -86.152676+0.026592j
[2025-08-19 10:58:07] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -86.088032-0.017722j
[2025-08-19 10:58:16] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -86.092987-0.004583j
[2025-08-19 10:58:25] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -86.087947-0.009580j
[2025-08-19 10:58:34] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -86.153964+0.002957j
[2025-08-19 10:58:44] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -86.149358+0.002276j
[2025-08-19 10:58:53] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -86.209826+0.002962j
[2025-08-19 10:59:02] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -86.197486+0.027307j
[2025-08-19 10:59:12] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -86.241226-0.008308j
[2025-08-19 10:59:21] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -86.111703+0.009165j
[2025-08-19 10:59:30] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -86.182191-0.017835j
[2025-08-19 10:59:39] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -86.198780-0.003647j
[2025-08-19 10:59:49] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -86.114099+0.002919j
[2025-08-19 10:59:58] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -86.170464-0.003920j
[2025-08-19 11:00:07] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -86.110591+0.017917j
[2025-08-19 11:00:17] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -86.138979-0.016427j
[2025-08-19 11:00:26] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -86.262600-0.004510j
[2025-08-19 11:00:35] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -86.138427-0.018220j
[2025-08-19 11:00:45] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -86.173077-0.019706j
[2025-08-19 11:00:54] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -86.141114-0.012747j
[2025-08-19 11:01:03] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -86.230452+0.000927j
[2025-08-19 11:01:13] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -86.226111-0.026080j
[2025-08-19 11:01:22] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -86.300772+0.016950j
[2025-08-19 11:01:31] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -86.204566-0.033136j
[2025-08-19 11:01:41] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -86.216106-0.036877j
[2025-08-19 11:01:50] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -86.287277-0.015157j
[2025-08-19 11:01:59] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -86.359265+0.007018j
[2025-08-19 11:02:08] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -86.174928+0.006771j
[2025-08-19 11:02:18] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -86.255099-0.022062j
[2025-08-19 11:02:27] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -86.212495+0.006575j
[2025-08-19 11:02:36] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -86.255012-0.010365j
[2025-08-19 11:02:46] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -86.139366-0.007404j
[2025-08-19 11:02:55] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -86.045800-0.016292j
[2025-08-19 11:03:04] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -86.013886+0.006717j
[2025-08-19 11:03:14] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -86.232396+0.025575j
[2025-08-19 11:03:23] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -86.197764-0.023716j
[2025-08-19 11:03:32] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -86.173429-0.022865j
[2025-08-19 11:03:41] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -86.154233-0.065169j
[2025-08-19 11:03:51] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -86.121076-0.003303j
[2025-08-19 11:04:00] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -86.019759-0.010365j
[2025-08-19 11:04:09] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -86.105702-0.009580j
[2025-08-19 11:04:19] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -86.049749+0.010715j
[2025-08-19 11:04:28] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -86.089287-0.005274j
[2025-08-19 11:04:37] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -86.133283+0.001246j
[2025-08-19 11:04:46] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -86.073216+0.001359j
[2025-08-19 11:04:56] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -86.126610+0.010329j
[2025-08-19 11:05:05] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -86.078953-0.016273j
[2025-08-19 11:05:14] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -86.108744-0.026242j
[2025-08-19 11:05:24] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -86.209058-0.010095j
[2025-08-19 11:05:33] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -86.237160+0.042065j
[2025-08-19 11:05:42] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -86.257948-0.005276j
[2025-08-19 11:05:51] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -86.234931-0.002607j
[2025-08-19 11:06:01] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -86.293654+0.007226j
[2025-08-19 11:06:10] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -86.375079+0.034155j
[2025-08-19 11:06:19] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -86.250926-0.004413j
[2025-08-19 11:06:29] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -86.295662+0.016042j
[2025-08-19 11:06:38] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -86.275574+0.004849j
[2025-08-19 11:06:47] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -86.322518-0.006381j
[2025-08-19 11:06:57] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -86.264079+0.016576j
[2025-08-19 11:07:06] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -86.236687-0.014281j
[2025-08-19 11:07:15] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -86.305203+0.001034j
[2025-08-19 11:07:24] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -86.326878+0.040543j
[2025-08-19 11:07:34] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -86.286651+0.000461j
[2025-08-19 11:07:43] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -86.192157-0.010107j
[2025-08-19 11:07:52] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -86.258124-0.008414j
[2025-08-19 11:08:02] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -86.296445-0.004111j
[2025-08-19 11:08:11] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -86.174355+0.000933j
[2025-08-19 11:08:20] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -86.145555+0.011611j
[2025-08-19 11:08:29] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -86.264761+0.024509j
[2025-08-19 11:08:39] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -86.085951+0.020753j
[2025-08-19 11:08:48] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -86.062213+0.018081j
[2025-08-19 11:08:57] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -86.048235+0.019399j
[2025-08-19 11:09:07] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -86.134269+0.007844j
[2025-08-19 11:09:16] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -86.199308-0.021645j
[2025-08-19 11:09:25] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -86.127697-0.000177j
[2025-08-19 11:09:35] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -86.149572+0.005638j
[2025-08-19 11:09:44] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -86.265734-0.005507j
[2025-08-19 11:09:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -86.301638-0.007184j
[2025-08-19 11:10:02] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -86.166594+0.021887j
[2025-08-19 11:10:12] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -86.001711+0.013157j
[2025-08-19 11:10:21] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -86.130699-0.051473j
[2025-08-19 11:10:30] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -86.122579-0.021335j
[2025-08-19 11:10:30] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-19 11:10:40] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -86.136453-0.006952j
[2025-08-19 11:10:49] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -86.226122-0.022363j
[2025-08-19 11:10:58] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -86.185706+0.016661j
[2025-08-19 11:11:08] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -86.339511-0.019078j
[2025-08-19 11:11:17] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -86.265640+0.004944j
[2025-08-19 11:11:26] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -86.233790-0.001519j
[2025-08-19 11:11:35] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -86.314237-0.001579j
[2025-08-19 11:11:45] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -86.223936+0.000574j
[2025-08-19 11:11:54] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -86.324173+0.007981j
[2025-08-19 11:12:03] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -86.250159+0.004115j
[2025-08-19 11:12:13] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -86.252415+0.000518j
[2025-08-19 11:12:22] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -86.307121+0.006327j
[2025-08-19 11:12:31] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -86.275637-0.003136j
[2025-08-19 11:12:41] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -86.411188-0.016243j
[2025-08-19 11:12:50] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -86.347239+0.000991j
[2025-08-19 11:12:59] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -86.345325-0.015146j
[2025-08-19 11:13:08] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -86.266110-0.013144j
[2025-08-19 11:13:18] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -86.042326-0.010983j
[2025-08-19 11:13:27] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -86.130369-0.006984j
[2025-08-19 11:13:36] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -86.120150+0.007979j
[2025-08-19 11:13:46] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -86.067885+0.006571j
[2025-08-19 11:13:55] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -86.048374-0.021157j
[2025-08-19 11:14:04] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -86.031458+0.011145j
[2025-08-19 11:14:14] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -86.192242+0.027808j
[2025-08-19 11:14:23] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -85.920184+0.013045j
[2025-08-19 11:14:32] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -86.079834+0.001174j
[2025-08-19 11:14:41] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -86.169172-0.005393j
[2025-08-19 11:14:51] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -86.022251-0.014199j
[2025-08-19 11:15:00] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -86.074341+0.010383j
[2025-08-19 11:15:09] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -86.045866-0.020697j
[2025-08-19 11:15:19] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -86.035917+0.017121j
[2025-08-19 11:15:28] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -86.092526-0.009561j
[2025-08-19 11:15:37] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -86.033186+0.005790j
[2025-08-19 11:15:46] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -85.970575-0.008794j
[2025-08-19 11:15:56] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -85.976965-0.022136j
[2025-08-19 11:16:05] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -86.006470-0.013505j
[2025-08-19 11:16:14] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -86.075050-0.019018j
[2025-08-19 11:16:24] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -85.989007+0.061079j
[2025-08-19 11:16:33] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -86.037535-0.020420j
[2025-08-19 11:16:42] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -86.197383-0.019352j
[2025-08-19 11:16:52] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -86.142687+0.001861j
[2025-08-19 11:17:01] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -86.084266-0.002800j
[2025-08-19 11:17:10] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -86.106077-0.015156j
[2025-08-19 11:17:19] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -86.041435-0.030040j
[2025-08-19 11:17:29] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -86.196932-0.013704j
[2025-08-19 11:17:38] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -86.078222-0.007446j
[2025-08-19 11:17:47] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -86.104998-0.001688j
[2025-08-19 11:17:57] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -86.110310-0.000839j
[2025-08-19 11:18:06] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -86.171853+0.004003j
[2025-08-19 11:18:15] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -86.176580+0.002272j
[2025-08-19 11:18:25] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -86.164246-0.005504j
[2025-08-19 11:18:34] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -86.184085-0.021273j
[2025-08-19 11:18:43] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -86.256671-0.017395j
[2025-08-19 11:18:53] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -86.215975-0.006247j
[2025-08-19 11:19:02] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -86.165269-0.015007j
[2025-08-19 11:19:11] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -86.121889-0.002586j
[2025-08-19 11:19:20] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -86.076233+0.002115j
[2025-08-19 11:19:30] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -86.128300+0.004486j
[2025-08-19 11:19:39] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -86.164500+0.000322j
[2025-08-19 11:19:48] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -86.094705+0.002645j
[2025-08-19 11:19:58] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -86.049164-0.003296j
[2025-08-19 11:20:07] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -86.050634+0.011892j
[2025-08-19 11:20:16] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -86.201575-0.006933j
[2025-08-19 11:20:25] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -86.127115-0.015136j
[2025-08-19 11:20:35] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -86.126931+0.006558j
[2025-08-19 11:20:44] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -86.044571+0.004784j
[2025-08-19 11:20:53] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -86.206447+0.013361j
[2025-08-19 11:21:03] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -86.059480-0.003526j
[2025-08-19 11:21:12] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -85.935040+0.003334j
[2025-08-19 11:21:21] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -86.066401+0.041525j
[2025-08-19 11:21:31] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -86.005690+0.023539j
[2025-08-19 11:21:40] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -86.055783+0.007845j
[2025-08-19 11:21:49] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -86.177546-0.007554j
[2025-08-19 11:21:59] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -86.162505-0.000031j
[2025-08-19 11:22:08] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -86.162021-0.004234j
[2025-08-19 11:22:17] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -86.074524+0.017407j
[2025-08-19 11:22:26] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -86.120434+0.022531j
[2025-08-19 11:22:36] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -86.158597+0.002260j
[2025-08-19 11:22:45] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -86.154131-0.016813j
[2025-08-19 11:22:54] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -86.089378-0.020063j
[2025-08-19 11:23:04] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -86.135122-0.028720j
[2025-08-19 11:23:13] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -86.000960+0.008461j
[2025-08-19 11:23:22] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -86.012322+0.006064j
[2025-08-19 11:23:32] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -85.782404+0.007130j
[2025-08-19 11:23:41] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -85.967988-0.011331j
[2025-08-19 11:23:50] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -85.967839-0.024165j
[2025-08-19 11:23:59] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -86.139992-0.006757j
[2025-08-19 11:24:09] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -86.128288+0.004872j
[2025-08-19 11:24:18] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -86.083704-0.020654j
[2025-08-19 11:24:27] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -86.263462-0.009018j
[2025-08-19 11:24:37] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -86.149284-0.026280j
[2025-08-19 11:24:46] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -86.208231+0.000780j
[2025-08-19 11:24:55] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -86.249877-0.017585j
[2025-08-19 11:25:05] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -86.235870-0.018513j
[2025-08-19 11:25:14] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -86.108694-0.015576j
[2025-08-19 11:25:23] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -86.276059-0.014868j
[2025-08-19 11:25:32] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -86.209195-0.003021j
[2025-08-19 11:25:42] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -86.212727-0.008389j
[2025-08-19 11:25:51] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -86.351034+0.000725j
[2025-08-19 11:26:00] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -86.290325+0.017361j
[2025-08-19 11:26:00] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-19 11:26:10] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -86.269135+0.004691j
[2025-08-19 11:26:19] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -86.458296+0.015299j
[2025-08-19 11:26:28] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -86.405693-0.004588j
[2025-08-19 11:26:38] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -86.383797+0.015408j
[2025-08-19 11:26:47] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -86.265948-0.006836j
[2025-08-19 11:26:56] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -86.218524-0.002508j
[2025-08-19 11:27:05] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -86.335821+0.011674j
[2025-08-19 11:27:15] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -86.147202+0.000954j
[2025-08-19 11:27:24] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -86.266851-0.003643j
[2025-08-19 11:27:33] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -86.174075-0.010455j
[2025-08-19 11:27:43] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -86.283935+0.000533j
[2025-08-19 11:27:52] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -86.149892+0.011078j
[2025-08-19 11:28:01] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -86.265272-0.017311j
[2025-08-19 11:28:11] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -86.374952+0.014900j
[2025-08-19 11:28:20] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -86.044883-0.006155j
[2025-08-19 11:28:29] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -86.137022+0.015243j
[2025-08-19 11:28:38] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -86.148526+0.007325j
[2025-08-19 11:28:48] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -86.129013-0.026111j
[2025-08-19 11:28:57] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -86.271682-0.002912j
[2025-08-19 11:29:06] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -86.229573+0.001130j
[2025-08-19 11:29:16] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -86.304373-0.009674j
[2025-08-19 11:29:25] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -86.161727-0.010619j
[2025-08-19 11:29:34] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -86.067174+0.038872j
[2025-08-19 11:29:44] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -85.947973-0.023690j
[2025-08-19 11:29:53] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -86.115570+0.004461j
[2025-08-19 11:30:02] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -86.018542-0.004715j
[2025-08-19 11:30:11] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -86.194177+0.006388j
[2025-08-19 11:30:21] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -86.080581-0.012120j
[2025-08-19 11:30:30] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -86.102315-0.011075j
[2025-08-19 11:30:39] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -86.133545+0.005680j
[2025-08-19 11:30:49] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -86.083234-0.001646j
[2025-08-19 11:30:58] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -86.071862-0.012708j
[2025-08-19 11:31:07] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -85.988203-0.006668j
[2025-08-19 11:31:16] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -85.961677-0.054946j
[2025-08-19 11:31:26] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -86.031945-0.000117j
[2025-08-19 11:31:35] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -85.982818-0.031547j
[2025-08-19 11:31:44] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -86.082094-0.001016j
[2025-08-19 11:31:54] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -86.132011+0.011819j
[2025-08-19 11:32:03] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -86.028905+0.003802j
[2025-08-19 11:32:12] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -86.125679+0.009225j
[2025-08-19 11:32:22] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -86.115543-0.012483j
[2025-08-19 11:32:31] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -86.182164+0.016378j
[2025-08-19 11:32:40] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -86.264954-0.005960j
[2025-08-19 11:32:50] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -86.224168+0.007127j
[2025-08-19 11:32:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -86.213786-0.004178j
[2025-08-19 11:33:08] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -86.139123+0.013927j
[2025-08-19 11:33:17] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -86.092486-0.008217j
[2025-08-19 11:33:27] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -86.008452-0.001675j
[2025-08-19 11:33:36] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -85.927184-0.004581j
[2025-08-19 11:33:45] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -86.034843-0.014249j
[2025-08-19 11:33:45] RESTART #2 | Period: 600
[2025-08-19 11:33:55] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -86.216780-0.009135j
[2025-08-19 11:34:04] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -86.069213+0.016380j
[2025-08-19 11:34:13] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -86.308048+0.013125j
[2025-08-19 11:34:22] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -86.267546-0.020087j
[2025-08-19 11:34:32] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -86.095933+0.002796j
[2025-08-19 11:34:41] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -86.300064+0.016817j
[2025-08-19 11:34:50] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -86.317924-0.005170j
[2025-08-19 11:35:00] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -86.320476+0.008722j
[2025-08-19 11:35:09] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -86.155799-0.018995j
[2025-08-19 11:35:18] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -86.238616+0.020790j
[2025-08-19 11:35:28] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -86.339774+0.002941j
[2025-08-19 11:35:37] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -86.352541-0.016859j
[2025-08-19 11:35:46] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -86.230883-0.003094j
[2025-08-19 11:35:55] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -86.230867-0.016235j
[2025-08-19 11:36:05] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -86.430076-0.017380j
[2025-08-19 11:36:14] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -86.194510-0.004039j
[2025-08-19 11:36:23] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -86.109198-0.007826j
[2025-08-19 11:36:33] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -86.048206+0.000905j
[2025-08-19 11:36:42] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -86.189553-0.001500j
[2025-08-19 11:36:51] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -86.176869-0.006711j
[2025-08-19 11:37:01] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -86.235634+0.028312j
[2025-08-19 11:37:10] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -86.113513+0.007460j
[2025-08-19 11:37:19] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -86.266295-0.013817j
[2025-08-19 11:37:28] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -86.322961+0.033865j
[2025-08-19 11:37:38] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -86.249854+0.028581j
[2025-08-19 11:37:47] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -86.237005+0.007497j
[2025-08-19 11:37:56] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -86.418937+0.013798j
[2025-08-19 11:38:06] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -86.351157-0.023358j
[2025-08-19 11:38:15] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -86.289241-0.000568j
[2025-08-19 11:38:24] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -86.173290+0.004655j
[2025-08-19 11:38:34] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -86.104440-0.002896j
[2025-08-19 11:38:43] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -86.183230+0.022148j
[2025-08-19 11:38:52] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -86.200848-0.006641j
[2025-08-19 11:39:01] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -86.255251+0.004429j
[2025-08-19 11:39:11] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -86.245092-0.017668j
[2025-08-19 11:39:20] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -86.310348-0.001657j
[2025-08-19 11:39:29] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -86.212912-0.000985j
[2025-08-19 11:39:39] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -86.363471+0.002137j
[2025-08-19 11:39:48] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -86.272738-0.042131j
[2025-08-19 11:39:57] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -86.248050+0.011972j
[2025-08-19 11:40:07] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -86.202316+0.024306j
[2025-08-19 11:40:16] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -86.332484+0.008805j
[2025-08-19 11:40:25] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -86.339494-0.035945j
[2025-08-19 11:40:34] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -86.180740+0.039772j
[2025-08-19 11:40:44] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -86.298280-0.002599j
[2025-08-19 11:40:53] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -86.153727+0.036749j
[2025-08-19 11:41:02] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -86.057885+0.044352j
[2025-08-19 11:41:12] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -86.198182+0.020019j
[2025-08-19 11:41:21] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -86.090628+0.009343j
[2025-08-19 11:41:30] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -86.155997+0.015132j
[2025-08-19 11:41:30] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-19 11:41:40] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -86.234917+0.018610j
[2025-08-19 11:41:49] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -86.179565+0.038775j
[2025-08-19 11:41:58] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -86.286593+0.014961j
[2025-08-19 11:42:07] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -86.262102-0.027919j
[2025-08-19 11:42:17] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -86.193054-0.016720j
[2025-08-19 11:42:26] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -86.228240+0.002126j
[2025-08-19 11:42:35] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -86.194688-0.002473j
[2025-08-19 11:42:45] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -86.225608+0.002553j
[2025-08-19 11:42:54] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -86.241867-0.010902j
[2025-08-19 11:43:03] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -86.111173-0.006582j
[2025-08-19 11:43:13] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -86.259866+0.001248j
[2025-08-19 11:43:22] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -86.300065+0.006495j
[2025-08-19 11:43:31] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -86.219938-0.000008j
[2025-08-19 11:43:40] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -86.315602-0.019682j
[2025-08-19 11:43:50] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -86.258779-0.005630j
[2025-08-19 11:43:59] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -86.149878+0.002095j
[2025-08-19 11:44:08] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -86.226182-0.001006j
[2025-08-19 11:44:18] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -86.122744-0.011607j
[2025-08-19 11:44:27] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -86.218602-0.000908j
[2025-08-19 11:44:36] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -86.171124-0.001850j
[2025-08-19 11:44:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -86.153332-0.000586j
[2025-08-19 11:44:55] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -86.234588-0.011954j
[2025-08-19 11:45:04] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -86.325271+0.007589j
[2025-08-19 11:45:14] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -86.334361+0.001799j
[2025-08-19 11:45:23] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -86.367339-0.008821j
[2025-08-19 11:45:32] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -86.237555-0.015238j
[2025-08-19 11:45:41] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -86.275916-0.003716j
[2025-08-19 11:45:51] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -86.231159+0.017089j
[2025-08-19 11:46:00] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -86.132977+0.009467j
[2025-08-19 11:46:09] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -86.206693+0.039430j
[2025-08-19 11:46:19] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -86.175066-0.007571j
[2025-08-19 11:46:28] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -86.187437-0.021377j
[2025-08-19 11:46:37] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -86.276675-0.018208j
[2025-08-19 11:46:47] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -86.102526-0.004309j
[2025-08-19 11:46:56] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -86.256511-0.006769j
[2025-08-19 11:47:05] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -86.235625+0.020773j
[2025-08-19 11:47:14] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -86.236771+0.003416j
[2025-08-19 11:47:24] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -86.176597+0.020376j
[2025-08-19 11:47:33] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -86.340451+0.044971j
[2025-08-19 11:47:42] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -86.106640-0.030008j
[2025-08-19 11:47:52] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -86.230368-0.021049j
[2025-08-19 11:48:01] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -86.120429-0.026960j
[2025-08-19 11:48:10] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -86.309120-0.005558j
[2025-08-19 11:48:20] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -86.212516-0.017532j
[2025-08-19 11:48:29] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -86.279108+0.012774j
[2025-08-19 11:48:38] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -86.215959+0.011881j
[2025-08-19 11:48:48] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -86.128367+0.015305j
[2025-08-19 11:48:57] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -86.083955+0.022351j
[2025-08-19 11:49:06] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -86.027042-0.018062j
[2025-08-19 11:49:15] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -86.176599-0.002934j
[2025-08-19 11:49:25] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -86.184943-0.015197j
[2025-08-19 11:49:34] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -86.044708-0.012076j
[2025-08-19 11:49:43] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -86.182691-0.000246j
[2025-08-19 11:49:53] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -86.124215-0.004797j
[2025-08-19 11:50:02] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -86.195718-0.007358j
[2025-08-19 11:50:11] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -86.131839+0.009324j
[2025-08-19 11:50:21] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -86.002083-0.006876j
[2025-08-19 11:50:30] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -85.996030-0.009722j
[2025-08-19 11:50:39] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -85.991288+0.000765j
[2025-08-19 11:50:49] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -86.026657-0.017845j
[2025-08-19 11:50:58] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -86.106103+0.002514j
[2025-08-19 11:51:07] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -86.183218+0.011235j
[2025-08-19 11:51:16] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -86.239217+0.021689j
[2025-08-19 11:51:26] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -86.118241-0.005590j
[2025-08-19 11:51:35] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -86.205410-0.003687j
[2025-08-19 11:51:44] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -86.145671-0.011518j
[2025-08-19 11:51:54] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -86.084923-0.000326j
[2025-08-19 11:52:03] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -86.102397+0.005166j
[2025-08-19 11:52:12] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -86.175334+0.000806j
[2025-08-19 11:52:22] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -86.201688+0.023215j
[2025-08-19 11:52:31] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -86.191815+0.008237j
[2025-08-19 11:52:40] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -86.258320-0.010107j
[2025-08-19 11:52:50] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -86.238876+0.018748j
[2025-08-19 11:52:59] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -86.355418-0.005438j
[2025-08-19 11:53:08] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -86.408611+0.002196j
[2025-08-19 11:53:17] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -86.325128+0.010672j
[2025-08-19 11:53:27] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -86.275148+0.015748j
[2025-08-19 11:53:36] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -86.235108+0.024386j
[2025-08-19 11:53:45] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -86.083538+0.036324j
[2025-08-19 11:53:55] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -86.206762+0.063211j
[2025-08-19 11:54:04] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -86.124829+0.029880j
[2025-08-19 11:54:13] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -86.119047-0.045706j
[2025-08-19 11:54:23] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -86.024720+0.027065j
[2025-08-19 11:54:32] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -85.984896+0.067750j
[2025-08-19 11:54:41] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -86.043139+0.003161j
[2025-08-19 11:54:50] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -85.997186-0.012949j
[2025-08-19 11:55:00] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -85.946121+0.008396j
[2025-08-19 11:55:09] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -86.116809-0.046716j
[2025-08-19 11:55:18] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -86.174451-0.056803j
[2025-08-19 11:55:28] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -86.102630+0.045078j
[2025-08-19 11:55:37] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -86.159202+0.001197j
[2025-08-19 11:55:46] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -86.078471-0.009309j
[2025-08-19 11:55:56] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -86.217053-0.018393j
[2025-08-19 11:56:05] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -86.088975-0.036180j
[2025-08-19 11:56:14] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -86.145552-0.036552j
[2025-08-19 11:56:23] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -85.938908+0.020650j
[2025-08-19 11:56:33] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -86.008328-0.008067j
[2025-08-19 11:56:42] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -86.155597-0.001361j
[2025-08-19 11:56:51] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -86.145237+0.003766j
[2025-08-19 11:57:01] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -86.168125-0.041656j
[2025-08-19 11:57:01] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-19 11:57:10] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -86.192527-0.020947j
[2025-08-19 11:57:19] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -86.107957+0.011070j
[2025-08-19 11:57:29] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -86.153930+0.001310j
[2025-08-19 11:57:38] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -86.122493+0.026470j
[2025-08-19 11:57:47] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -86.089679+0.014013j
[2025-08-19 11:57:57] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -86.055135+0.029657j
[2025-08-19 11:58:06] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -86.040654+0.024194j
[2025-08-19 11:58:15] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -86.024129+0.013855j
[2025-08-19 11:58:24] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -86.143658+0.020734j
[2025-08-19 11:58:34] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -85.987347+0.046442j
[2025-08-19 11:58:43] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -86.015036+0.019342j
[2025-08-19 11:58:52] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -86.020315+0.021462j
[2025-08-19 11:59:02] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -86.114719+0.008975j
[2025-08-19 11:59:11] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -86.066407+0.025158j
[2025-08-19 11:59:20] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -86.106853+0.020758j
[2025-08-19 11:59:29] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -86.096784+0.016891j
[2025-08-19 11:59:39] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -86.134594-0.001298j
[2025-08-19 11:59:48] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -86.212253-0.001852j
[2025-08-19 11:59:57] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -86.226581-0.001614j
[2025-08-19 12:00:07] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -86.326823+0.014200j
[2025-08-19 12:00:16] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -86.171111+0.003226j
[2025-08-19 12:00:25] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -86.280482+0.007351j
[2025-08-19 12:00:35] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -86.192590-0.001901j
[2025-08-19 12:00:44] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -86.156573-0.000106j
[2025-08-19 12:00:53] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -86.074641-0.013781j
[2025-08-19 12:01:02] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -86.144789-0.004046j
[2025-08-19 12:01:12] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -86.146123+0.002828j
[2025-08-19 12:01:21] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -86.309470+0.000960j
[2025-08-19 12:01:30] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -86.049258+0.011082j
[2025-08-19 12:01:40] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -86.198391+0.006740j
[2025-08-19 12:01:49] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -86.075612+0.031259j
[2025-08-19 12:01:58] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -86.214076+0.018910j
[2025-08-19 12:02:08] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -86.135761+0.019634j
[2025-08-19 12:02:17] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -86.189261-0.031647j
[2025-08-19 12:02:26] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -86.158617+0.020017j
[2025-08-19 12:02:35] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -86.265594-0.030910j
[2025-08-19 12:02:45] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -86.221753+0.054782j
[2025-08-19 12:02:54] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -86.097101+0.006502j
[2025-08-19 12:03:03] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -86.200497-0.014211j
[2025-08-19 12:03:13] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -86.223849-0.008229j
[2025-08-19 12:03:22] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -86.182377-0.011728j
[2025-08-19 12:03:31] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -86.235650-0.003748j
[2025-08-19 12:03:41] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -86.256287+0.025369j
[2025-08-19 12:03:50] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -86.302193+0.006809j
[2025-08-19 12:03:59] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -86.270696-0.031183j
[2025-08-19 12:04:08] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -86.371956+0.028409j
[2025-08-19 12:04:18] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -86.243507-0.017950j
[2025-08-19 12:04:27] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -86.410965+0.002106j
[2025-08-19 12:04:36] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -86.393895-0.005239j
[2025-08-19 12:04:46] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -86.157168+0.028226j
[2025-08-19 12:04:55] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -86.142268+0.018434j
[2025-08-19 12:05:04] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -86.126419-0.020999j
[2025-08-19 12:05:14] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -86.016141-0.014560j
[2025-08-19 12:05:23] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -85.970777+0.003593j
[2025-08-19 12:05:32] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -86.135536+0.020796j
[2025-08-19 12:05:41] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -86.165738-0.003456j
[2025-08-19 12:05:51] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -86.201253+0.026021j
[2025-08-19 12:06:00] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -86.279338+0.009140j
[2025-08-19 12:06:09] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -86.348620-0.002428j
[2025-08-19 12:06:19] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -86.315716-0.009459j
[2025-08-19 12:06:28] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -86.228159-0.020010j
[2025-08-19 12:06:37] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -86.144134+0.010734j
[2025-08-19 12:06:46] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -86.032753-0.039346j
[2025-08-19 12:06:56] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -86.086118-0.029249j
[2025-08-19 12:07:05] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -86.250740+0.062054j
[2025-08-19 12:07:14] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -86.135480+0.009290j
[2025-08-19 12:07:24] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -86.118668-0.020604j
[2025-08-19 12:07:33] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -86.142837-0.025947j
[2025-08-19 12:07:42] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -86.146540-0.003611j
[2025-08-19 12:07:52] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -85.991533-0.019852j
[2025-08-19 12:08:01] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -86.164091-0.006553j
[2025-08-19 12:08:10] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -86.152102-0.015914j
[2025-08-19 12:08:20] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -86.146459+0.015346j
[2025-08-19 12:08:29] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -86.114494+0.024812j
[2025-08-19 12:08:38] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -86.165611+0.058581j
[2025-08-19 12:08:47] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -86.265910+0.008334j
[2025-08-19 12:08:57] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -86.407367+0.043995j
[2025-08-19 12:09:06] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -86.361718+0.050956j
[2025-08-19 12:09:15] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -86.329831+0.096238j
[2025-08-19 12:09:25] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -86.241239+0.032327j
[2025-08-19 12:09:34] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -86.147096+0.044402j
[2025-08-19 12:09:43] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -86.260419+0.035893j
[2025-08-19 12:09:53] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -86.321083+0.012297j
[2025-08-19 12:10:02] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -86.336999+0.029347j
[2025-08-19 12:10:11] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -86.282035+0.012836j
[2025-08-19 12:10:21] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -86.524155-0.004524j
[2025-08-19 12:10:30] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -86.142787-0.046685j
[2025-08-19 12:10:39] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -86.145443-0.011859j
[2025-08-19 12:10:48] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -86.134628-0.094023j
[2025-08-19 12:10:58] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -86.272774+0.005514j
[2025-08-19 12:11:07] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -86.036791-0.019352j
[2025-08-19 12:11:16] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -86.220871+0.004024j
[2025-08-19 12:11:26] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -86.100586-0.037071j
[2025-08-19 12:11:35] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -86.306866-0.016375j
[2025-08-19 12:11:44] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -86.319803+0.002006j
[2025-08-19 12:11:53] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -86.462541+0.002376j
[2025-08-19 12:12:03] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -86.331963+0.003600j
[2025-08-19 12:12:12] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -86.320956-0.038630j
[2025-08-19 12:12:21] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -86.311174+0.029745j
[2025-08-19 12:12:31] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -86.257078+0.022850j
[2025-08-19 12:12:31] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-19 12:12:40] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -86.245030-0.019380j
[2025-08-19 12:12:49] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -86.298822+0.013819j
[2025-08-19 12:12:59] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -86.177588-0.020490j
[2025-08-19 12:13:08] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -86.205935-0.003315j
[2025-08-19 12:13:17] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -86.195192+0.008885j
[2025-08-19 12:13:27] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -86.141635-0.002914j
[2025-08-19 12:13:36] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -86.294905+0.013453j
[2025-08-19 12:13:45] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -86.405457+0.005057j
[2025-08-19 12:13:54] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -86.391334-0.022845j
[2025-08-19 12:14:04] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -86.312688+0.002592j
[2025-08-19 12:14:13] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -86.469936-0.004340j
[2025-08-19 12:14:22] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -86.415053-0.007443j
[2025-08-19 12:14:32] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -86.329013-0.008969j
[2025-08-19 12:14:41] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -86.273192+0.005299j
[2025-08-19 12:14:50] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -86.209836+0.001500j
[2025-08-19 12:15:00] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -86.130897+0.005222j
[2025-08-19 12:15:09] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -86.329185+0.014113j
[2025-08-19 12:15:18] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -86.352128-0.006907j
[2025-08-19 12:15:27] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -86.276732+0.004003j
[2025-08-19 12:15:37] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -86.260481+0.016791j
[2025-08-19 12:15:46] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -86.086492-0.009243j
[2025-08-19 12:15:55] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -86.136009-0.009935j
[2025-08-19 12:16:05] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -86.137249-0.001887j
[2025-08-19 12:16:14] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -86.150786-0.008868j
[2025-08-19 12:16:23] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -86.326875-0.011062j
[2025-08-19 12:16:33] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -86.368539-0.005283j
[2025-08-19 12:16:42] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -86.339397+0.025631j
[2025-08-19 12:16:51] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -86.272443+0.028068j
[2025-08-19 12:17:00] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -86.329534-0.012469j
[2025-08-19 12:17:10] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -86.371277+0.017578j
[2025-08-19 12:17:19] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -86.273067-0.012006j
[2025-08-19 12:17:28] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -86.144661-0.001553j
[2025-08-19 12:17:38] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -86.178371-0.020392j
[2025-08-19 12:17:47] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -86.142654-0.029966j
[2025-08-19 12:17:56] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -86.166846-0.015377j
[2025-08-19 12:18:06] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -86.022625+0.010948j
[2025-08-19 12:18:15] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -86.096210-0.012055j
[2025-08-19 12:18:24] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -86.236882+0.015423j
[2025-08-19 12:18:33] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -86.272875+0.006697j
[2025-08-19 12:18:43] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -86.294372-0.018212j
[2025-08-19 12:18:52] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -86.323988+0.027149j
[2025-08-19 12:19:01] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -86.147016-0.006987j
[2025-08-19 12:19:11] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -86.228471+0.017829j
[2025-08-19 12:19:20] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -86.130931+0.007367j
[2025-08-19 12:19:29] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -86.313650-0.007144j
[2025-08-19 12:19:38] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -86.277475+0.015419j
[2025-08-19 12:19:48] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -86.274863+0.026734j
[2025-08-19 12:19:57] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -86.216097+0.062788j
[2025-08-19 12:20:06] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -86.095901-0.039498j
[2025-08-19 12:20:16] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -86.071959+0.011355j
[2025-08-19 12:20:25] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -86.132768-0.019438j
[2025-08-19 12:20:34] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -86.234423-0.016674j
[2025-08-19 12:20:44] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -86.137153-0.019195j
[2025-08-19 12:20:53] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -86.191174+0.014402j
[2025-08-19 12:21:02] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -86.191741-0.016161j
[2025-08-19 12:21:11] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -86.130756+0.005261j
[2025-08-19 12:21:21] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -86.104629-0.000727j
[2025-08-19 12:21:30] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -86.058516+0.012562j
[2025-08-19 12:21:39] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -86.045905-0.023553j
[2025-08-19 12:21:49] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -86.045346-0.017910j
[2025-08-19 12:21:58] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -86.155820-0.013045j
[2025-08-19 12:22:07] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -86.108114-0.034188j
[2025-08-19 12:22:17] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -86.169450+0.005672j
[2025-08-19 12:22:26] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -86.258612-0.006938j
[2025-08-19 12:22:35] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -86.061601-0.014634j
[2025-08-19 12:22:44] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -86.322011+0.007515j
[2025-08-19 12:22:54] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -86.337918-0.004475j
[2025-08-19 12:23:03] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -86.278369-0.003760j
[2025-08-19 12:23:12] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -86.367125+0.002941j
[2025-08-19 12:23:22] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -86.285923-0.017048j
[2025-08-19 12:23:31] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -86.213093-0.010368j
[2025-08-19 12:23:40] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -86.135176-0.006128j
[2025-08-19 12:23:50] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -86.146126-0.017838j
[2025-08-19 12:23:59] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -86.172042+0.015768j
[2025-08-19 12:24:08] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -86.142859-0.025952j
[2025-08-19 12:24:17] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -86.191456+0.013754j
[2025-08-19 12:24:27] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -86.140419+0.009379j
[2025-08-19 12:24:36] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -86.157549+0.015160j
[2025-08-19 12:24:45] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -86.263485-0.011769j
[2025-08-19 12:24:55] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -86.074976-0.009527j
[2025-08-19 12:25:04] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -86.036745+0.004462j
[2025-08-19 12:25:13] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -86.098253-0.065089j
[2025-08-19 12:25:23] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -86.067806+0.007992j
[2025-08-19 12:25:32] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -86.098333-0.009399j
[2025-08-19 12:25:41] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -86.057665+0.001567j
[2025-08-19 12:25:50] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -86.073199+0.012393j
[2025-08-19 12:26:00] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -86.101848-0.003444j
[2025-08-19 12:26:09] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -86.071880+0.020210j
[2025-08-19 12:26:18] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -86.008694-0.011227j
[2025-08-19 12:26:28] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -86.071528+0.008234j
[2025-08-19 12:26:37] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -86.095484-0.011616j
[2025-08-19 12:26:46] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -86.032996+0.019220j
[2025-08-19 12:26:56] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -86.024095-0.005971j
[2025-08-19 12:27:05] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -86.100945-0.003741j
[2025-08-19 12:27:14] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -86.181660-0.021380j
[2025-08-19 12:27:24] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -86.230787-0.003031j
[2025-08-19 12:27:33] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -86.214536-0.001710j
[2025-08-19 12:27:42] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -86.296913+0.000632j
[2025-08-19 12:27:52] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -86.122935-0.013850j
[2025-08-19 12:28:01] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -86.144291-0.015658j
[2025-08-19 12:28:01] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-19 12:28:10] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -86.121428-0.030788j
[2025-08-19 12:28:19] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -86.226433-0.025539j
[2025-08-19 12:28:29] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -86.257359-0.040525j
[2025-08-19 12:28:38] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -86.187324-0.015705j
[2025-08-19 12:28:47] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -86.195606+0.024857j
[2025-08-19 12:28:57] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -86.329822-0.013894j
[2025-08-19 12:29:06] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -86.200604+0.032280j
[2025-08-19 12:29:15] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -86.269140-0.025880j
[2025-08-19 12:29:25] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -86.193869+0.008187j
[2025-08-19 12:29:34] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -86.049164-0.019230j
[2025-08-19 12:29:43] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -86.135874-0.018745j
[2025-08-19 12:29:53] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -86.143896-0.018737j
[2025-08-19 12:30:02] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -86.105324-0.002133j
[2025-08-19 12:30:11] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -85.994503+0.000384j
[2025-08-19 12:30:20] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -85.959148+0.036917j
[2025-08-19 12:30:30] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -85.990442-0.008531j
[2025-08-19 12:30:39] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -85.979330+0.006007j
[2025-08-19 12:30:48] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -85.972095+0.001337j
[2025-08-19 12:30:58] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -85.997245+0.011215j
[2025-08-19 12:31:07] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -86.029329-0.003535j
[2025-08-19 12:31:16] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -86.044830-0.004341j
[2025-08-19 12:31:26] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -86.077534-0.037412j
[2025-08-19 12:31:35] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -86.127290+0.020417j
[2025-08-19 12:31:44] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -86.075590-0.029529j
[2025-08-19 12:31:53] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -86.112926-0.007584j
[2025-08-19 12:32:03] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -86.269300-0.001575j
[2025-08-19 12:32:12] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -86.182700-0.005257j
[2025-08-19 12:32:21] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -86.254449-0.008764j
[2025-08-19 12:32:31] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -86.177021-0.028881j
[2025-08-19 12:32:40] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -86.131545+0.000123j
[2025-08-19 12:32:49] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -86.116246-0.027064j
[2025-08-19 12:32:59] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -86.244600+0.000811j
[2025-08-19 12:33:08] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -86.092820-0.011566j
[2025-08-19 12:33:17] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -86.140662-0.001236j
[2025-08-19 12:33:26] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -86.038515-0.007244j
[2025-08-19 12:33:36] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -86.211033+0.008477j
[2025-08-19 12:33:45] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -86.216352-0.015016j
[2025-08-19 12:33:54] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -86.000943-0.002034j
[2025-08-19 12:34:04] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -86.041385+0.001263j
[2025-08-19 12:34:13] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -86.033330+0.001590j
[2025-08-19 12:34:22] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -86.155902+0.018592j
[2025-08-19 12:34:32] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -86.173843+0.002089j
[2025-08-19 12:34:41] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -86.175008-0.013479j
[2025-08-19 12:34:50] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -86.347916+0.020667j
[2025-08-19 12:35:00] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -86.097739+0.003930j
[2025-08-19 12:35:09] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -86.275820-0.009140j
[2025-08-19 12:35:18] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -86.273682-0.014649j
[2025-08-19 12:35:27] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -86.293390+0.001301j
[2025-08-19 12:35:37] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -86.302881-0.018219j
[2025-08-19 12:35:46] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -86.250020-0.005332j
[2025-08-19 12:35:55] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -86.185488-0.013854j
[2025-08-19 12:36:05] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -86.188853-0.016062j
[2025-08-19 12:36:14] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -86.249560-0.017585j
[2025-08-19 12:36:23] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -86.256709-0.025359j
[2025-08-19 12:36:33] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -86.116307-0.002195j
[2025-08-19 12:36:42] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -86.165607-0.021015j
[2025-08-19 12:36:51] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -86.256915-0.014422j
[2025-08-19 12:37:00] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -86.250681-0.008619j
[2025-08-19 12:37:10] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -86.266424-0.005458j
[2025-08-19 12:37:19] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -86.204459+0.003254j
[2025-08-19 12:37:28] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -86.236911-0.006890j
[2025-08-19 12:37:38] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -86.136787-0.000750j
[2025-08-19 12:37:47] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -86.139719-0.012316j
[2025-08-19 12:37:56] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -85.999865-0.010499j
[2025-08-19 12:38:06] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -86.009952-0.004445j
[2025-08-19 12:38:15] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -86.063392+0.003157j
[2025-08-19 12:38:24] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -86.120418-0.013342j
[2025-08-19 12:38:34] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -86.044420+0.008284j
[2025-08-19 12:38:43] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -86.113906-0.022655j
[2025-08-19 12:38:52] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -86.055022+0.001500j
[2025-08-19 12:39:02] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -86.105553-0.025491j
[2025-08-19 12:39:11] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -86.076270-0.014213j
[2025-08-19 12:39:20] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -86.007097+0.005614j
[2025-08-19 12:39:30] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -86.129314-0.011676j
[2025-08-19 12:39:39] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -86.117589-0.047776j
[2025-08-19 12:39:48] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -85.894461-0.012051j
[2025-08-19 12:39:57] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -86.107540-0.045777j
[2025-08-19 12:40:07] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -86.211425-0.014382j
[2025-08-19 12:40:16] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -86.096674-0.016109j
[2025-08-19 12:40:25] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -86.199542-0.013256j
[2025-08-19 12:40:35] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -86.181348+0.026708j
[2025-08-19 12:40:44] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -86.127382-0.030905j
[2025-08-19 12:40:53] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -86.054599-0.024682j
[2025-08-19 12:41:03] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -86.038933+0.013801j
[2025-08-19 12:41:12] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -86.194866+0.023485j
[2025-08-19 12:41:21] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -86.129818+0.021566j
[2025-08-19 12:41:31] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -86.229583+0.049642j
[2025-08-19 12:41:40] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -86.277378+0.039479j
[2025-08-19 12:41:49] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -86.430016-0.022083j
[2025-08-19 12:41:59] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -86.291652-0.010616j
[2025-08-19 12:42:08] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -86.076491+0.006307j
[2025-08-19 12:42:17] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -86.166159-0.006508j
[2025-08-19 12:42:26] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -86.192123-0.025589j
[2025-08-19 12:42:36] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -86.239907-0.006769j
[2025-08-19 12:42:45] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -86.063341-0.010624j
[2025-08-19 12:42:54] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -86.206916+0.003184j
[2025-08-19 12:43:04] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -86.028561+0.000089j
[2025-08-19 12:43:13] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -86.177411-0.029821j
[2025-08-19 12:43:22] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -86.259527+0.007880j
[2025-08-19 12:43:32] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -86.369420-0.024860j
[2025-08-19 12:43:32] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-19 12:43:41] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -86.259095+0.032362j
[2025-08-19 12:43:50] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -86.279436-0.018903j
[2025-08-19 12:44:00] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -86.256192+0.024656j
[2025-08-19 12:44:09] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -86.237599+0.007734j
[2025-08-19 12:44:18] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -85.938333+0.036360j
[2025-08-19 12:44:28] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -86.079306-0.019531j
[2025-08-19 12:44:37] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -86.267567+0.010482j
[2025-08-19 12:44:46] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -86.191777+0.001287j
[2025-08-19 12:44:55] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -86.114983-0.004505j
[2025-08-19 12:45:05] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -85.947465+0.026215j
[2025-08-19 12:45:14] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -86.055648+0.112742j
[2025-08-19 12:45:23] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -86.035808+0.111679j
[2025-08-19 12:45:33] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -86.131775+0.090088j
[2025-08-19 12:45:42] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -86.255164+0.094770j
[2025-08-19 12:45:51] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -86.248920+0.037450j
[2025-08-19 12:46:01] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -86.055825+0.020128j
[2025-08-19 12:46:10] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -86.035201+0.021467j
[2025-08-19 12:46:19] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -86.162423+0.026522j
[2025-08-19 12:46:29] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -86.093942+0.006029j
[2025-08-19 12:46:38] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -86.032454+0.020729j
[2025-08-19 12:46:47] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -86.111918+0.028992j
[2025-08-19 12:46:57] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -86.166769-0.026500j
[2025-08-19 12:47:06] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -86.199530-0.015917j
[2025-08-19 12:47:15] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -86.215383+0.002653j
[2025-08-19 12:47:24] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -86.126582+0.005383j
[2025-08-19 12:47:34] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -85.959237+0.007336j
[2025-08-19 12:47:43] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -85.879225+0.010107j
[2025-08-19 12:47:52] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -85.917742+0.009976j
[2025-08-19 12:48:02] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -85.977648-0.000160j
[2025-08-19 12:48:11] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -86.020910+0.006999j
[2025-08-19 12:48:20] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -86.041147+0.015746j
[2025-08-19 12:48:30] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -86.075219+0.002797j
[2025-08-19 12:48:39] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -86.041857-0.008968j
[2025-08-19 12:48:48] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -86.016245-0.008421j
[2025-08-19 12:48:57] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -86.060143-0.008439j
[2025-08-19 12:49:07] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -86.135613-0.033500j
[2025-08-19 12:49:16] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -86.226044+0.009123j
[2025-08-19 12:49:25] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -86.077289-0.006976j
[2025-08-19 12:49:35] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -86.095195-0.018174j
[2025-08-19 12:49:44] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -86.090945+0.017364j
[2025-08-19 12:49:53] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -86.179952+0.017027j
[2025-08-19 12:50:03] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -86.091384+0.015454j
[2025-08-19 12:50:12] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -86.238004-0.007865j
[2025-08-19 12:50:21] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -86.333181-0.031012j
[2025-08-19 12:50:31] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -86.286425+0.008160j
[2025-08-19 12:50:40] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -86.250955-0.004783j
[2025-08-19 12:50:49] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -86.106096-0.021688j
[2025-08-19 12:50:59] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -86.099422+0.003669j
[2025-08-19 12:51:08] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -86.270436-0.019077j
[2025-08-19 12:51:17] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -86.275613+0.010900j
[2025-08-19 12:51:26] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -86.214804-0.008642j
[2025-08-19 12:51:36] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -86.318872+0.000879j
[2025-08-19 12:51:45] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -86.230241-0.019906j
[2025-08-19 12:51:54] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -86.377768+0.018807j
[2025-08-19 12:52:04] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -86.304973-0.005459j
[2025-08-19 12:52:13] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -86.273881+0.016522j
[2025-08-19 12:52:22] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -86.214284+0.056434j
[2025-08-19 12:52:31] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -86.222082+0.021272j
[2025-08-19 12:52:41] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -86.198486+0.011135j
[2025-08-19 12:52:50] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -86.396469-0.000634j
[2025-08-19 12:52:59] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -86.279949+0.001790j
[2025-08-19 12:53:09] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -86.283436+0.005860j
[2025-08-19 12:53:18] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -86.322361+0.016121j
[2025-08-19 12:53:27] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -86.209350+0.019228j
[2025-08-19 12:53:37] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -86.307176+0.000264j
[2025-08-19 12:53:46] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -86.290977-0.012917j
[2025-08-19 12:53:55] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -86.319276+0.010616j
[2025-08-19 12:54:04] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -86.352158+0.001683j
[2025-08-19 12:54:14] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -86.274079+0.016600j
[2025-08-19 12:54:23] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -86.304949+0.013739j
[2025-08-19 12:54:32] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -86.199556+0.006240j
[2025-08-19 12:54:42] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -86.391249-0.006372j
[2025-08-19 12:54:51] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -86.315348-0.003272j
[2025-08-19 12:55:00] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -86.236673-0.019506j
[2025-08-19 12:55:10] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -86.296324-0.019059j
[2025-08-19 12:55:19] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -86.296494-0.001684j
[2025-08-19 12:55:28] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -86.345360-0.004271j
[2025-08-19 12:55:37] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -86.475747-0.002626j
[2025-08-19 12:55:47] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -86.369743+0.004776j
[2025-08-19 12:55:56] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -86.438439-0.021736j
[2025-08-19 12:56:05] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -86.330593+0.022694j
[2025-08-19 12:56:15] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -86.467080+0.020902j
[2025-08-19 12:56:24] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -86.306162-0.057518j
[2025-08-19 12:56:33] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -86.331725-0.012616j
[2025-08-19 12:56:43] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -86.268449+0.011064j
[2025-08-19 12:56:52] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -86.335087-0.002227j
[2025-08-19 12:57:01] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -86.361470+0.027427j
[2025-08-19 12:57:11] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -86.335368+0.006652j
[2025-08-19 12:57:20] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -86.231667+0.022218j
[2025-08-19 12:57:29] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -86.284663-0.003206j
[2025-08-19 12:57:38] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -86.221188+0.006462j
[2025-08-19 12:57:48] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -86.287880+0.002545j
[2025-08-19 12:57:57] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -86.285003+0.009385j
[2025-08-19 12:58:06] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -86.100072-0.000723j
[2025-08-19 12:58:16] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -86.178006+0.010069j
[2025-08-19 12:58:25] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -86.090432-0.006584j
[2025-08-19 12:58:34] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -86.178646-0.014539j
[2025-08-19 12:58:44] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -86.195937-0.018109j
[2025-08-19 12:58:53] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -86.162737-0.041802j
[2025-08-19 12:59:02] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -86.047002+0.016005j
[2025-08-19 12:59:02] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-19 12:59:11] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -86.183097-0.023277j
[2025-08-19 12:59:21] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -86.242093-0.016921j
[2025-08-19 12:59:30] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -86.424970+0.018209j
[2025-08-19 12:59:39] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -86.262864+0.001605j
[2025-08-19 12:59:49] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -86.149738+0.030509j
[2025-08-19 12:59:58] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -86.205148+0.040644j
[2025-08-19 13:00:07] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -86.203899+0.006854j
[2025-08-19 13:00:16] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -86.189803-0.007975j
[2025-08-19 13:00:26] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -86.095413+0.007546j
[2025-08-19 13:00:35] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -86.147962+0.001837j
[2025-08-19 13:00:44] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -86.105206-0.001076j
[2025-08-19 13:00:54] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -86.120937+0.005562j
[2025-08-19 13:01:03] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -86.149409-0.002221j
[2025-08-19 13:01:12] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -86.045168-0.007767j
[2025-08-19 13:01:22] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -86.054253-0.023347j
[2025-08-19 13:01:31] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -86.133881-0.005159j
[2025-08-19 13:01:40] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -86.180763-0.003395j
[2025-08-19 13:01:49] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -86.187464-0.019566j
[2025-08-19 13:01:59] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -86.162565-0.012162j
[2025-08-19 13:02:08] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -86.267321-0.009388j
[2025-08-19 13:02:17] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -86.299950-0.002386j
[2025-08-19 13:02:27] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -86.195420-0.002438j
[2025-08-19 13:02:36] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -86.272853+0.025168j
[2025-08-19 13:02:45] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -86.255917-0.012737j
[2025-08-19 13:02:55] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -86.265467+0.012729j
[2025-08-19 13:03:04] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -86.242249-0.012216j
[2025-08-19 13:03:13] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -86.335000-0.005045j
[2025-08-19 13:03:23] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -86.194146+0.029566j
[2025-08-19 13:03:32] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -86.207023-0.052074j
[2025-08-19 13:03:41] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -86.172040+0.013168j
[2025-08-19 13:03:50] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -86.146997-0.017529j
[2025-08-19 13:04:00] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -86.143289+0.019024j
[2025-08-19 13:04:09] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -86.161291-0.024258j
[2025-08-19 13:04:18] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -86.178553+0.006196j
[2025-08-19 13:04:28] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -86.138185+0.047203j
[2025-08-19 13:04:37] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -86.228019+0.014263j
[2025-08-19 13:04:46] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -86.172371+0.039282j
[2025-08-19 13:04:55] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -86.322491+0.006502j
[2025-08-19 13:05:05] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -86.339538-0.032668j
[2025-08-19 13:05:14] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -86.345495-0.003002j
[2025-08-19 13:05:23] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -86.186063+0.016226j
[2025-08-19 13:05:33] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -86.307719-0.002798j
[2025-08-19 13:05:42] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -86.230561-0.008076j
[2025-08-19 13:05:51] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -86.160934-0.000897j
[2025-08-19 13:06:01] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -86.186904+0.003018j
[2025-08-19 13:06:10] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -86.097008-0.003682j
[2025-08-19 13:06:19] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -86.148706-0.001791j
[2025-08-19 13:06:29] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -86.161457-0.010472j
[2025-08-19 13:06:38] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -86.204880+0.017933j
[2025-08-19 13:06:47] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -86.162060+0.000699j
[2025-08-19 13:06:47] ✅ Training completed | Restarts: 2
[2025-08-19 13:06:47] ============================================================
[2025-08-19 13:06:47] Training completed | Runtime: 9802.5s
[2025-08-19 13:07:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-19 13:07:01] ============================================================
