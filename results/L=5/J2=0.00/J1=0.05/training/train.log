[2025-08-18 23:49:26] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-18 23:49:26]   - 迭代次数: final
[2025-08-18 23:49:26]   - 能量: -85.025512+0.000057j ± 0.055059
[2025-08-18 23:49:26]   - 时间戳: 2025-07-30T23:38:53.138432
[2025-08-18 23:49:34] ✓ 变分状态参数已从checkpoint恢复
[2025-08-18 23:49:34] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-18 23:49:34] ==================================================
[2025-08-18 23:49:34] GCNN for Shastry-Sutherland Model
[2025-08-18 23:49:34] ==================================================
[2025-08-18 23:49:34] System parameters:
[2025-08-18 23:49:34]   - System size: L=5, N=100
[2025-08-18 23:49:34]   - System parameters: J1=0.05, J2=0.0, Q=1.0
[2025-08-18 23:49:34] --------------------------------------------------
[2025-08-18 23:49:34] Model parameters:
[2025-08-18 23:49:34]   - Number of layers = 4
[2025-08-18 23:49:34]   - Number of features = 4
[2025-08-18 23:49:34]   - Total parameters = 19628
[2025-08-18 23:49:34] --------------------------------------------------
[2025-08-18 23:49:34] Training parameters:
[2025-08-18 23:49:34]   - Learning rate: 0.015
[2025-08-18 23:49:34]   - Total iterations: 1050
[2025-08-18 23:49:34]   - Annealing cycles: 3
[2025-08-18 23:49:34]   - Initial period: 150
[2025-08-18 23:49:34]   - Period multiplier: 2.0
[2025-08-18 23:49:34]   - Temperature range: 0.0-1.0
[2025-08-18 23:49:34]   - Samples: 4096
[2025-08-18 23:49:34]   - Discarded samples: 0
[2025-08-18 23:49:34]   - Chunk size: 2048
[2025-08-18 23:49:34]   - Diagonal shift: 0.2
[2025-08-18 23:49:34]   - Gradient clipping: 1.0
[2025-08-18 23:49:34]   - Checkpoint enabled: interval=100
[2025-08-18 23:49:34]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.05/training/checkpoints
[2025-08-18 23:49:34] --------------------------------------------------
[2025-08-18 23:49:34] Device status:
[2025-08-18 23:49:34]   - Devices model: A100
[2025-08-18 23:49:34]   - Number of devices: 1
[2025-08-18 23:49:34]   - Sharding: True
[2025-08-18 23:49:34] ============================================================
[2025-08-18 23:50:07] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -85.560391+0.043007j
[2025-08-18 23:50:28] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -85.832270+0.029451j
[2025-08-18 23:50:38] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -85.631831+0.055426j
[2025-08-18 23:50:47] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -85.796023+0.052087j
[2025-08-18 23:50:57] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -85.847274+0.011707j
[2025-08-18 23:51:06] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -85.708193-0.008009j
[2025-08-18 23:51:15] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -85.665885-0.016620j
[2025-08-18 23:51:25] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -85.676448-0.028453j
[2025-08-18 23:51:34] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -85.759964-0.025689j
[2025-08-18 23:51:43] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -85.822782-0.007764j
[2025-08-18 23:51:53] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -85.640641+0.014582j
[2025-08-18 23:52:02] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -85.653977-0.031008j
[2025-08-18 23:52:12] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -85.569366-0.004109j
[2025-08-18 23:52:21] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -85.683715-0.003277j
[2025-08-18 23:52:30] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -85.722704-0.023422j
[2025-08-18 23:52:40] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -85.743239-0.013024j
[2025-08-18 23:52:49] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -85.887761-0.001643j
[2025-08-18 23:52:58] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -85.783581+0.002750j
[2025-08-18 23:53:08] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -85.849008-0.015299j
[2025-08-18 23:53:17] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -85.773224-0.024766j
[2025-08-18 23:53:27] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -85.846791+0.015648j
[2025-08-18 23:53:36] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -85.656511+0.000273j
[2025-08-18 23:53:45] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -85.793978-0.016005j
[2025-08-18 23:53:55] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -85.672882+0.011009j
[2025-08-18 23:54:04] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -85.692555+0.010482j
[2025-08-18 23:54:14] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -85.521188-0.011258j
[2025-08-18 23:54:23] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -85.718614+0.046372j
[2025-08-18 23:54:32] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -85.571790-0.028196j
[2025-08-18 23:54:42] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -85.627699-0.034610j
[2025-08-18 23:54:51] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -85.593539-0.002026j
[2025-08-18 23:55:00] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -85.674300-0.026914j
[2025-08-18 23:55:10] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -85.681212-0.010120j
[2025-08-18 23:55:19] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -85.520336+0.016196j
[2025-08-18 23:55:29] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -85.625335+0.000980j
[2025-08-18 23:55:38] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -85.627940+0.012635j
[2025-08-18 23:55:47] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -85.594320+0.018543j
[2025-08-18 23:55:57] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -85.622862+0.000392j
[2025-08-18 23:56:06] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -85.516501-0.009766j
[2025-08-18 23:56:16] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -85.475898-0.007532j
[2025-08-18 23:56:25] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -85.502535-0.010588j
[2025-08-18 23:56:34] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -85.520149+0.011157j
[2025-08-18 23:56:44] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -85.366937+0.009245j
[2025-08-18 23:56:53] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -85.577687-0.021156j
[2025-08-18 23:57:02] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -85.393695+0.005955j
[2025-08-18 23:57:12] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -85.476609-0.019541j
[2025-08-18 23:57:21] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -85.574531-0.016752j
[2025-08-18 23:57:31] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -85.584606-0.003736j
[2025-08-18 23:57:40] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -85.578566-0.013801j
[2025-08-18 23:57:49] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -85.509100-0.018643j
[2025-08-18 23:57:59] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -85.540530+0.000262j
[2025-08-18 23:58:08] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -85.562849+0.083109j
[2025-08-18 23:58:18] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -85.255565-0.005556j
[2025-08-18 23:58:27] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -85.539832+0.021679j
[2025-08-18 23:58:36] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -85.604060+0.010801j
[2025-08-18 23:58:46] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -85.631129+0.025631j
[2025-08-18 23:58:55] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -85.541674+0.012638j
[2025-08-18 23:59:04] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -85.509647-0.003433j
[2025-08-18 23:59:14] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -85.604938-0.007946j
[2025-08-18 23:59:23] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -85.588200+0.038886j
[2025-08-18 23:59:33] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -85.712757+0.022296j
[2025-08-18 23:59:42] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -85.622261-0.013282j
[2025-08-18 23:59:51] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -85.616789+0.022273j
[2025-08-19 00:00:01] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -85.646250+0.021725j
[2025-08-19 00:00:10] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -85.529804-0.016197j
[2025-08-19 00:00:20] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -85.380790-0.014255j
[2025-08-19 00:00:29] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -85.485506+0.013499j
[2025-08-19 00:00:38] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -85.520586+0.005858j
[2025-08-19 00:00:48] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -85.479799-0.008263j
[2025-08-19 00:00:57] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -85.565227-0.003516j
[2025-08-19 00:01:07] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -85.459049-0.000391j
[2025-08-19 00:01:16] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -85.640944+0.014091j
[2025-08-19 00:01:25] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -85.796922+0.001270j
[2025-08-19 00:01:35] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -85.830311-0.000083j
[2025-08-19 00:01:44] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -85.787216+0.003420j
[2025-08-19 00:01:53] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -85.872294+0.012540j
[2025-08-19 00:02:03] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -85.838558-0.005531j
[2025-08-19 00:02:12] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -85.719173+0.002467j
[2025-08-19 00:02:22] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -85.766004-0.006849j
[2025-08-19 00:02:31] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -85.725252-0.024138j
[2025-08-19 00:02:41] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -85.725496-0.005021j
[2025-08-19 00:02:50] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -85.602752+0.007182j
[2025-08-19 00:02:59] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -85.783590+0.000984j
[2025-08-19 00:03:09] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -85.824715-0.016218j
[2025-08-19 00:03:18] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -85.630633-0.003938j
[2025-08-19 00:03:28] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -85.577957-0.010163j
[2025-08-19 00:03:37] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -85.671306+0.019429j
[2025-08-19 00:03:46] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -85.625654+0.036728j
[2025-08-19 00:03:56] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -85.583218-0.002877j
[2025-08-19 00:04:05] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -85.597548-0.035646j
[2025-08-19 00:04:14] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -85.554429-0.003331j
[2025-08-19 00:04:24] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -85.496739+0.014602j
[2025-08-19 00:04:33] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -85.518350+0.000786j
[2025-08-19 00:04:43] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -85.452032+0.023925j
[2025-08-19 00:04:52] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -85.505067-0.018091j
[2025-08-19 00:05:01] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -85.465712-0.005417j
[2025-08-19 00:05:11] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -85.372290+0.007873j
[2025-08-19 00:05:20] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -85.537944-0.042432j
[2025-08-19 00:05:30] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -85.500373-0.015151j
[2025-08-19 00:05:39] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -85.396690-0.019813j
[2025-08-19 00:05:49] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -85.559830-0.003694j
[2025-08-19 00:05:49] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-19 00:05:58] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -85.539251-0.038728j
[2025-08-19 00:06:07] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -85.565711+0.016014j
[2025-08-19 00:06:17] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -85.621305-0.011570j
[2025-08-19 00:06:26] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -85.532087+0.035519j
[2025-08-19 00:06:35] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -85.565074+0.001051j
[2025-08-19 00:06:45] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -85.417555+0.002818j
[2025-08-19 00:06:54] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -85.483580-0.002978j
[2025-08-19 00:07:04] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -85.439675+0.004227j
[2025-08-19 00:07:13] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -85.506908+0.008990j
[2025-08-19 00:07:22] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -85.532408+0.015268j
[2025-08-19 00:07:32] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -85.742230+0.006851j
[2025-08-19 00:07:41] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -85.609479-0.000857j
[2025-08-19 00:07:51] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -85.433265-0.035424j
[2025-08-19 00:08:00] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -85.434655+0.008453j
[2025-08-19 00:08:09] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -85.363771-0.007283j
[2025-08-19 00:08:19] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -85.414576-0.001590j
[2025-08-19 00:08:28] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -85.454222-0.013628j
[2025-08-19 00:08:38] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -85.499036-0.002234j
[2025-08-19 00:08:47] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -85.657397-0.010014j
[2025-08-19 00:08:56] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -85.608376-0.012475j
[2025-08-19 00:09:06] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -85.805223-0.002661j
[2025-08-19 00:09:15] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -85.848867-0.005092j
[2025-08-19 00:09:24] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -85.813573-0.011217j
[2025-08-19 00:09:34] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -85.695097+0.000601j
[2025-08-19 00:09:43] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -85.703791-0.015775j
[2025-08-19 00:09:53] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -85.748829-0.006185j
[2025-08-19 00:10:02] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -85.707833+0.010882j
[2025-08-19 00:10:11] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -85.633824-0.003623j
[2025-08-19 00:10:21] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -85.823777-0.007937j
[2025-08-19 00:10:30] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -85.794810-0.009447j
[2025-08-19 00:10:40] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -85.677971-0.011433j
[2025-08-19 00:10:49] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -85.828424+0.018361j
[2025-08-19 00:10:58] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -85.688040-0.006011j
[2025-08-19 00:11:08] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -85.729819+0.002597j
[2025-08-19 00:11:17] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -85.608205+0.007560j
[2025-08-19 00:11:27] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -85.634174+0.001081j
[2025-08-19 00:11:36] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -85.635685-0.008886j
[2025-08-19 00:11:45] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -85.629965-0.017343j
[2025-08-19 00:11:55] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -85.764874-0.017973j
[2025-08-19 00:12:04] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -85.700275-0.009763j
[2025-08-19 00:12:14] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -85.645217+0.019771j
[2025-08-19 00:12:23] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -85.648223-0.009266j
[2025-08-19 00:12:32] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -85.694689+0.015997j
[2025-08-19 00:12:42] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -85.739314-0.019601j
[2025-08-19 00:12:51] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -85.673840+0.000789j
[2025-08-19 00:13:00] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -85.599303+0.004173j
[2025-08-19 00:13:10] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -85.678183-0.015537j
[2025-08-19 00:13:19] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -85.538615+0.000537j
[2025-08-19 00:13:29] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -85.532460-0.018222j
[2025-08-19 00:13:38] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -85.504228-0.029610j
[2025-08-19 00:13:38] RESTART #1 | Period: 300
[2025-08-19 00:13:47] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -85.298007-0.038452j
[2025-08-19 00:13:57] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -85.495031-0.060336j
[2025-08-19 00:14:06] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -85.533574-0.032758j
[2025-08-19 00:14:16] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -85.558476-0.013283j
[2025-08-19 00:14:25] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -85.498460+0.003474j
[2025-08-19 00:14:34] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -85.507219+0.011092j
[2025-08-19 00:14:44] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -85.523695-0.019300j
[2025-08-19 00:14:53] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -85.391705+0.010849j
[2025-08-19 00:15:03] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -85.375779+0.003157j
[2025-08-19 00:15:12] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -85.428274+0.041025j
[2025-08-19 00:15:21] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -85.309729-0.012681j
[2025-08-19 00:15:31] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -85.459920-0.008748j
[2025-08-19 00:15:40] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -85.424635+0.006310j
[2025-08-19 00:15:50] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -85.498081+0.010794j
[2025-08-19 00:15:59] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -85.679029-0.014504j
[2025-08-19 00:16:08] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -85.599558-0.025038j
[2025-08-19 00:16:18] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -85.607402-0.014894j
[2025-08-19 00:16:27] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -85.532466-0.004745j
[2025-08-19 00:16:37] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -85.682758+0.030439j
[2025-08-19 00:16:46] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -85.579159+0.011351j
[2025-08-19 00:16:55] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -85.551007+0.051884j
[2025-08-19 00:17:05] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -85.568240+0.017895j
[2025-08-19 00:17:14] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -85.488444+0.003633j
[2025-08-19 00:17:23] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -85.600802-0.002125j
[2025-08-19 00:17:33] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -85.440320-0.006264j
[2025-08-19 00:17:42] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -85.469589-0.013368j
[2025-08-19 00:17:52] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -85.438812+0.000333j
[2025-08-19 00:18:01] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -85.393664+0.006535j
[2025-08-19 00:18:11] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -85.399432+0.041404j
[2025-08-19 00:18:20] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -85.509415+0.008480j
[2025-08-19 00:18:29] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -85.524500-0.000053j
[2025-08-19 00:18:39] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -85.738852-0.025406j
[2025-08-19 00:18:48] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -85.622404+0.023976j
[2025-08-19 00:18:57] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -85.796667+0.005782j
[2025-08-19 00:19:07] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -85.677971+0.005367j
[2025-08-19 00:19:16] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -85.628105-0.005060j
[2025-08-19 00:19:26] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -85.552336+0.015757j
[2025-08-19 00:19:35] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -85.590609+0.048627j
[2025-08-19 00:19:44] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -85.576051-0.011311j
[2025-08-19 00:19:54] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -85.637824+0.007180j
[2025-08-19 00:20:03] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -85.697234+0.001791j
[2025-08-19 00:20:13] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -85.803932+0.010415j
[2025-08-19 00:20:22] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -85.630418-0.005103j
[2025-08-19 00:20:31] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -85.739727+0.018172j
[2025-08-19 00:20:41] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -85.811007-0.000197j
[2025-08-19 00:20:50] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -85.829817+0.005719j
[2025-08-19 00:21:00] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -85.883377+0.001371j
[2025-08-19 00:21:09] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -85.911316-0.013639j
[2025-08-19 00:21:18] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -85.886416-0.006041j
[2025-08-19 00:21:28] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -85.888398-0.000732j
[2025-08-19 00:21:28] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-19 00:21:37] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -85.927099+0.001728j
[2025-08-19 00:21:46] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -85.683211+0.007238j
[2025-08-19 00:21:56] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -85.646742+0.009451j
[2025-08-19 00:22:05] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -85.635902+0.022924j
[2025-08-19 00:22:15] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -85.659260+0.009101j
[2025-08-19 00:22:24] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -85.430164-0.000576j
[2025-08-19 00:22:33] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -85.489324+0.020153j
[2025-08-19 00:22:43] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -85.439677-0.020725j
[2025-08-19 00:22:52] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -85.400614-0.021072j
[2025-08-19 00:23:02] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -85.429437+0.006713j
[2025-08-19 00:23:11] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -85.404531-0.026817j
[2025-08-19 00:23:21] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -85.618227-0.031613j
[2025-08-19 00:23:30] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -85.518408-0.001099j
[2025-08-19 00:23:39] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -85.502092+0.013327j
[2025-08-19 00:23:49] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -85.504784+0.005878j
[2025-08-19 00:23:58] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -85.490424-0.004033j
[2025-08-19 00:24:08] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -85.507915-0.016938j
[2025-08-19 00:24:17] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -85.568592+0.009594j
[2025-08-19 00:24:26] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -85.681365+0.024026j
[2025-08-19 00:24:36] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -85.692206-0.010401j
[2025-08-19 00:24:45] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -85.603084-0.002704j
[2025-08-19 00:24:54] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -85.650878+0.005786j
[2025-08-19 00:25:04] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -85.488029+0.001945j
[2025-08-19 00:25:13] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -85.538160+0.003289j
[2025-08-19 00:25:23] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -85.771727+0.015811j
[2025-08-19 00:25:32] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -85.635784+0.019994j
[2025-08-19 00:25:41] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -85.682109+0.013582j
[2025-08-19 00:25:51] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -85.708139+0.039575j
[2025-08-19 00:26:00] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -85.523858+0.026274j
[2025-08-19 00:26:10] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -85.476457+0.019651j
[2025-08-19 00:26:19] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -85.427873-0.032057j
[2025-08-19 00:26:28] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -85.378882+0.015006j
[2025-08-19 00:26:38] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -85.290016+0.041464j
[2025-08-19 00:26:47] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -85.320059+0.001073j
[2025-08-19 00:26:57] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -85.267010+0.030208j
[2025-08-19 00:27:06] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -85.476802+0.031722j
[2025-08-19 00:27:15] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -85.518015+0.014945j
[2025-08-19 00:27:25] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -85.476396+0.009098j
[2025-08-19 00:27:34] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -85.455780+0.001755j
[2025-08-19 00:27:44] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -85.643977+0.016777j
[2025-08-19 00:27:53] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -85.567882-0.003819j
[2025-08-19 00:28:02] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -85.633044+0.015372j
[2025-08-19 00:28:12] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -85.491087+0.008797j
[2025-08-19 00:28:21] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -85.520664+0.027449j
[2025-08-19 00:28:30] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -85.576871+0.002527j
[2025-08-19 00:28:40] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -85.534361-0.011779j
[2025-08-19 00:28:49] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -85.409162-0.015869j
[2025-08-19 00:28:59] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -85.509688+0.001747j
[2025-08-19 00:29:08] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -85.529244+0.005355j
[2025-08-19 00:29:17] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -85.469345-0.013501j
[2025-08-19 00:29:27] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -85.567053-0.004988j
[2025-08-19 00:29:36] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -85.624109-0.013992j
[2025-08-19 00:29:46] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -85.609587-0.010672j
[2025-08-19 00:29:55] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -85.685147+0.010508j
[2025-08-19 00:30:04] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -85.616596+0.016169j
[2025-08-19 00:30:14] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -85.403250-0.034260j
[2025-08-19 00:30:23] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -85.679138-0.008174j
[2025-08-19 00:30:33] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -85.700795+0.017509j
[2025-08-19 00:30:42] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -85.638590-0.009845j
[2025-08-19 00:30:51] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -85.600791+0.005427j
[2025-08-19 00:31:01] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -85.575565+0.000666j
[2025-08-19 00:31:10] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -85.553007-0.028404j
[2025-08-19 00:31:20] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -85.672207-0.009091j
[2025-08-19 00:31:29] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -85.550916+0.023216j
[2025-08-19 00:31:38] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -85.579015+0.026058j
[2025-08-19 00:31:48] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -85.596739+0.029324j
[2025-08-19 00:31:57] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -85.550157-0.009637j
[2025-08-19 00:32:07] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -85.611215+0.009689j
[2025-08-19 00:32:16] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -85.468103-0.018785j
[2025-08-19 00:32:25] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -85.441652+0.021630j
[2025-08-19 00:32:35] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -85.628322+0.013739j
[2025-08-19 00:32:44] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -85.495709+0.006172j
[2025-08-19 00:32:54] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -85.520335-0.014660j
[2025-08-19 00:33:03] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -85.373625+0.001659j
[2025-08-19 00:33:12] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -85.179769+0.019517j
[2025-08-19 00:33:22] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -85.262026-0.007987j
[2025-08-19 00:33:31] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -85.158928-0.002099j
[2025-08-19 00:33:41] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -85.293335+0.014726j
[2025-08-19 00:33:50] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -85.178945+0.005367j
[2025-08-19 00:33:59] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -85.278477+0.005825j
[2025-08-19 00:34:09] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -85.410797-0.001604j
[2025-08-19 00:34:18] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -85.549213+0.005567j
[2025-08-19 00:34:28] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -85.381639+0.008555j
[2025-08-19 00:34:37] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -85.544491-0.008223j
[2025-08-19 00:34:46] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -85.488646-0.002190j
[2025-08-19 00:34:56] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -85.425614+0.019558j
[2025-08-19 00:35:05] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -85.468297-0.006998j
[2025-08-19 00:35:15] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -85.364676-0.004356j
[2025-08-19 00:35:24] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -85.419256+0.004166j
[2025-08-19 00:35:33] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -85.503093-0.012892j
[2025-08-19 00:35:43] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -85.383169-0.002985j
[2025-08-19 00:35:52] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -85.192681-0.008190j
[2025-08-19 00:36:02] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -85.357880-0.035587j
[2025-08-19 00:36:11] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -85.376425+0.004385j
[2025-08-19 00:36:20] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -85.388478+0.008964j
[2025-08-19 00:36:30] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -85.377756-0.036450j
[2025-08-19 00:36:39] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -85.380277+0.022697j
[2025-08-19 00:36:49] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -85.319662-0.021477j
[2025-08-19 00:36:58] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -85.457242-0.001617j
[2025-08-19 00:37:07] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -85.407295+0.019119j
[2025-08-19 00:37:07] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-19 00:37:17] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -85.482201-0.002929j
[2025-08-19 00:37:26] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -85.279534+0.004443j
[2025-08-19 00:37:36] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -85.414045+0.016082j
[2025-08-19 00:37:45] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -85.435443-0.001079j
[2025-08-19 00:37:54] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -85.425270+0.007783j
[2025-08-19 00:38:04] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -85.266073-0.015910j
[2025-08-19 00:38:13] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -85.536846+0.013375j
[2025-08-19 00:38:22] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -85.465163+0.006978j
[2025-08-19 00:38:32] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -85.484489-0.026209j
[2025-08-19 00:38:41] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -85.587999-0.001665j
[2025-08-19 00:38:51] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -85.558986-0.009291j
[2025-08-19 00:39:00] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -85.569769+0.009227j
[2025-08-19 00:39:09] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -85.529647-0.020919j
[2025-08-19 00:39:19] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -85.646166+0.003488j
[2025-08-19 00:39:28] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -85.572388+0.010123j
[2025-08-19 00:39:38] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -85.685864-0.002000j
[2025-08-19 00:39:47] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -85.537870+0.040496j
[2025-08-19 00:39:56] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -85.531026+0.014719j
[2025-08-19 00:40:06] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -85.573393-0.027645j
[2025-08-19 00:40:15] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -85.566686-0.013127j
[2025-08-19 00:40:24] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -85.567451-0.020827j
[2025-08-19 00:40:34] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -85.577965-0.008901j
[2025-08-19 00:40:43] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -85.501238-0.012026j
[2025-08-19 00:40:53] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -85.568095-0.034104j
[2025-08-19 00:41:02] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -85.679303+0.000142j
[2025-08-19 00:41:11] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -85.628890-0.017687j
[2025-08-19 00:41:21] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -85.665620-0.006696j
[2025-08-19 00:41:30] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -85.732874-0.005523j
[2025-08-19 00:41:40] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -85.782779-0.007229j
[2025-08-19 00:41:49] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -85.723620-0.010121j
[2025-08-19 00:41:58] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -85.676153-0.000813j
[2025-08-19 00:42:08] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -85.755287-0.006768j
[2025-08-19 00:42:17] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -85.809465+0.012398j
[2025-08-19 00:42:27] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -85.745927+0.000876j
[2025-08-19 00:42:36] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -85.705418-0.022251j
[2025-08-19 00:42:45] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -85.590394+0.016866j
[2025-08-19 00:42:55] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -85.590121+0.000215j
[2025-08-19 00:43:04] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -85.641018-0.001932j
[2025-08-19 00:43:14] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -85.636993+0.003614j
[2025-08-19 00:43:23] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -85.605521-0.028829j
[2025-08-19 00:43:32] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -85.712768-0.016836j
[2025-08-19 00:43:42] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -85.604348+0.009513j
[2025-08-19 00:43:51] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -85.568642+0.005090j
[2025-08-19 00:44:01] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -85.578614+0.012438j
[2025-08-19 00:44:10] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -85.602187+0.010295j
[2025-08-19 00:44:19] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -85.585444-0.007412j
[2025-08-19 00:44:29] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -85.480182+0.009424j
[2025-08-19 00:44:38] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -85.599502+0.002086j
[2025-08-19 00:44:48] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -85.419800+0.009744j
[2025-08-19 00:44:57] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -85.559643-0.008616j
[2025-08-19 00:45:06] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -85.509792-0.001363j
[2025-08-19 00:45:16] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -85.753428-0.014947j
[2025-08-19 00:45:25] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -85.600165+0.017035j
[2025-08-19 00:45:34] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -85.716476-0.011945j
[2025-08-19 00:45:44] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -85.592928-0.008203j
[2025-08-19 00:45:53] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -85.588528+0.003496j
[2025-08-19 00:46:03] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -85.483814-0.012077j
[2025-08-19 00:46:12] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -85.538251-0.005579j
[2025-08-19 00:46:22] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -85.490890-0.011979j
[2025-08-19 00:46:31] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -85.562433+0.021501j
[2025-08-19 00:46:40] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -85.440721-0.003841j
[2025-08-19 00:46:50] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -85.438003-0.019941j
[2025-08-19 00:46:59] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -85.504953+0.005437j
[2025-08-19 00:47:09] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -85.670967+0.008908j
[2025-08-19 00:47:18] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -85.520621-0.006390j
[2025-08-19 00:47:28] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -85.653870-0.019574j
[2025-08-19 00:47:37] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -85.609476+0.017262j
[2025-08-19 00:47:46] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -85.542924+0.021091j
[2025-08-19 00:47:56] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -85.546894-0.005879j
[2025-08-19 00:48:05] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -85.437430-0.006745j
[2025-08-19 00:48:15] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -85.429159+0.035190j
[2025-08-19 00:48:24] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -85.505847+0.021953j
[2025-08-19 00:48:33] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -85.595660-0.005888j
[2025-08-19 00:48:43] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -85.497387+0.005065j
[2025-08-19 00:48:52] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -85.470181-0.021731j
[2025-08-19 00:49:02] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -85.508612-0.001922j
[2025-08-19 00:49:11] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -85.528692+0.001883j
[2025-08-19 00:49:20] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -85.556204+0.006458j
[2025-08-19 00:49:30] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -85.484929+0.019027j
[2025-08-19 00:49:39] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -85.536038+0.008884j
[2025-08-19 00:49:48] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -85.689906+0.006577j
[2025-08-19 00:49:58] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -85.566673-0.025776j
[2025-08-19 00:50:07] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -85.668927+0.006948j
[2025-08-19 00:50:17] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -85.654003-0.005572j
[2025-08-19 00:50:26] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -85.765111+0.002338j
[2025-08-19 00:50:35] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -85.715134+0.003260j
[2025-08-19 00:50:45] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -85.784227+0.015673j
[2025-08-19 00:50:54] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -85.845916-0.010865j
[2025-08-19 00:51:04] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -85.743696-0.051171j
[2025-08-19 00:51:13] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -85.679720-0.007657j
[2025-08-19 00:51:22] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -85.789878-0.016336j
[2025-08-19 00:51:32] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -85.687644+0.004720j
[2025-08-19 00:51:41] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -85.619155-0.005318j
[2025-08-19 00:51:51] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -85.615843+0.007910j
[2025-08-19 00:52:00] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -85.630691-0.011838j
[2025-08-19 00:52:09] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -85.631109-0.034535j
[2025-08-19 00:52:19] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -85.556445-0.000635j
[2025-08-19 00:52:28] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -85.604228-0.016170j
[2025-08-19 00:52:38] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -85.545639+0.015380j
[2025-08-19 00:52:47] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -85.571512-0.009794j
[2025-08-19 00:52:47] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-19 00:52:56] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -85.560562+0.002736j
[2025-08-19 00:53:06] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -85.437369-0.003346j
[2025-08-19 00:53:15] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -85.397169+0.055475j
[2025-08-19 00:53:25] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -85.363922+0.010401j
[2025-08-19 00:53:34] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -85.567906-0.027507j
[2025-08-19 00:53:43] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -85.568094-0.004440j
[2025-08-19 00:53:53] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -85.572091+0.035117j
[2025-08-19 00:54:02] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -85.708868+0.035521j
[2025-08-19 00:54:12] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -85.576091+0.014259j
[2025-08-19 00:54:21] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -85.581083-0.011015j
[2025-08-19 00:54:30] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -85.740097+0.020259j
[2025-08-19 00:54:40] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -85.585275-0.001926j
[2025-08-19 00:54:49] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -85.446299+0.037857j
[2025-08-19 00:54:59] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -85.498545+0.039583j
[2025-08-19 00:55:08] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -85.528612+0.011364j
[2025-08-19 00:55:17] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -85.378089+0.003983j
[2025-08-19 00:55:27] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -85.364104-0.015705j
[2025-08-19 00:55:36] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -85.415664-0.000865j
[2025-08-19 00:55:46] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -85.484468+0.015927j
[2025-08-19 00:55:55] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -85.533329+0.003207j
[2025-08-19 00:56:04] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -85.378686-0.029138j
[2025-08-19 00:56:14] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -85.523503+0.013120j
[2025-08-19 00:56:23] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -85.457792+0.002584j
[2025-08-19 00:56:32] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -85.458189-0.008515j
[2025-08-19 00:56:42] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -85.572223+0.012094j
[2025-08-19 00:56:51] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -85.404336+0.001254j
[2025-08-19 00:57:01] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -85.486895+0.005606j
[2025-08-19 00:57:10] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -85.518490-0.000826j
[2025-08-19 00:57:19] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -85.607483-0.004019j
[2025-08-19 00:57:29] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -85.716996+0.005506j
[2025-08-19 00:57:38] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -85.574179+0.005969j
[2025-08-19 00:57:48] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -85.542945-0.000094j
[2025-08-19 00:57:57] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -85.646086-0.017702j
[2025-08-19 00:58:06] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -85.791180-0.001911j
[2025-08-19 00:58:16] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -85.768922-0.020314j
[2025-08-19 00:58:25] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -85.690069-0.016023j
[2025-08-19 00:58:35] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -85.652205-0.006860j
[2025-08-19 00:58:44] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -85.612349+0.005340j
[2025-08-19 00:58:53] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -85.669984-0.017796j
[2025-08-19 00:59:03] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -85.653489-0.014123j
[2025-08-19 00:59:12] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -85.629561+0.006844j
[2025-08-19 00:59:22] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -85.653816-0.007180j
[2025-08-19 00:59:31] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -85.698047+0.008105j
[2025-08-19 00:59:40] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -85.713375-0.013716j
[2025-08-19 00:59:50] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -85.516660+0.012293j
[2025-08-19 00:59:59] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -85.693974+0.002168j
[2025-08-19 01:00:08] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -85.652026+0.018440j
[2025-08-19 01:00:18] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -85.580483-0.003855j
[2025-08-19 01:00:27] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -85.762239-0.007318j
[2025-08-19 01:00:37] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -85.787911-0.000012j
[2025-08-19 01:00:37] RESTART #2 | Period: 600
[2025-08-19 01:00:46] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -85.673532-0.002871j
[2025-08-19 01:00:55] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -85.642985-0.010695j
[2025-08-19 01:01:05] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -85.594473-0.014248j
[2025-08-19 01:01:14] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -85.444610-0.009944j
[2025-08-19 01:01:24] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -85.639158-0.004438j
[2025-08-19 01:01:33] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -85.553600-0.006536j
[2025-08-19 01:01:42] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -85.650276-0.000480j
[2025-08-19 01:01:52] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -85.623290-0.005495j
[2025-08-19 01:02:01] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -85.759885-0.000117j
[2025-08-19 01:02:11] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -85.562238+0.014573j
[2025-08-19 01:02:20] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -85.652516-0.018897j
[2025-08-19 01:02:29] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -85.580274-0.012274j
[2025-08-19 01:02:39] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -85.472421+0.000073j
[2025-08-19 01:02:48] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -85.497572+0.004046j
[2025-08-19 01:02:58] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -85.579803-0.001520j
[2025-08-19 01:03:07] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -85.513777+0.005843j
[2025-08-19 01:03:16] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -85.424003-0.003688j
[2025-08-19 01:03:26] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -85.340768-0.011038j
[2025-08-19 01:03:35] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -85.380926+0.006897j
[2025-08-19 01:03:44] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -85.370124+0.005101j
[2025-08-19 01:03:54] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -85.381364+0.000782j
[2025-08-19 01:04:03] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -85.442914-0.009637j
[2025-08-19 01:04:13] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -85.506308-0.020290j
[2025-08-19 01:04:22] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -85.410606+0.007809j
[2025-08-19 01:04:31] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -85.434811-0.019190j
[2025-08-19 01:04:41] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -85.499503-0.008563j
[2025-08-19 01:04:50] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -85.373347-0.015515j
[2025-08-19 01:05:00] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -85.478405-0.014570j
[2025-08-19 01:05:09] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -85.424707+0.007600j
[2025-08-19 01:05:18] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -85.425893+0.001432j
[2025-08-19 01:05:28] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -85.375167-0.014491j
[2025-08-19 01:05:37] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -85.453154+0.032417j
[2025-08-19 01:05:46] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -85.471054-0.014658j
[2025-08-19 01:05:56] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -85.476494-0.005380j
[2025-08-19 01:06:05] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -85.454084-0.000166j
[2025-08-19 01:06:15] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -85.407908-0.046340j
[2025-08-19 01:06:24] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -85.347896+0.000767j
[2025-08-19 01:06:33] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -85.321508-0.008679j
[2025-08-19 01:06:43] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -85.380081-0.016298j
[2025-08-19 01:06:52] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -85.442491-0.006918j
[2025-08-19 01:07:02] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -85.315664+0.009953j
[2025-08-19 01:07:11] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -85.241041+0.001636j
[2025-08-19 01:07:20] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -85.319759+0.013917j
[2025-08-19 01:07:30] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -85.298476-0.012647j
[2025-08-19 01:07:39] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -85.513071-0.007826j
[2025-08-19 01:07:48] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -85.400577-0.034013j
[2025-08-19 01:07:58] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -85.577697+0.020331j
[2025-08-19 01:08:07] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -85.542962-0.039457j
[2025-08-19 01:08:17] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -85.468264-0.015140j
[2025-08-19 01:08:26] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -85.555305+0.011708j
[2025-08-19 01:08:26] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-19 01:08:35] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -85.542629-0.018017j
[2025-08-19 01:08:45] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -85.610776+0.007454j
[2025-08-19 01:08:54] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -85.505070-0.024816j
[2025-08-19 01:09:04] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -85.466856-0.002169j
[2025-08-19 01:09:13] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -85.502315-0.001692j
[2025-08-19 01:09:22] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -85.530703+0.009160j
[2025-08-19 01:09:32] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -85.482111-0.000234j
[2025-08-19 01:09:41] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -85.658959+0.015343j
[2025-08-19 01:09:51] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -85.745266+0.003376j
[2025-08-19 01:10:00] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -85.715779+0.005730j
[2025-08-19 01:10:09] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -85.722130+0.006905j
[2025-08-19 01:10:19] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -85.733507-0.011657j
[2025-08-19 01:10:28] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -85.625434+0.016441j
[2025-08-19 01:10:37] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -85.744044+0.013105j
[2025-08-19 01:10:47] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -85.722478+0.013885j
[2025-08-19 01:10:56] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -85.673584-0.016946j
[2025-08-19 01:11:06] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -85.656900-0.027844j
[2025-08-19 01:11:15] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -85.769156-0.007786j
[2025-08-19 01:11:24] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -85.567052+0.002772j
[2025-08-19 01:11:34] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -85.668768+0.010029j
[2025-08-19 01:11:43] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -85.675276-0.016761j
[2025-08-19 01:11:53] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -85.532966-0.008209j
[2025-08-19 01:12:02] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -85.630496+0.009159j
[2025-08-19 01:12:11] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -85.712426-0.016557j
[2025-08-19 01:12:21] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -85.707613+0.004161j
[2025-08-19 01:12:30] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -85.644938+0.001503j
[2025-08-19 01:12:39] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -85.638016+0.011436j
[2025-08-19 01:12:49] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -85.649823-0.003952j
[2025-08-19 01:12:58] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -85.461444-0.018082j
[2025-08-19 01:13:08] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -85.447108+0.001259j
[2025-08-19 01:13:17] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -85.524811-0.002369j
[2025-08-19 01:13:26] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -85.352256-0.022628j
[2025-08-19 01:13:36] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -85.329022+0.042904j
[2025-08-19 01:13:45] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -85.420524+0.014654j
[2025-08-19 01:13:55] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -85.634493-0.015163j
[2025-08-19 01:14:04] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -85.480345+0.009086j
[2025-08-19 01:14:13] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -85.429020+0.006550j
[2025-08-19 01:14:23] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -85.608118+0.004930j
[2025-08-19 01:14:32] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -85.472302+0.003909j
[2025-08-19 01:14:41] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -85.668257+0.007430j
[2025-08-19 01:14:51] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -85.832810-0.013608j
[2025-08-19 01:15:00] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -85.550712+0.010938j
[2025-08-19 01:15:10] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -85.509624+0.003971j
[2025-08-19 01:15:19] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -85.536280-0.016599j
[2025-08-19 01:15:28] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -85.504819-0.014404j
[2025-08-19 01:15:38] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -85.736476+0.003743j
[2025-08-19 01:15:47] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -85.665957-0.002394j
[2025-08-19 01:15:57] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -85.738700-0.012236j
[2025-08-19 01:16:06] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -85.544643-0.010562j
[2025-08-19 01:16:15] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -85.696213+0.000814j
[2025-08-19 01:16:25] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -85.653898-0.008617j
[2025-08-19 01:16:34] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -85.480170-0.004259j
[2025-08-19 01:16:43] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -85.429801+0.004101j
[2025-08-19 01:16:53] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -85.699124-0.001690j
[2025-08-19 01:17:02] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -85.649034-0.010258j
[2025-08-19 01:17:12] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -85.491885-0.008893j
[2025-08-19 01:17:21] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -85.486330+0.023617j
[2025-08-19 01:17:30] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -85.487876+0.003250j
[2025-08-19 01:17:40] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -85.546774-0.006182j
[2025-08-19 01:17:49] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -85.505715-0.027953j
[2025-08-19 01:17:59] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -85.584355-0.012872j
[2025-08-19 01:18:08] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -85.580735+0.022327j
[2025-08-19 01:18:17] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -85.538796-0.038620j
[2025-08-19 01:18:27] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -85.522111+0.004647j
[2025-08-19 01:18:36] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -85.516056+0.004993j
[2025-08-19 01:18:45] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -85.577410-0.003117j
[2025-08-19 01:18:55] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -85.496985-0.028539j
[2025-08-19 01:19:04] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -85.435476+0.006440j
[2025-08-19 01:19:14] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -85.559952-0.002650j
[2025-08-19 01:19:23] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -85.581839-0.014966j
[2025-08-19 01:19:32] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -85.577612-0.001752j
[2025-08-19 01:19:42] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -85.586804-0.003154j
[2025-08-19 01:19:51] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -85.579660+0.020519j
[2025-08-19 01:20:01] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -85.422112+0.002315j
[2025-08-19 01:20:10] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -85.605190+0.002161j
[2025-08-19 01:20:19] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -85.470889-0.013684j
[2025-08-19 01:20:29] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -85.553037-0.022717j
[2025-08-19 01:20:38] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -85.453429+0.003080j
[2025-08-19 01:20:47] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -85.526263+0.014947j
[2025-08-19 01:20:57] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -85.650731+0.015237j
[2025-08-19 01:21:06] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -85.747129-0.010242j
[2025-08-19 01:21:16] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -85.677008+0.008583j
[2025-08-19 01:21:25] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -85.686259-0.001010j
[2025-08-19 01:21:34] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -85.731880-0.005992j
[2025-08-19 01:21:44] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -85.695552+0.001326j
[2025-08-19 01:21:53] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -85.769226-0.017090j
[2025-08-19 01:22:02] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -85.869776+0.004341j
[2025-08-19 01:22:12] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -85.810479-0.030388j
[2025-08-19 01:22:21] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -85.803180-0.023994j
[2025-08-19 01:22:31] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -85.654123-0.023471j
[2025-08-19 01:22:40] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -85.710652+0.002976j
[2025-08-19 01:22:49] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -85.703272-0.015676j
[2025-08-19 01:22:59] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -85.614528+0.009528j
[2025-08-19 01:23:08] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -85.547307-0.001962j
[2025-08-19 01:23:18] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -85.434893-0.020082j
[2025-08-19 01:23:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -85.452407+0.037651j
[2025-08-19 01:23:36] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -85.506496+0.025658j
[2025-08-19 01:23:46] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -85.529839-0.007237j
[2025-08-19 01:23:55] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -85.365607-0.008483j
[2025-08-19 01:24:04] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -85.387973+0.011634j
[2025-08-19 01:24:04] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-19 01:24:14] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -85.375022-0.016224j
[2025-08-19 01:24:23] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -85.402815-0.074223j
[2025-08-19 01:24:33] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -85.269161-0.006873j
[2025-08-19 01:24:42] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -85.292833+0.052947j
[2025-08-19 01:24:51] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -85.336264+0.034883j
[2025-08-19 01:25:01] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -85.370498+0.051575j
[2025-08-19 01:25:10] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -85.405907+0.031511j
[2025-08-19 01:25:20] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -85.297365-0.013316j
[2025-08-19 01:25:29] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -85.443848+0.012093j
[2025-08-19 01:25:38] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -85.418041+0.010900j
[2025-08-19 01:25:48] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -85.407224+0.014302j
[2025-08-19 01:25:57] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -85.540537+0.021882j
[2025-08-19 01:26:07] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -85.486602+0.000507j
[2025-08-19 01:26:16] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -85.471132+0.081788j
[2025-08-19 01:26:25] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -85.500233+0.027487j
[2025-08-19 01:26:35] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -85.589702-0.026750j
[2025-08-19 01:26:44] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -85.498049+0.012347j
[2025-08-19 01:26:53] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -85.472278-0.026976j
[2025-08-19 01:27:03] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -85.419612+0.010049j
[2025-08-19 01:27:12] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -85.527359-0.001159j
[2025-08-19 01:27:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -85.376018+0.002423j
[2025-08-19 01:27:31] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -85.472451+0.010153j
[2025-08-19 01:27:40] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -85.479737-0.016286j
[2025-08-19 01:27:50] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -85.411856+0.001763j
[2025-08-19 01:27:59] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -85.468522+0.007829j
[2025-08-19 01:28:09] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -85.503104+0.004422j
[2025-08-19 01:28:18] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -85.521342+0.008410j
[2025-08-19 01:28:27] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -85.657627-0.002456j
[2025-08-19 01:28:37] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -85.652844+0.006952j
[2025-08-19 01:28:46] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -85.708408-0.028385j
[2025-08-19 01:28:56] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -85.700999-0.001758j
[2025-08-19 01:29:05] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -85.671293+0.012846j
[2025-08-19 01:29:14] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -85.542372+0.014083j
[2025-08-19 01:29:24] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -85.623446+0.003634j
[2025-08-19 01:29:33] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -85.579557+0.013407j
[2025-08-19 01:29:42] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -85.578838+0.016175j
[2025-08-19 01:29:52] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -85.517636+0.016242j
[2025-08-19 01:30:01] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -85.568731-0.016911j
[2025-08-19 01:30:11] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -85.591963-0.007126j
[2025-08-19 01:30:20] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -85.637927+0.006757j
[2025-08-19 01:30:29] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -85.690921-0.001608j
[2025-08-19 01:30:39] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -85.690633+0.006425j
[2025-08-19 01:30:48] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -85.459080+0.003403j
[2025-08-19 01:30:58] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -85.539216-0.018773j
[2025-08-19 01:31:07] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -85.508748-0.017111j
[2025-08-19 01:31:16] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -85.512021-0.014624j
[2025-08-19 01:31:26] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -85.399248-0.021549j
[2025-08-19 01:31:35] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -85.480913-0.022486j
[2025-08-19 01:31:44] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -85.672211-0.006266j
[2025-08-19 01:31:54] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -85.542992+0.022345j
[2025-08-19 01:32:03] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -85.538550-0.000284j
[2025-08-19 01:32:13] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -85.648558-0.009279j
[2025-08-19 01:32:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -85.452828-0.016400j
[2025-08-19 01:32:31] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -85.574047+0.000481j
[2025-08-19 01:32:41] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -85.618613+0.003024j
[2025-08-19 01:32:50] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -85.730871-0.014991j
[2025-08-19 01:33:00] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -85.697148+0.019185j
[2025-08-19 01:33:09] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -85.711475-0.012426j
[2025-08-19 01:33:18] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -85.811269+0.010211j
[2025-08-19 01:33:28] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -85.702218-0.044162j
[2025-08-19 01:33:37] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -85.686812-0.021685j
[2025-08-19 01:33:47] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -85.593603+0.003218j
[2025-08-19 01:33:56] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -85.552802-0.000446j
[2025-08-19 01:34:05] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -85.530734+0.001460j
[2025-08-19 01:34:15] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -85.762242-0.005159j
[2025-08-19 01:34:24] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -85.508074-0.032162j
[2025-08-19 01:34:33] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -85.410377-0.023824j
[2025-08-19 01:34:43] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -85.425161-0.006544j
[2025-08-19 01:34:52] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -85.537185-0.011878j
[2025-08-19 01:35:02] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -85.402128+0.016518j
[2025-08-19 01:35:11] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -85.554902+0.021556j
[2025-08-19 01:35:21] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -85.509974-0.024842j
[2025-08-19 01:35:30] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -85.470868+0.000902j
[2025-08-19 01:35:39] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -85.615345-0.018842j
[2025-08-19 01:35:49] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -85.572631+0.006005j
[2025-08-19 01:35:58] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -85.624849-0.000444j
[2025-08-19 01:36:07] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -85.638536+0.007227j
[2025-08-19 01:36:17] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -85.611352+0.004344j
[2025-08-19 01:36:26] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -85.638961-0.011886j
[2025-08-19 01:36:36] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -85.570325-0.010161j
[2025-08-19 01:36:45] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -85.504560+0.011104j
[2025-08-19 01:36:54] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -85.546068-0.005242j
[2025-08-19 01:37:04] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -85.670375+0.013124j
[2025-08-19 01:37:13] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -85.653796-0.005323j
[2025-08-19 01:37:23] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -85.743198+0.023041j
[2025-08-19 01:37:32] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -85.731828-0.003396j
[2025-08-19 01:37:41] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -85.766297-0.020345j
[2025-08-19 01:37:51] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -85.752553+0.009054j
[2025-08-19 01:38:00] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -85.675590+0.021445j
[2025-08-19 01:38:10] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -85.742739+0.036648j
[2025-08-19 01:38:19] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -85.811398-0.006487j
[2025-08-19 01:38:28] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -85.704711+0.018629j
[2025-08-19 01:38:38] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -85.689301+0.016519j
[2025-08-19 01:38:47] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -85.642699-0.003605j
[2025-08-19 01:38:56] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -85.674704+0.020308j
[2025-08-19 01:39:06] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -85.591720+0.012274j
[2025-08-19 01:39:15] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -85.469826+0.013015j
[2025-08-19 01:39:25] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -85.454241+0.055644j
[2025-08-19 01:39:34] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -85.550548+0.015370j
[2025-08-19 01:39:43] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -85.593158-0.011020j
[2025-08-19 01:39:43] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-19 01:39:53] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -85.601195+0.029547j
[2025-08-19 01:40:02] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -85.480715+0.018488j
[2025-08-19 01:40:12] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -85.474622+0.024229j
[2025-08-19 01:40:21] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -85.558035+0.012468j
[2025-08-19 01:40:30] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -85.510712-0.008352j
[2025-08-19 01:40:40] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -85.444656-0.001230j
[2025-08-19 01:40:49] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -85.412275+0.018444j
[2025-08-19 01:40:59] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -85.504481-0.000830j
[2025-08-19 01:41:08] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -85.475601+0.019184j
[2025-08-19 01:41:17] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -85.482257-0.008010j
[2025-08-19 01:41:27] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -85.454361+0.022070j
[2025-08-19 01:41:36] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -85.388944+0.049585j
[2025-08-19 01:41:46] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -85.402838+0.020890j
[2025-08-19 01:41:55] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -85.445450+0.014397j
[2025-08-19 01:42:04] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -85.512015+0.002295j
[2025-08-19 01:42:14] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -85.450739+0.009615j
[2025-08-19 01:42:23] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -85.460205+0.014048j
[2025-08-19 01:42:32] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -85.424809-0.011037j
[2025-08-19 01:42:42] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -85.494284+0.015360j
[2025-08-19 01:42:51] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -85.661740+0.006432j
[2025-08-19 01:43:01] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -85.715287-0.013183j
[2025-08-19 01:43:10] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -85.604262+0.001126j
[2025-08-19 01:43:19] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -85.640012+0.006796j
[2025-08-19 01:43:29] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -85.622181+0.006701j
[2025-08-19 01:43:38] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -85.543391-0.008342j
[2025-08-19 01:43:48] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -85.531278-0.002137j
[2025-08-19 01:43:57] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -85.441095-0.000142j
[2025-08-19 01:44:06] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -85.492816-0.006669j
[2025-08-19 01:44:16] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -85.596703-0.008427j
[2025-08-19 01:44:25] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -85.630297-0.009824j
[2025-08-19 01:44:35] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -85.699938-0.003929j
[2025-08-19 01:44:44] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -85.865370-0.006698j
[2025-08-19 01:44:53] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -85.774523+0.000404j
[2025-08-19 01:45:03] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -85.751839-0.012068j
[2025-08-19 01:45:12] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -85.700190-0.012380j
[2025-08-19 01:45:21] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -85.637126+0.001825j
[2025-08-19 01:45:31] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -85.612708-0.005550j
[2025-08-19 01:45:40] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -85.591234-0.010023j
[2025-08-19 01:45:50] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -85.590411+0.015187j
[2025-08-19 01:45:59] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -85.517558+0.011257j
[2025-08-19 01:46:08] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -85.489380+0.003428j
[2025-08-19 01:46:18] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -85.399852+0.007429j
[2025-08-19 01:46:27] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -85.602757+0.011651j
[2025-08-19 01:46:37] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -85.465553+0.002803j
[2025-08-19 01:46:46] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -85.452935+0.006556j
[2025-08-19 01:46:55] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -85.464783-0.008095j
[2025-08-19 01:47:05] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -85.624730+0.011995j
[2025-08-19 01:47:14] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -85.665596+0.003128j
[2025-08-19 01:47:23] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -85.594316+0.022308j
[2025-08-19 01:47:33] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -85.764783-0.011170j
[2025-08-19 01:47:42] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -85.765356-0.019874j
[2025-08-19 01:47:52] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -85.724675-0.015879j
[2025-08-19 01:48:01] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -85.695312+0.013987j
[2025-08-19 01:48:11] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -85.667280-0.011479j
[2025-08-19 01:48:20] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -85.772474-0.002144j
[2025-08-19 01:48:29] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -85.775910+0.013203j
[2025-08-19 01:48:39] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -85.619939-0.013453j
[2025-08-19 01:48:48] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -85.603242+0.006018j
[2025-08-19 01:48:57] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -85.680206+0.003604j
[2025-08-19 01:49:07] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -85.513938-0.005421j
[2025-08-19 01:49:16] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -85.565910+0.011774j
[2025-08-19 01:49:26] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -85.481667-0.021306j
[2025-08-19 01:49:35] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -85.324367-0.042334j
[2025-08-19 01:49:44] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -85.470811+0.020234j
[2025-08-19 01:49:54] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -85.373973+0.009968j
[2025-08-19 01:50:03] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -85.638106-0.029758j
[2025-08-19 01:50:13] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -85.566988-0.023205j
[2025-08-19 01:50:22] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -85.624920+0.013107j
[2025-08-19 01:50:31] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -85.714616+0.001997j
[2025-08-19 01:50:41] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -85.699522-0.005831j
[2025-08-19 01:50:50] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -85.707292+0.009330j
[2025-08-19 01:51:00] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -85.671304-0.013511j
[2025-08-19 01:51:09] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -85.602547+0.002416j
[2025-08-19 01:51:18] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -85.621742-0.004441j
[2025-08-19 01:51:28] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -85.710306+0.012745j
[2025-08-19 01:51:37] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -85.645070-0.003061j
[2025-08-19 01:51:46] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -85.587205+0.015738j
[2025-08-19 01:51:56] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -85.687257+0.010639j
[2025-08-19 01:52:05] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -85.615141+0.003787j
[2025-08-19 01:52:15] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -85.619105+0.011825j
[2025-08-19 01:52:24] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -85.515390+0.010116j
[2025-08-19 01:52:33] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -85.555373+0.023482j
[2025-08-19 01:52:43] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -85.618730-0.002853j
[2025-08-19 01:52:52] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -85.688393-0.007149j
[2025-08-19 01:53:02] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -85.667004+0.010290j
[2025-08-19 01:53:11] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -85.494000-0.014518j
[2025-08-19 01:53:20] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -85.746778-0.006561j
[2025-08-19 01:53:30] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -85.728097+0.001625j
[2025-08-19 01:53:39] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -85.643088-0.005757j
[2025-08-19 01:53:48] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -85.644865+0.015001j
[2025-08-19 01:53:58] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -85.581614+0.047010j
[2025-08-19 01:54:07] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -85.615432+0.003953j
[2025-08-19 01:54:17] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -85.746506+0.013347j
[2025-08-19 01:54:26] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -85.658270-0.024829j
[2025-08-19 01:54:35] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -85.632873+0.003600j
[2025-08-19 01:54:45] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -85.745685+0.011276j
[2025-08-19 01:54:54] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -85.649410-0.005300j
[2025-08-19 01:55:04] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -85.767603-0.030188j
[2025-08-19 01:55:13] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -85.532381+0.004503j
[2025-08-19 01:55:22] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -85.634532+0.024547j
[2025-08-19 01:55:22] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-19 01:55:32] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -85.648789-0.009185j
[2025-08-19 01:55:41] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -85.649829-0.005171j
[2025-08-19 01:55:51] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -85.632592+0.003148j
[2025-08-19 01:56:00] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -85.453907+0.003431j
[2025-08-19 01:56:09] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -85.499490-0.027209j
[2025-08-19 01:56:19] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -85.524158+0.006248j
[2025-08-19 01:56:28] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -85.503173-0.019195j
[2025-08-19 01:56:37] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -85.594153+0.011456j
[2025-08-19 01:56:47] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -85.498244-0.014621j
[2025-08-19 01:56:56] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -85.479724-0.007967j
[2025-08-19 01:57:06] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -85.469214-0.019718j
[2025-08-19 01:57:15] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -85.479046-0.002001j
[2025-08-19 01:57:24] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -85.503927+0.010377j
[2025-08-19 01:57:34] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -85.499126+0.001837j
[2025-08-19 01:57:43] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -85.496812+0.000475j
[2025-08-19 01:57:53] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -85.454945-0.001501j
[2025-08-19 01:58:02] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -85.472476+0.009811j
[2025-08-19 01:58:11] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -85.577627+0.001070j
[2025-08-19 01:58:21] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -85.518366-0.018286j
[2025-08-19 01:58:30] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -85.632810+0.002608j
[2025-08-19 01:58:40] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -85.612439-0.019722j
[2025-08-19 01:58:49] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -85.602661-0.021207j
[2025-08-19 01:58:58] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -85.471168-0.002547j
[2025-08-19 01:59:08] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -85.530993-0.009529j
[2025-08-19 01:59:17] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -85.483542+0.001536j
[2025-08-19 01:59:26] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -85.560365+0.008763j
[2025-08-19 01:59:36] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -85.434894-0.007039j
[2025-08-19 01:59:45] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -85.501733-0.000050j
[2025-08-19 01:59:55] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -85.598230-0.001402j
[2025-08-19 02:00:04] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -85.601215-0.003233j
[2025-08-19 02:00:13] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -85.664324-0.003425j
[2025-08-19 02:00:23] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -85.589422-0.000610j
[2025-08-19 02:00:32] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -85.699492-0.009430j
[2025-08-19 02:00:41] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -85.593194+0.003726j
[2025-08-19 02:00:51] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -85.543197-0.015402j
[2025-08-19 02:01:00] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -85.520536-0.002275j
[2025-08-19 02:01:10] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -85.591503+0.008224j
[2025-08-19 02:01:19] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -85.515258+0.006279j
[2025-08-19 02:01:28] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -85.394159-0.023387j
[2025-08-19 02:01:38] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -85.434266-0.000022j
[2025-08-19 02:01:47] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -85.416553+0.024543j
[2025-08-19 02:01:57] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -85.437208+0.002694j
[2025-08-19 02:02:06] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -85.495599-0.002862j
[2025-08-19 02:02:15] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -85.481111-0.005424j
[2025-08-19 02:02:25] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -85.456028-0.019621j
[2025-08-19 02:02:34] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -85.387471-0.017814j
[2025-08-19 02:02:44] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -85.488468+0.000481j
[2025-08-19 02:02:53] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -85.576379-0.017152j
[2025-08-19 02:03:02] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -85.442295+0.004399j
[2025-08-19 02:03:12] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -85.545285-0.000287j
[2025-08-19 02:03:21] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -85.540650-0.004968j
[2025-08-19 02:03:30] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -85.506100-0.008128j
[2025-08-19 02:03:40] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -85.464266-0.026978j
[2025-08-19 02:03:49] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -85.403665-0.013685j
[2025-08-19 02:03:59] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -85.361620-0.002537j
[2025-08-19 02:04:08] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -85.475715-0.016139j
[2025-08-19 02:04:17] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -85.410525-0.008820j
[2025-08-19 02:04:27] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -85.581285+0.003108j
[2025-08-19 02:04:36] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -85.633157-0.014810j
[2025-08-19 02:04:45] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -85.553571-0.001358j
[2025-08-19 02:04:55] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -85.520029-0.010048j
[2025-08-19 02:05:04] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -85.675438-0.012664j
[2025-08-19 02:05:14] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -85.724836-0.000090j
[2025-08-19 02:05:23] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -85.718273-0.007369j
[2025-08-19 02:05:32] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -85.544805+0.003796j
[2025-08-19 02:05:42] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -85.559378-0.001378j
[2025-08-19 02:05:51] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -85.572524+0.010793j
[2025-08-19 02:06:00] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -85.540758-0.007510j
[2025-08-19 02:06:10] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -85.624756-0.019039j
[2025-08-19 02:06:19] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -85.750497-0.004075j
[2025-08-19 02:06:29] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -85.652188-0.007309j
[2025-08-19 02:06:38] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -85.788427-0.015254j
[2025-08-19 02:06:47] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -85.624604+0.017083j
[2025-08-19 02:06:57] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -85.635116-0.007531j
[2025-08-19 02:07:06] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -85.505206+0.049120j
[2025-08-19 02:07:15] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -85.656654-0.010107j
[2025-08-19 02:07:25] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -85.661663+0.007718j
[2025-08-19 02:07:34] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -85.558136+0.006166j
[2025-08-19 02:07:44] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -85.582122-0.009161j
[2025-08-19 02:07:53] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -85.554289+0.004093j
[2025-08-19 02:08:02] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -85.538384-0.009093j
[2025-08-19 02:08:12] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -85.594398-0.003466j
[2025-08-19 02:08:21] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -85.530808-0.016375j
[2025-08-19 02:08:30] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -85.540138+0.004980j
[2025-08-19 02:08:40] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -85.516842+0.005080j
[2025-08-19 02:08:49] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -85.631331-0.004067j
[2025-08-19 02:08:59] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -85.566328-0.021376j
[2025-08-19 02:09:08] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -85.602681+0.007824j
[2025-08-19 02:09:17] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -85.511005-0.001530j
[2025-08-19 02:09:27] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -85.351431+0.005748j
[2025-08-19 02:09:36] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -85.458230-0.002656j
[2025-08-19 02:09:45] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -85.397701-0.007502j
[2025-08-19 02:09:55] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -85.483137+0.004529j
[2025-08-19 02:10:04] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -85.631594+0.005485j
[2025-08-19 02:10:14] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -85.498548-0.004298j
[2025-08-19 02:10:23] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -85.521173+0.000992j
[2025-08-19 02:10:32] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -85.457046-0.005485j
[2025-08-19 02:10:42] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -85.424126-0.001266j
[2025-08-19 02:10:51] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -85.522380-0.028225j
[2025-08-19 02:11:01] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -85.544920-0.024828j
[2025-08-19 02:11:01] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-19 02:11:10] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -85.541523-0.006858j
[2025-08-19 02:11:19] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -85.615102-0.021929j
[2025-08-19 02:11:29] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -85.698598-0.000882j
[2025-08-19 02:11:38] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -85.642575-0.008284j
[2025-08-19 02:11:47] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -85.704602+0.014451j
[2025-08-19 02:11:57] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -85.512477-0.017179j
[2025-08-19 02:12:06] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -85.581164-0.007838j
[2025-08-19 02:12:16] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -85.520883-0.020711j
[2025-08-19 02:12:25] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -85.393653-0.004887j
[2025-08-19 02:12:34] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -85.380873-0.016319j
[2025-08-19 02:12:44] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -85.393086-0.005290j
[2025-08-19 02:12:53] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -85.312842-0.025182j
[2025-08-19 02:13:03] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -85.482802-0.002281j
[2025-08-19 02:13:12] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -85.429982+0.032291j
[2025-08-19 02:13:22] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -85.584706+0.007554j
[2025-08-19 02:13:31] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -85.523513-0.000743j
[2025-08-19 02:13:40] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -85.541418+0.007645j
[2025-08-19 02:13:50] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -85.531117-0.017361j
[2025-08-19 02:13:59] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -85.477402+0.021278j
[2025-08-19 02:14:08] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -85.412438+0.010739j
[2025-08-19 02:14:18] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -85.530395+0.013847j
[2025-08-19 02:14:27] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -85.409664-0.002661j
[2025-08-19 02:14:37] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -85.452824-0.008117j
[2025-08-19 02:14:46] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -85.427779-0.016782j
[2025-08-19 02:14:55] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -85.476050+0.002826j
[2025-08-19 02:15:05] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -85.693578-0.012877j
[2025-08-19 02:15:14] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -85.578815-0.015485j
[2025-08-19 02:15:24] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -85.544922-0.000515j
[2025-08-19 02:15:33] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -85.633155-0.011596j
[2025-08-19 02:15:42] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -85.546245+0.005475j
[2025-08-19 02:15:52] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -85.543606-0.010032j
[2025-08-19 02:16:01] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -85.502754-0.012638j
[2025-08-19 02:16:11] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -85.544498-0.011499j
[2025-08-19 02:16:20] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -85.554957-0.007927j
[2025-08-19 02:16:29] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -85.621498-0.004004j
[2025-08-19 02:16:39] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -85.583779-0.015888j
[2025-08-19 02:16:48] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -85.490706+0.006216j
[2025-08-19 02:16:58] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -85.470777+0.003176j
[2025-08-19 02:17:07] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -85.395359+0.013577j
[2025-08-19 02:17:16] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -85.384487-0.019428j
[2025-08-19 02:17:26] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -85.478523+0.019063j
[2025-08-19 02:17:35] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -85.451539-0.011044j
[2025-08-19 02:17:44] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -85.491769+0.025612j
[2025-08-19 02:17:54] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -85.554464-0.000720j
[2025-08-19 02:18:03] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -85.532379+0.004420j
[2025-08-19 02:18:13] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -85.445862+0.014189j
[2025-08-19 02:18:22] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -85.457642-0.010141j
[2025-08-19 02:18:31] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -85.466668+0.006380j
[2025-08-19 02:18:41] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -85.473336+0.006631j
[2025-08-19 02:18:50] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -85.604696+0.004859j
[2025-08-19 02:18:59] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -85.553480-0.008107j
[2025-08-19 02:19:09] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -85.485703+0.003678j
[2025-08-19 02:19:18] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -85.578438-0.008728j
[2025-08-19 02:19:28] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -85.567810-0.030201j
[2025-08-19 02:19:37] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -85.621201-0.008158j
[2025-08-19 02:19:46] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -85.650525+0.009913j
[2025-08-19 02:19:56] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -85.729566-0.007369j
[2025-08-19 02:20:05] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -85.607783-0.011309j
[2025-08-19 02:20:15] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -85.576798-0.006633j
[2025-08-19 02:20:24] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -85.653811+0.008108j
[2025-08-19 02:20:33] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -85.572180+0.010030j
[2025-08-19 02:20:43] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -85.612762-0.001881j
[2025-08-19 02:20:52] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -85.421600-0.005346j
[2025-08-19 02:21:02] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -85.453730-0.000954j
[2025-08-19 02:21:11] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -85.604326+0.010556j
[2025-08-19 02:21:20] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -85.574360+0.018825j
[2025-08-19 02:21:30] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -85.583126-0.000361j
[2025-08-19 02:21:39] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -85.537947-0.042498j
[2025-08-19 02:21:48] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -85.564027-0.002715j
[2025-08-19 02:21:58] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -85.490807+0.006982j
[2025-08-19 02:22:07] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -85.553474+0.004793j
[2025-08-19 02:22:17] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -85.467636-0.027416j
[2025-08-19 02:22:26] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -85.472098+0.002055j
[2025-08-19 02:22:35] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -85.435189+0.013256j
[2025-08-19 02:22:45] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -85.510682+0.014516j
[2025-08-19 02:22:54] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -85.480858-0.010107j
[2025-08-19 02:23:04] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -85.623577+0.025711j
[2025-08-19 02:23:13] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -85.491972+0.028270j
[2025-08-19 02:23:22] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -85.432960+0.015698j
[2025-08-19 02:23:32] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -85.443749+0.000712j
[2025-08-19 02:23:41] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -85.518619+0.052380j
[2025-08-19 02:23:51] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -85.447913+0.032957j
[2025-08-19 02:24:00] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -85.405595+0.050208j
[2025-08-19 02:24:09] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -85.517117+0.021376j
[2025-08-19 02:24:19] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -85.626788+0.028350j
[2025-08-19 02:24:28] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -85.673745+0.047867j
[2025-08-19 02:24:37] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -85.595399+0.010310j
[2025-08-19 02:24:47] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -85.663384+0.009050j
[2025-08-19 02:24:56] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -85.479528+0.013827j
[2025-08-19 02:25:06] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -85.497739+0.006623j
[2025-08-19 02:25:15] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -85.601497+0.013187j
[2025-08-19 02:25:24] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -85.682237+0.023493j
[2025-08-19 02:25:34] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -85.559627-0.003367j
[2025-08-19 02:25:43] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -85.613064+0.022784j
[2025-08-19 02:25:53] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -85.544671-0.014152j
[2025-08-19 02:26:02] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -85.583828+0.006254j
[2025-08-19 02:26:11] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -85.702108-0.015534j
[2025-08-19 02:26:21] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -85.671689-0.003672j
[2025-08-19 02:26:30] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -85.659443+0.013414j
[2025-08-19 02:26:39] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -85.622222-0.014972j
[2025-08-19 02:26:39] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-19 02:26:49] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -85.709191+0.012620j
[2025-08-19 02:26:58] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -85.517618+0.005279j
[2025-08-19 02:27:08] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -85.631332+0.014126j
[2025-08-19 02:27:17] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -85.528303+0.010978j
[2025-08-19 02:27:26] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -85.593449+0.010319j
[2025-08-19 02:27:36] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -85.534597+0.062662j
[2025-08-19 02:27:45] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -85.611466-0.011799j
[2025-08-19 02:27:55] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -85.499667+0.008716j
[2025-08-19 02:28:04] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -85.658944+0.042194j
[2025-08-19 02:28:13] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -85.553906+0.013162j
[2025-08-19 02:28:23] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -85.541627+0.019492j
[2025-08-19 02:28:32] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -85.588723+0.033989j
[2025-08-19 02:28:41] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -85.552948-0.018297j
[2025-08-19 02:28:51] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -85.578185+0.055973j
[2025-08-19 02:29:00] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -85.703142-0.001259j
[2025-08-19 02:29:10] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -85.554348+0.011166j
[2025-08-19 02:29:19] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -85.509747+0.023092j
[2025-08-19 02:29:28] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -85.665171+0.012673j
[2025-08-19 02:29:38] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -85.500618+0.008380j
[2025-08-19 02:29:47] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -85.538254+0.011512j
[2025-08-19 02:29:57] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -85.509457+0.003383j
[2025-08-19 02:30:06] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -85.552426+0.032267j
[2025-08-19 02:30:15] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -85.564294+0.004709j
[2025-08-19 02:30:25] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -85.616169+0.006468j
[2025-08-19 02:30:34] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -85.455638+0.019332j
[2025-08-19 02:30:44] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -85.441616-0.006338j
[2025-08-19 02:30:53] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -85.402472+0.001026j
[2025-08-19 02:31:02] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -85.530549+0.011833j
[2025-08-19 02:31:12] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -85.565319+0.008876j
[2025-08-19 02:31:21] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -85.553244-0.005693j
[2025-08-19 02:31:30] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -85.496337-0.012524j
[2025-08-19 02:31:40] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -85.478117-0.001577j
[2025-08-19 02:31:49] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -85.611127+0.014452j
[2025-08-19 02:31:59] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -85.625469+0.010755j
[2025-08-19 02:32:08] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -85.856708+0.024623j
[2025-08-19 02:32:17] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -85.709011+0.015057j
[2025-08-19 02:32:27] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -85.525952+0.004135j
[2025-08-19 02:32:36] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -85.496613+0.001586j
[2025-08-19 02:32:45] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -85.671524+0.006870j
[2025-08-19 02:32:55] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -85.639136-0.008444j
[2025-08-19 02:33:04] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -85.585281+0.005105j
[2025-08-19 02:33:14] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -85.573100-0.023158j
[2025-08-19 02:33:23] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -85.658238+0.013121j
[2025-08-19 02:33:32] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -85.619301+0.011817j
[2025-08-19 02:33:42] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -85.550123-0.015569j
[2025-08-19 02:33:51] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -85.516571-0.009517j
[2025-08-19 02:34:01] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -85.738038-0.007392j
[2025-08-19 02:34:10] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -85.684145-0.011751j
[2025-08-19 02:34:19] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -85.619474-0.013414j
[2025-08-19 02:34:29] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -85.609562-0.006170j
[2025-08-19 02:34:29] ✅ Training completed | Restarts: 2
[2025-08-19 02:34:29] ============================================================
[2025-08-19 02:34:29] Training completed | Runtime: 9894.6s
[2025-08-19 02:34:42] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-19 02:34:42] ============================================================
