[2025-08-19 14:46:36] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-08-19 14:46:36]   - 迭代次数: final
[2025-08-19 14:46:36]   - 能量: -86.182585-0.016759j ± 0.111063
[2025-08-19 14:46:36]   - 时间戳: 2025-08-19T13:07:01.226703+08:00
[2025-08-19 14:46:44] ✓ 变分状态参数已从checkpoint恢复
[2025-08-19 14:46:44] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-19 14:46:44] ==================================================
[2025-08-19 14:46:44] GCNN for Shastry-Sutherland Model
[2025-08-19 14:46:44] ==================================================
[2025-08-19 14:46:44] System parameters:
[2025-08-19 14:46:44]   - System size: L=5, N=100
[2025-08-19 14:46:44]   - System parameters: J1=0.07, J2=0.0, Q=1.0
[2025-08-19 14:46:44] --------------------------------------------------
[2025-08-19 14:46:44] Model parameters:
[2025-08-19 14:46:44]   - Number of layers = 4
[2025-08-19 14:46:44]   - Number of features = 4
[2025-08-19 14:46:44]   - Total parameters = 19628
[2025-08-19 14:46:44] --------------------------------------------------
[2025-08-19 14:46:44] Training parameters:
[2025-08-19 14:46:44]   - Learning rate: 0.015
[2025-08-19 14:46:44]   - Total iterations: 1050
[2025-08-19 14:46:44]   - Annealing cycles: 3
[2025-08-19 14:46:44]   - Initial period: 150
[2025-08-19 14:46:44]   - Period multiplier: 2.0
[2025-08-19 14:46:44]   - Temperature range: 0.0-1.0
[2025-08-19 14:46:44]   - Samples: 4096
[2025-08-19 14:46:44]   - Discarded samples: 0
[2025-08-19 14:46:44]   - Chunk size: 2048
[2025-08-19 14:46:44]   - Diagonal shift: 0.2
[2025-08-19 14:46:44]   - Gradient clipping: 1.0
[2025-08-19 14:46:44]   - Checkpoint enabled: interval=100
[2025-08-19 14:46:44]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.07/training/checkpoints
[2025-08-19 14:46:44] --------------------------------------------------
[2025-08-19 14:46:44] Device status:
[2025-08-19 14:46:44]   - Devices model: A100
[2025-08-19 14:46:44]   - Number of devices: 1
[2025-08-19 14:46:44]   - Sharding: True
[2025-08-19 14:46:44] ============================================================
[2025-08-19 14:47:20] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -86.740404+0.039233j
[2025-08-19 14:47:43] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -86.688002+0.041171j
[2025-08-19 14:47:52] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -86.648139+0.023058j
[2025-08-19 14:48:02] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -86.522404+0.021513j
[2025-08-19 14:48:11] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -86.517875-0.012652j
[2025-08-19 14:48:21] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -86.673217+0.029069j
[2025-08-19 14:48:30] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -86.521787+0.025086j
[2025-08-19 14:48:39] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -86.662117+0.028791j
[2025-08-19 14:48:49] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -86.564491+0.028361j
[2025-08-19 14:48:58] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -86.657055+0.009907j
[2025-08-19 14:49:08] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -86.571046+0.049495j
[2025-08-19 14:49:17] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -86.584123+0.007592j
[2025-08-19 14:49:26] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -86.604147+0.066228j
[2025-08-19 14:49:36] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -86.715166+0.027989j
[2025-08-19 14:49:45] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -86.590980+0.054551j
[2025-08-19 14:49:55] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -86.711930-0.030640j
[2025-08-19 14:50:04] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -86.647152+0.105938j
[2025-08-19 14:50:13] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -86.573848+0.007195j
[2025-08-19 14:50:23] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -86.549974+0.021297j
[2025-08-19 14:50:32] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -86.623347+0.011116j
[2025-08-19 14:50:41] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -86.580506+0.047700j
[2025-08-19 14:50:51] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -86.594597+0.011793j
[2025-08-19 14:51:00] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -86.591012+0.036035j
[2025-08-19 14:51:10] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -86.726436+0.017392j
[2025-08-19 14:51:19] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -86.777075+0.008932j
[2025-08-19 14:51:29] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -86.543601+0.018950j
[2025-08-19 14:51:38] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -86.758097-0.029944j
[2025-08-19 14:51:47] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -86.679205+0.012912j
[2025-08-19 14:51:57] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -86.620046-0.002837j
[2025-08-19 14:52:06] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -86.671622+0.021782j
[2025-08-19 14:52:15] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -86.850422+0.034028j
[2025-08-19 14:52:25] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -86.911892+0.043178j
[2025-08-19 14:52:34] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -86.891308+0.040399j
[2025-08-19 14:52:44] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -86.746620+0.016622j
[2025-08-19 14:52:53] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -86.849459+0.016614j
[2025-08-19 14:53:02] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -86.757040+0.001238j
[2025-08-19 14:53:12] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -86.804441+0.026785j
[2025-08-19 14:53:21] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -86.767848-0.012458j
[2025-08-19 14:53:31] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -86.726112+0.023351j
[2025-08-19 14:53:40] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -86.747515+0.037067j
[2025-08-19 14:53:49] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -86.927227-0.013570j
[2025-08-19 14:53:59] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -86.747257+0.047752j
[2025-08-19 14:54:08] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -86.683294-0.008880j
[2025-08-19 14:54:18] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -86.534024+0.043028j
[2025-08-19 14:54:27] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -86.617211-0.018670j
[2025-08-19 14:54:36] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -86.346275-0.110010j
[2025-08-19 14:54:46] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -86.433755-0.018502j
[2025-08-19 14:54:55] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -86.421117-0.027758j
[2025-08-19 14:55:05] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -86.600165-0.038616j
[2025-08-19 14:55:14] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -86.777527-0.018678j
[2025-08-19 14:55:23] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -86.839495+0.005048j
[2025-08-19 14:55:33] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -86.844402-0.019188j
[2025-08-19 14:55:42] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -86.826804-0.010726j
[2025-08-19 14:55:52] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -86.853239-0.023690j
[2025-08-19 14:56:01] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -86.830945-0.010844j
[2025-08-19 14:56:10] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -86.834003-0.018968j
[2025-08-19 14:56:20] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -86.802877-0.005615j
[2025-08-19 14:56:29] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -86.770615-0.005622j
[2025-08-19 14:56:38] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -86.646518-0.025245j
[2025-08-19 14:56:48] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -86.597217-0.009554j
[2025-08-19 14:56:57] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -86.512352-0.016803j
[2025-08-19 14:57:07] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -86.647380-0.002250j
[2025-08-19 14:57:16] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -86.510037-0.007684j
[2025-08-19 14:57:25] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -86.480985-0.037026j
[2025-08-19 14:57:35] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -86.631461-0.011772j
[2025-08-19 14:57:44] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -86.624597-0.034005j
[2025-08-19 14:57:54] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -86.568652+0.027960j
[2025-08-19 14:58:03] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -86.629048-0.020744j
[2025-08-19 14:58:12] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -86.685847-0.021904j
[2025-08-19 14:58:22] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -86.711257-0.027634j
[2025-08-19 14:58:31] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -86.578132+0.025237j
[2025-08-19 14:58:41] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -86.699185+0.090704j
[2025-08-19 14:58:50] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -86.524650+0.009041j
[2025-08-19 14:58:59] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -86.667822-0.008352j
[2025-08-19 14:59:09] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -86.744979+0.008603j
[2025-08-19 14:59:18] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -86.807964+0.033968j
[2025-08-19 14:59:27] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -86.800252-0.005895j
[2025-08-19 14:59:37] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -86.871704-0.003204j
[2025-08-19 14:59:46] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -86.863091+0.019290j
[2025-08-19 14:59:56] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -86.976892-0.026580j
[2025-08-19 15:00:05] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -86.931127+0.005926j
[2025-08-19 15:00:14] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -86.796346-0.018702j
[2025-08-19 15:00:24] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -86.612439-0.001139j
[2025-08-19 15:00:33] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -86.781632+0.009511j
[2025-08-19 15:00:43] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -86.585490+0.055091j
[2025-08-19 15:00:52] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -86.498271+0.158577j
[2025-08-19 15:01:01] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -86.645921+0.093721j
[2025-08-19 15:01:11] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -86.434462+0.110227j
[2025-08-19 15:01:20] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -86.416245+0.163271j
[2025-08-19 15:01:30] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -86.393986+0.176686j
[2025-08-19 15:01:39] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -86.487970+0.117144j
[2025-08-19 15:01:48] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -86.576895+0.105761j
[2025-08-19 15:01:58] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -86.416669+0.105500j
[2025-08-19 15:02:07] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -86.396384+0.050140j
[2025-08-19 15:02:17] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -86.540584+0.066372j
[2025-08-19 15:02:26] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -86.518822+0.084282j
[2025-08-19 15:02:35] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -86.459487+0.096034j
[2025-08-19 15:02:45] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -86.431116+0.167076j
[2025-08-19 15:02:54] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -86.412633+0.081325j
[2025-08-19 15:03:04] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -86.316694-0.003268j
[2025-08-19 15:03:04] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-19 15:03:13] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -86.438497+0.082013j
[2025-08-19 15:03:22] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -86.519080+0.044160j
[2025-08-19 15:03:32] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -86.405719+0.068376j
[2025-08-19 15:03:41] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -86.393288+0.049424j
[2025-08-19 15:03:51] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -86.735294+0.064918j
[2025-08-19 15:04:00] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -86.664619+0.076955j
[2025-08-19 15:04:09] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -86.732406+0.041085j
[2025-08-19 15:04:19] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -86.605777+0.050276j
[2025-08-19 15:04:28] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -86.655558+0.077683j
[2025-08-19 15:04:37] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -86.712081+0.071177j
[2025-08-19 15:04:47] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -86.809764+0.053307j
[2025-08-19 15:04:56] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -86.714314+0.023383j
[2025-08-19 15:05:06] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -86.836247+0.029235j
[2025-08-19 15:05:15] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -86.595476+0.038829j
[2025-08-19 15:05:24] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -86.712916+0.057216j
[2025-08-19 15:05:34] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -86.724147-0.015858j
[2025-08-19 15:05:43] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -86.691157-0.001056j
[2025-08-19 15:05:53] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -86.414746-0.024984j
[2025-08-19 15:06:02] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -86.578380-0.001705j
[2025-08-19 15:06:11] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -86.429957-0.073388j
[2025-08-19 15:06:21] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -86.600414-0.096136j
[2025-08-19 15:06:30] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -86.463603-0.150542j
[2025-08-19 15:06:40] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -86.535827-0.167766j
[2025-08-19 15:06:49] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -86.451119-0.151820j
[2025-08-19 15:06:58] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -86.440485-0.166054j
[2025-08-19 15:07:08] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -86.366874-0.183040j
[2025-08-19 15:07:17] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -86.404497-0.180658j
[2025-08-19 15:07:27] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -86.406549-0.109818j
[2025-08-19 15:07:36] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -86.467720-0.108833j
[2025-08-19 15:07:45] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -86.544391-0.093608j
[2025-08-19 15:07:55] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -86.587758-0.066940j
[2025-08-19 15:08:04] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -86.636605-0.080526j
[2025-08-19 15:08:13] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -86.717437-0.105062j
[2025-08-19 15:08:23] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -86.462842-0.233973j
[2025-08-19 15:08:32] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -86.571856-0.189424j
[2025-08-19 15:08:42] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -86.582101-0.175187j
[2025-08-19 15:08:51] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -86.560329-0.154666j
[2025-08-19 15:09:00] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -86.387438-0.115825j
[2025-08-19 15:09:10] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -86.401650-0.078582j
[2025-08-19 15:09:19] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -85.731393-0.049139j
[2025-08-19 15:09:29] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -85.598944-0.060401j
[2025-08-19 15:09:38] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -85.850917-0.116852j
[2025-08-19 15:09:47] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -86.023032-0.106232j
[2025-08-19 15:09:57] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -85.995455-0.034888j
[2025-08-19 15:10:06] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -86.063418-0.032664j
[2025-08-19 15:10:15] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -85.980440-0.104213j
[2025-08-19 15:10:25] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -86.190599-0.018725j
[2025-08-19 15:10:34] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -86.122347-0.034262j
[2025-08-19 15:10:44] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -86.276070+0.036825j
[2025-08-19 15:10:53] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -86.316106+0.017060j
[2025-08-19 15:10:53] RESTART #1 | Period: 300
[2025-08-19 15:11:02] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -86.375656-0.009561j
[2025-08-19 15:11:12] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -86.311466-0.000998j
[2025-08-19 15:11:21] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -86.339647+0.012604j
[2025-08-19 15:11:31] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -86.382464+0.025540j
[2025-08-19 15:11:40] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -86.400825-0.004230j
[2025-08-19 15:11:49] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -86.368472+0.008170j
[2025-08-19 15:11:59] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -86.476952+0.013242j
[2025-08-19 15:12:08] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -86.492815+0.041824j
[2025-08-19 15:12:18] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -86.447327+0.033236j
[2025-08-19 15:12:27] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -86.606736+0.005515j
[2025-08-19 15:12:36] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -86.539669+0.037498j
[2025-08-19 15:12:46] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -86.749832+0.033881j
[2025-08-19 15:12:55] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -86.625514+0.027878j
[2025-08-19 15:13:05] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -86.714846+0.050777j
[2025-08-19 15:13:14] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -86.811961+0.024618j
[2025-08-19 15:13:23] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -86.713318+0.043072j
[2025-08-19 15:13:33] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -86.736487+0.027574j
[2025-08-19 15:13:42] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -86.425032-0.092876j
[2025-08-19 15:13:52] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -86.503443+0.002072j
[2025-08-19 15:14:01] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -86.692200+0.016218j
[2025-08-19 15:14:10] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -86.662223-0.047283j
[2025-08-19 15:14:20] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -86.456565-0.003280j
[2025-08-19 15:14:29] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -86.232690+0.018498j
[2025-08-19 15:14:38] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -86.356069+0.094475j
[2025-08-19 15:14:48] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -86.618507+0.038004j
[2025-08-19 15:14:57] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -86.771906-0.010123j
[2025-08-19 15:15:07] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -86.818053+0.004278j
[2025-08-19 15:15:16] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -86.693075-0.000323j
[2025-08-19 15:15:25] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -86.778423+0.010978j
[2025-08-19 15:15:35] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -86.684775+0.036286j
[2025-08-19 15:15:44] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -86.625618+0.012202j
[2025-08-19 15:15:54] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -86.521100+0.024214j
[2025-08-19 15:16:03] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -86.600151+0.014950j
[2025-08-19 15:16:12] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -86.775341+0.023711j
[2025-08-19 15:16:22] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -86.612444+0.033911j
[2025-08-19 15:16:31] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -86.777352-0.000311j
[2025-08-19 15:16:40] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -86.591575-0.078609j
[2025-08-19 15:16:50] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -86.643961-0.046124j
[2025-08-19 15:16:59] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -86.609660-0.045763j
[2025-08-19 15:17:09] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -86.687251-0.052543j
[2025-08-19 15:17:18] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -86.611110-0.078319j
[2025-08-19 15:17:27] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -86.572365-0.047137j
[2025-08-19 15:17:37] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -86.729486-0.011901j
[2025-08-19 15:17:46] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -86.727657+0.001589j
[2025-08-19 15:17:56] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -86.924079+0.001482j
[2025-08-19 15:18:05] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -86.739828-0.026076j
[2025-08-19 15:18:14] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -86.749065-0.010648j
[2025-08-19 15:18:24] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -86.718576-0.006603j
[2025-08-19 15:18:33] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -86.663827+0.013936j
[2025-08-19 15:18:43] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -86.711875-0.015338j
[2025-08-19 15:18:43] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-19 15:18:52] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -86.677521-0.010229j
[2025-08-19 15:19:01] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -86.698399+0.033551j
[2025-08-19 15:19:11] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -86.835982+0.029132j
[2025-08-19 15:19:20] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -86.798562+0.006590j
[2025-08-19 15:19:30] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -86.682638+0.052271j
[2025-08-19 15:19:39] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -86.648628-0.005258j
[2025-08-19 15:19:48] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -86.579477+0.022261j
[2025-08-19 15:19:58] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -86.570610+0.037444j
[2025-08-19 15:20:07] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -86.702413+0.008767j
[2025-08-19 15:20:17] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -86.799541+0.013593j
[2025-08-19 15:20:26] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -86.657518+0.025813j
[2025-08-19 15:20:35] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -86.764995+0.019986j
[2025-08-19 15:20:45] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -86.548870+0.007891j
[2025-08-19 15:20:54] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -86.767379+0.023982j
[2025-08-19 15:21:03] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -86.849247+0.017875j
[2025-08-19 15:21:13] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -86.777594+0.027384j
[2025-08-19 15:21:22] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -86.953519+0.010089j
[2025-08-19 15:21:32] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -86.918171+0.027172j
[2025-08-19 15:21:41] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -86.869250+0.036726j
[2025-08-19 15:21:50] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -86.868747+0.010016j
[2025-08-19 15:22:00] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -86.728025+0.019684j
[2025-08-19 15:22:09] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -86.714828+0.012982j
[2025-08-19 15:22:19] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -86.764363+0.037099j
[2025-08-19 15:22:28] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -86.728502+0.012592j
[2025-08-19 15:22:37] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -86.710757+0.015089j
[2025-08-19 15:22:47] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -86.785685+0.014202j
[2025-08-19 15:22:56] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -86.921135+0.014510j
[2025-08-19 15:23:06] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -87.047298+0.001107j
[2025-08-19 15:23:15] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -87.035185+0.019774j
[2025-08-19 15:23:24] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -86.816004+0.006971j
[2025-08-19 15:23:34] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -86.883378+0.003073j
[2025-08-19 15:23:43] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -86.968497+0.032069j
[2025-08-19 15:23:52] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -86.905493+0.037866j
[2025-08-19 15:24:02] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -86.746357+0.001459j
[2025-08-19 15:24:11] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -86.863401-0.036026j
[2025-08-19 15:24:21] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -86.803142+0.007142j
[2025-08-19 15:24:30] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -86.753716-0.013116j
[2025-08-19 15:24:39] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -86.712238+0.009326j
[2025-08-19 15:24:49] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -86.762674+0.038166j
[2025-08-19 15:24:58] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -86.753076-0.011632j
[2025-08-19 15:25:08] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -86.752890+0.033164j
[2025-08-19 15:25:17] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -86.786392+0.015185j
[2025-08-19 15:25:26] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -86.819490-0.008974j
[2025-08-19 15:25:36] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -86.907889+0.015431j
[2025-08-19 15:25:45] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -86.813469+0.004276j
[2025-08-19 15:25:54] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -86.881649+0.002705j
[2025-08-19 15:26:04] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -86.890375+0.020218j
[2025-08-19 15:26:13] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -86.813062+0.042204j
[2025-08-19 15:26:23] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -86.940868+0.010987j
[2025-08-19 15:26:32] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -86.871145+0.004013j
[2025-08-19 15:26:41] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -86.850722+0.011880j
[2025-08-19 15:26:51] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -86.718225+0.003592j
[2025-08-19 15:27:00] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -86.848101-0.001945j
[2025-08-19 15:27:10] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -86.859099-0.014175j
[2025-08-19 15:27:19] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -86.744182+0.006037j
[2025-08-19 15:27:28] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -86.781121-0.000712j
[2025-08-19 15:27:38] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -86.823875+0.014957j
[2025-08-19 15:27:47] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -86.869402+0.017221j
[2025-08-19 15:27:57] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -86.742907+0.042873j
[2025-08-19 15:28:06] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -86.684688+0.002654j
[2025-08-19 15:28:15] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -86.723483+0.017241j
[2025-08-19 15:28:25] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -86.754479-0.042377j
[2025-08-19 15:28:34] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -86.735414-0.004261j
[2025-08-19 15:28:44] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -86.657021-0.020252j
[2025-08-19 15:28:53] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -86.677279+0.016731j
[2025-08-19 15:29:02] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -86.788569-0.054076j
[2025-08-19 15:29:12] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -86.624054-0.147577j
[2025-08-19 15:29:21] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -86.470939+0.069624j
[2025-08-19 15:29:31] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -86.105308-0.027294j
[2025-08-19 15:29:40] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -85.498583+0.226723j
[2025-08-19 15:29:49] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -86.091377+0.301576j
[2025-08-19 15:29:59] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -86.236929+0.080762j
[2025-08-19 15:30:08] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -86.434711+0.088306j
[2025-08-19 15:30:18] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -86.587019+0.062672j
[2025-08-19 15:30:27] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -86.644550+0.060720j
[2025-08-19 15:30:36] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -86.608101+0.043817j
[2025-08-19 15:30:46] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -86.663145+0.019056j
[2025-08-19 15:30:55] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -86.768822+0.012611j
[2025-08-19 15:31:05] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -86.676938-0.062969j
[2025-08-19 15:31:14] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -86.638154+0.030750j
[2025-08-19 15:31:23] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -86.671925+0.026508j
[2025-08-19 15:31:33] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -86.773613+0.020381j
[2025-08-19 15:31:42] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -86.729313+0.019535j
[2025-08-19 15:31:52] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -86.871304+0.007431j
[2025-08-19 15:32:01] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -86.801718-0.000341j
[2025-08-19 15:32:10] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -86.734175-0.010926j
[2025-08-19 15:32:20] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -86.758288-0.007914j
[2025-08-19 15:32:29] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -86.682013-0.045489j
[2025-08-19 15:32:38] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -86.838673-0.015386j
[2025-08-19 15:32:48] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -86.845194-0.017843j
[2025-08-19 15:32:57] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -86.818947+0.009872j
[2025-08-19 15:33:07] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -86.762908+0.044235j
[2025-08-19 15:33:16] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -86.847050+0.008020j
[2025-08-19 15:33:25] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -86.804205-0.013350j
[2025-08-19 15:33:35] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -86.803191-0.006866j
[2025-08-19 15:33:44] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -86.915079+0.013089j
[2025-08-19 15:33:54] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -86.760066+0.032468j
[2025-08-19 15:34:03] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -86.765347-0.009933j
[2025-08-19 15:34:12] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -86.756034+0.028147j
[2025-08-19 15:34:22] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -86.862253+0.030384j
[2025-08-19 15:34:22] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-19 15:34:31] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -86.767023+0.016694j
[2025-08-19 15:34:41] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -86.742956-0.002422j
[2025-08-19 15:34:50] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -86.842435+0.002117j
[2025-08-19 15:34:59] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -86.761393+0.003612j
[2025-08-19 15:35:09] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -86.755963-0.008539j
[2025-08-19 15:35:18] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -86.696400-0.009358j
[2025-08-19 15:35:28] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -86.628020-0.009677j
[2025-08-19 15:35:37] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -86.595645-0.008154j
[2025-08-19 15:35:46] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -86.772710+0.001463j
[2025-08-19 15:35:56] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -86.754762+0.005092j
[2025-08-19 15:36:05] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -86.639912+0.004020j
[2025-08-19 15:36:15] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -86.675470+0.017236j
[2025-08-19 15:36:24] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -86.594661+0.011793j
[2025-08-19 15:36:33] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -86.708459-0.006751j
[2025-08-19 15:36:43] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -86.870186-0.004339j
[2025-08-19 15:36:52] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -86.845611+0.016193j
[2025-08-19 15:37:01] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -86.827959+0.006598j
[2025-08-19 15:37:11] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -86.701444-0.015213j
[2025-08-19 15:37:20] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -86.819567+0.016478j
[2025-08-19 15:37:30] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -86.818275-0.011374j
[2025-08-19 15:37:39] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -86.881775-0.005550j
[2025-08-19 15:37:48] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -86.724621-0.003097j
[2025-08-19 15:37:58] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -86.678036+0.049909j
[2025-08-19 15:38:07] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -86.653779+0.024506j
[2025-08-19 15:38:17] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -86.733102+0.027471j
[2025-08-19 15:38:26] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -86.732363-0.001227j
[2025-08-19 15:38:35] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -86.815840+0.012637j
[2025-08-19 15:38:45] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -86.733317+0.005261j
[2025-08-19 15:38:54] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -86.849023-0.013690j
[2025-08-19 15:39:04] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -86.735521+0.030282j
[2025-08-19 15:39:13] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -86.813727-0.014266j
[2025-08-19 15:39:22] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -86.764940-0.010989j
[2025-08-19 15:39:32] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -86.948839-0.013198j
[2025-08-19 15:39:41] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -86.860403+0.001548j
[2025-08-19 15:39:51] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -87.009863+0.004811j
[2025-08-19 15:40:00] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -86.836691+0.011403j
[2025-08-19 15:40:09] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -86.965866+0.019458j
[2025-08-19 15:40:19] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -86.916213+0.018677j
[2025-08-19 15:40:28] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -86.823314+0.012747j
[2025-08-19 15:40:37] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -86.897078+0.012563j
[2025-08-19 15:40:47] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -86.817860-0.000314j
[2025-08-19 15:40:56] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -86.850919-0.000602j
[2025-08-19 15:41:06] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -86.726725-0.048958j
[2025-08-19 15:41:15] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -86.850460-0.018486j
[2025-08-19 15:41:24] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -86.688875-0.011240j
[2025-08-19 15:41:34] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -86.710011+0.020517j
[2025-08-19 15:41:43] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -86.827571+0.011731j
[2025-08-19 15:41:53] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -86.713422-0.009751j
[2025-08-19 15:42:02] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -86.767257+0.003826j
[2025-08-19 15:42:11] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -86.902526+0.012117j
[2025-08-19 15:42:21] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -86.797560+0.000581j
[2025-08-19 15:42:30] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -86.868542-0.000752j
[2025-08-19 15:42:40] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -86.809270+0.000579j
[2025-08-19 15:42:49] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -86.710139+0.024835j
[2025-08-19 15:42:59] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -86.781695+0.003477j
[2025-08-19 15:43:08] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -86.677895-0.022426j
[2025-08-19 15:43:17] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -86.693217+0.023697j
[2025-08-19 15:43:27] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -86.739253+0.031651j
[2025-08-19 15:43:36] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -86.670223+0.011854j
[2025-08-19 15:43:45] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -86.721714+0.014728j
[2025-08-19 15:43:55] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -86.565246+0.008645j
[2025-08-19 15:44:04] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -86.562541-0.013420j
[2025-08-19 15:44:14] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -86.630218-0.048473j
[2025-08-19 15:44:23] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -86.722129+0.028959j
[2025-08-19 15:44:32] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -86.800015+0.009691j
[2025-08-19 15:44:42] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -86.795696+0.011499j
[2025-08-19 15:44:51] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -86.665216+0.008090j
[2025-08-19 15:45:01] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -86.714670+0.000397j
[2025-08-19 15:45:10] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -86.818063-0.009065j
[2025-08-19 15:45:19] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -86.798929+0.001173j
[2025-08-19 15:45:29] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -86.704727+0.004319j
[2025-08-19 15:45:38] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -86.682349-0.001728j
[2025-08-19 15:45:48] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -86.696160-0.002531j
[2025-08-19 15:45:57] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -86.673190+0.010045j
[2025-08-19 15:46:06] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -86.746382-0.010793j
[2025-08-19 15:46:16] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -86.734529-0.003237j
[2025-08-19 15:46:25] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -86.740010+0.000588j
[2025-08-19 15:46:35] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -86.767233-0.017791j
[2025-08-19 15:46:44] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -86.706098-0.009176j
[2025-08-19 15:46:53] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -86.753894-0.013812j
[2025-08-19 15:47:03] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -86.758555-0.008170j
[2025-08-19 15:47:12] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -86.695388-0.008131j
[2025-08-19 15:47:22] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -86.651885-0.008884j
[2025-08-19 15:47:31] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -86.690392-0.022197j
[2025-08-19 15:47:40] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -86.826634-0.015870j
[2025-08-19 15:47:50] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -86.654593-0.056801j
[2025-08-19 15:47:59] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -86.679459-0.038469j
[2025-08-19 15:48:09] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -86.829743+0.009085j
[2025-08-19 15:48:18] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -86.739274+0.020684j
[2025-08-19 15:48:27] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -86.816440-0.059586j
[2025-08-19 15:48:37] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -87.082042+0.003761j
[2025-08-19 15:48:46] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -86.905112+0.012526j
[2025-08-19 15:48:56] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -86.779889+0.011714j
[2025-08-19 15:49:05] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -86.815844+0.023517j
[2025-08-19 15:49:14] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -86.805712-0.013472j
[2025-08-19 15:49:24] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -86.886088+0.005599j
[2025-08-19 15:49:33] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -86.829520-0.006052j
[2025-08-19 15:49:43] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -86.778282-0.012246j
[2025-08-19 15:49:52] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -87.000302-0.010180j
[2025-08-19 15:50:01] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -86.925369+0.008628j
[2025-08-19 15:50:01] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-19 15:50:11] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -87.061015-0.003381j
[2025-08-19 15:50:20] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -86.878250-0.006015j
[2025-08-19 15:50:29] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -86.948228-0.004880j
[2025-08-19 15:50:39] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -86.789340+0.003003j
[2025-08-19 15:50:48] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -86.880858-0.012337j
[2025-08-19 15:50:58] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -86.696668-0.006204j
[2025-08-19 15:51:07] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -86.669861+0.012027j
[2025-08-19 15:51:16] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -86.767430+0.004293j
[2025-08-19 15:51:26] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -86.726498-0.015522j
[2025-08-19 15:51:35] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -86.713782-0.002685j
[2025-08-19 15:51:45] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -86.741318+0.012104j
[2025-08-19 15:51:54] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -86.783872+0.018940j
[2025-08-19 15:52:03] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -86.810872+0.025495j
[2025-08-19 15:52:13] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -86.913747+0.003665j
[2025-08-19 15:52:22] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -86.862927+0.022830j
[2025-08-19 15:52:32] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -86.870542+0.011068j
[2025-08-19 15:52:41] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -86.676253+0.071934j
[2025-08-19 15:52:50] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -86.769455-0.028499j
[2025-08-19 15:53:00] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -86.850867-0.010840j
[2025-08-19 15:53:09] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -86.778952+0.016558j
[2025-08-19 15:53:19] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -86.738647+0.004755j
[2025-08-19 15:53:28] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -86.711515-0.038395j
[2025-08-19 15:53:37] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -86.798731+0.002862j
[2025-08-19 15:53:47] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -86.794321-0.035051j
[2025-08-19 15:53:56] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -86.876731-0.032583j
[2025-08-19 15:54:06] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -86.899506-0.023414j
[2025-08-19 15:54:15] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -86.779987-0.034754j
[2025-08-19 15:54:24] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -86.842926-0.030980j
[2025-08-19 15:54:34] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -86.800056-0.032388j
[2025-08-19 15:54:43] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -86.745700-0.027351j
[2025-08-19 15:54:52] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -87.014617-0.003958j
[2025-08-19 15:55:02] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -86.865609-0.035373j
[2025-08-19 15:55:11] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -86.766107-0.038309j
[2025-08-19 15:55:21] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -86.806628-0.015419j
[2025-08-19 15:55:30] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -86.821729-0.019846j
[2025-08-19 15:55:39] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -86.838695-0.013066j
[2025-08-19 15:55:49] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -86.807618-0.002992j
[2025-08-19 15:55:58] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -86.757647+0.000567j
[2025-08-19 15:56:08] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -86.827893+0.008920j
[2025-08-19 15:56:17] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -86.733213-0.001773j
[2025-08-19 15:56:26] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -86.713247+0.009921j
[2025-08-19 15:56:36] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -86.601234+0.018135j
[2025-08-19 15:56:45] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -86.711838+0.009482j
[2025-08-19 15:56:55] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -86.748982-0.030136j
[2025-08-19 15:57:04] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -86.645143-0.004224j
[2025-08-19 15:57:13] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -86.600695-0.012756j
[2025-08-19 15:57:23] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -86.762916-0.012412j
[2025-08-19 15:57:32] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -86.774435+0.004152j
[2025-08-19 15:57:42] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -86.658100+0.014896j
[2025-08-19 15:57:51] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -86.609962+0.003162j
[2025-08-19 15:57:51] RESTART #2 | Period: 600
[2025-08-19 15:58:00] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -86.501996+0.010740j
[2025-08-19 15:58:10] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -86.610148+0.001937j
[2025-08-19 15:58:19] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -86.686428+0.015901j
[2025-08-19 15:58:29] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -86.658488+0.009991j
[2025-08-19 15:58:38] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -86.656064-0.009475j
[2025-08-19 15:58:47] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -86.589784+0.034650j
[2025-08-19 15:58:57] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -86.813666-0.006366j
[2025-08-19 15:59:06] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -86.862852+0.005096j
[2025-08-19 15:59:15] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -86.794059+0.001870j
[2025-08-19 15:59:25] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -86.789379+0.011163j
[2025-08-19 15:59:34] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -86.769288+0.010222j
[2025-08-19 15:59:44] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -86.758376-0.023468j
[2025-08-19 15:59:53] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -86.763696+0.012326j
[2025-08-19 16:00:02] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -86.767179+0.009306j
[2025-08-19 16:00:12] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -86.724547+0.039138j
[2025-08-19 16:00:21] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -86.652859+0.029949j
[2025-08-19 16:00:31] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -86.532627-0.042326j
[2025-08-19 16:00:40] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -86.771535+0.030569j
[2025-08-19 16:00:49] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -86.771968+0.005579j
[2025-08-19 16:00:59] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -86.563876-0.047233j
[2025-08-19 16:01:08] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -86.647625-0.107134j
[2025-08-19 16:01:18] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -86.700979-0.038915j
[2025-08-19 16:01:27] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -86.761319-0.061165j
[2025-08-19 16:01:36] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -86.722836-0.055265j
[2025-08-19 16:01:46] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -86.873346-0.006002j
[2025-08-19 16:01:55] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -86.818052-0.005786j
[2025-08-19 16:02:05] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -86.773484+0.010450j
[2025-08-19 16:02:14] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -86.575077+0.015154j
[2025-08-19 16:02:23] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -86.737104+0.011663j
[2025-08-19 16:02:33] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -86.754309+0.054258j
[2025-08-19 16:02:42] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -86.725466+0.010060j
[2025-08-19 16:02:52] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -86.705751+0.026321j
[2025-08-19 16:03:01] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -86.820128+0.004137j
[2025-08-19 16:03:10] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -86.763631-0.009110j
[2025-08-19 16:03:20] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -86.729937+0.014909j
[2025-08-19 16:03:29] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -86.682540+0.019891j
[2025-08-19 16:03:39] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -86.613443+0.042732j
[2025-08-19 16:03:48] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -86.647657+0.031548j
[2025-08-19 16:03:57] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -86.596634+0.072088j
[2025-08-19 16:04:07] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -86.584923+0.064698j
[2025-08-19 16:04:16] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -86.559554+0.053661j
[2025-08-19 16:04:26] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -86.546132+0.057024j
[2025-08-19 16:04:35] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -86.636581+0.076277j
[2025-08-19 16:04:44] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -86.665300+0.065141j
[2025-08-19 16:04:54] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -86.730572+0.035188j
[2025-08-19 16:05:03] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -86.716290+0.071848j
[2025-08-19 16:05:12] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -86.639527+0.044170j
[2025-08-19 16:05:22] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -86.641279+0.101154j
[2025-08-19 16:05:31] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -86.537845+0.070259j
[2025-08-19 16:05:41] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -86.652955+0.068916j
[2025-08-19 16:05:41] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-19 16:05:50] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -86.603524+0.081201j
[2025-08-19 16:05:59] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -86.618852+0.053100j
[2025-08-19 16:06:09] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -86.592705+0.059884j
[2025-08-19 16:06:18] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -86.822777+0.043951j
[2025-08-19 16:06:28] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -86.836007+0.043719j
[2025-08-19 16:06:37] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -86.834359+0.013670j
[2025-08-19 16:06:46] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -86.874442+0.035585j
[2025-08-19 16:06:56] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -86.898560+0.029994j
[2025-08-19 16:07:05] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -86.777297+0.039453j
[2025-08-19 16:07:15] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -86.759964+0.053499j
[2025-08-19 16:07:24] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -86.755519+0.071976j
[2025-08-19 16:07:33] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -86.844943+0.022086j
[2025-08-19 16:07:43] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -86.793588+0.061491j
[2025-08-19 16:07:52] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -86.824819+0.068267j
[2025-08-19 16:08:02] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -86.805603+0.065364j
[2025-08-19 16:08:11] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -86.831530+0.035464j
[2025-08-19 16:08:20] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -86.736079+0.026950j
[2025-08-19 16:08:30] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -86.740977+0.044161j
[2025-08-19 16:08:39] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -86.707505+0.034824j
[2025-08-19 16:08:49] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -86.597895+0.030803j
[2025-08-19 16:08:58] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -86.566178+0.023156j
[2025-08-19 16:09:07] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -86.487002+0.012349j
[2025-08-19 16:09:17] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -86.696003-0.010663j
[2025-08-19 16:09:26] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -86.648322+0.023110j
[2025-08-19 16:09:36] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -86.739565+0.005166j
[2025-08-19 16:09:45] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -86.546980+0.006111j
[2025-08-19 16:09:54] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -86.708982+0.018372j
[2025-08-19 16:10:04] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -86.801586+0.020761j
[2025-08-19 16:10:13] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -86.791047-0.004719j
[2025-08-19 16:10:23] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -86.647437-0.009947j
[2025-08-19 16:10:32] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -86.674354-0.024616j
[2025-08-19 16:10:41] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -86.579143-0.012154j
[2025-08-19 16:10:51] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -86.530277-0.006158j
[2025-08-19 16:11:00] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -86.557909-0.023593j
[2025-08-19 16:11:10] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -86.724386+0.002348j
[2025-08-19 16:11:19] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -86.722302+0.002423j
[2025-08-19 16:11:28] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -86.728977+0.003175j
[2025-08-19 16:11:38] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -86.677585-0.004395j
[2025-08-19 16:11:47] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -86.764464+0.005329j
[2025-08-19 16:11:57] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -86.808615+0.004295j
[2025-08-19 16:12:06] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -86.891552-0.019829j
[2025-08-19 16:12:15] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -86.792104+0.000735j
[2025-08-19 16:12:25] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -86.884274-0.003689j
[2025-08-19 16:12:34] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -86.828106-0.006161j
[2025-08-19 16:12:44] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -86.846396-0.005862j
[2025-08-19 16:12:53] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -86.889490+0.001210j
[2025-08-19 16:13:02] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -86.897801-0.004427j
[2025-08-19 16:13:12] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -86.841274+0.013105j
[2025-08-19 16:13:21] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -86.744542+0.021845j
[2025-08-19 16:13:31] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -86.781840-0.009402j
[2025-08-19 16:13:40] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -86.755877+0.002243j
[2025-08-19 16:13:49] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -86.815292-0.006613j
[2025-08-19 16:13:59] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -86.694438-0.062909j
[2025-08-19 16:14:08] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -86.849414+0.005563j
[2025-08-19 16:14:18] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -86.794253-0.021148j
[2025-08-19 16:14:27] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -86.868799-0.028087j
[2025-08-19 16:14:36] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -86.749150-0.040338j
[2025-08-19 16:14:46] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -86.845784-0.050966j
[2025-08-19 16:14:55] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -86.694946+0.020631j
[2025-08-19 16:15:05] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -86.900337-0.036322j
[2025-08-19 16:15:14] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -86.889392-0.015798j
[2025-08-19 16:15:23] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -86.866995-0.026328j
[2025-08-19 16:15:33] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -86.842107-0.045809j
[2025-08-19 16:15:42] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -86.910346-0.039111j
[2025-08-19 16:15:52] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -86.912181-0.040702j
[2025-08-19 16:16:01] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -86.912600-0.043826j
[2025-08-19 16:16:10] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -86.860698-0.068895j
[2025-08-19 16:16:20] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -86.875223-0.045720j
[2025-08-19 16:16:29] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -86.706467+0.017824j
[2025-08-19 16:16:39] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -86.768165+0.012754j
[2025-08-19 16:16:48] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -86.784581+0.025259j
[2025-08-19 16:16:57] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -86.684350+0.009531j
[2025-08-19 16:17:07] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -86.799354+0.004581j
[2025-08-19 16:17:16] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -86.918941+0.011026j
[2025-08-19 16:17:26] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -86.828004+0.002021j
[2025-08-19 16:17:35] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -86.764320-0.006379j
[2025-08-19 16:17:44] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -86.829749-0.001329j
[2025-08-19 16:17:54] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -86.677937+0.006628j
[2025-08-19 16:18:03] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -86.803727+0.008208j
[2025-08-19 16:18:12] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -86.905041-0.005362j
[2025-08-19 16:18:22] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -86.854623+0.035405j
[2025-08-19 16:18:31] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -86.830401+0.025970j
[2025-08-19 16:18:41] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -86.846590+0.007056j
[2025-08-19 16:18:50] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -86.899622+0.009298j
[2025-08-19 16:18:59] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -86.833423+0.025526j
[2025-08-19 16:19:09] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -86.763338+0.023125j
[2025-08-19 16:19:18] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -86.765704+0.021988j
[2025-08-19 16:19:28] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -86.868178+0.044955j
[2025-08-19 16:19:37] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -86.843675+0.032225j
[2025-08-19 16:19:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -86.817246+0.023117j
[2025-08-19 16:19:56] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -86.793525+0.013956j
[2025-08-19 16:20:05] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -86.736051+0.013918j
[2025-08-19 16:20:15] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -86.703992+0.014025j
[2025-08-19 16:20:24] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -86.790816+0.028161j
[2025-08-19 16:20:33] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -86.571876-0.023567j
[2025-08-19 16:20:43] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -86.558788+0.002411j
[2025-08-19 16:20:52] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -86.525488+0.003756j
[2025-08-19 16:21:01] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -86.615417+0.000191j
[2025-08-19 16:21:11] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -86.808075+0.018304j
[2025-08-19 16:21:20] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -86.854812+0.003158j
[2025-08-19 16:21:20] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-19 16:21:30] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -86.807013+0.013461j
[2025-08-19 16:21:39] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -86.854339+0.025982j
[2025-08-19 16:21:48] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -86.909905-0.005183j
[2025-08-19 16:21:58] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -86.963199-0.032389j
[2025-08-19 16:22:07] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -86.912589+0.001936j
[2025-08-19 16:22:17] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -86.755986-0.049201j
[2025-08-19 16:22:26] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -86.813389-0.005772j
[2025-08-19 16:22:35] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -86.718156+0.017273j
[2025-08-19 16:22:45] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -86.720683+0.005357j
[2025-08-19 16:22:54] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -86.612206-0.013899j
[2025-08-19 16:23:04] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -86.813906-0.044494j
[2025-08-19 16:23:13] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -86.636187-0.007085j
[2025-08-19 16:23:22] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -86.620555+0.028010j
[2025-08-19 16:23:32] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -86.513001+0.037199j
[2025-08-19 16:23:41] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -86.600142+0.040968j
[2025-08-19 16:23:51] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -86.589100+0.027852j
[2025-08-19 16:24:00] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -86.681916+0.030775j
[2025-08-19 16:24:09] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -86.777327+0.037648j
[2025-08-19 16:24:19] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -86.635096-0.004825j
[2025-08-19 16:24:28] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -86.558530-0.063635j
[2025-08-19 16:24:38] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -86.437445-0.001260j
[2025-08-19 16:24:47] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -86.339555-0.034824j
[2025-08-19 16:24:56] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -86.251135+0.066997j
[2025-08-19 16:25:06] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -86.403092+0.056132j
[2025-08-19 16:25:15] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -86.424562-0.033590j
[2025-08-19 16:25:24] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -86.304915-0.016291j
[2025-08-19 16:25:34] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -86.252864-0.056310j
[2025-08-19 16:25:43] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -86.160941-0.115861j
[2025-08-19 16:25:53] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -86.321770-0.040255j
[2025-08-19 16:26:02] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -86.482026-0.035983j
[2025-08-19 16:26:11] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -86.501777-0.031324j
[2025-08-19 16:26:21] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -86.467944-0.023897j
[2025-08-19 16:26:30] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -86.260560-0.041975j
[2025-08-19 16:26:40] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -86.427237-0.029973j
[2025-08-19 16:26:49] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -86.549199-0.017291j
[2025-08-19 16:26:58] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -86.454837-0.043194j
[2025-08-19 16:27:08] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -86.421769-0.031962j
[2025-08-19 16:27:17] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -86.522168-0.022704j
[2025-08-19 16:27:27] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -86.491410-0.019766j
[2025-08-19 16:27:36] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -86.590654-0.023060j
[2025-08-19 16:27:45] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -86.592820-0.032214j
[2025-08-19 16:27:55] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -86.618116-0.021063j
[2025-08-19 16:28:04] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -86.636162-0.017080j
[2025-08-19 16:28:14] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -86.561413-0.021499j
[2025-08-19 16:28:23] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -86.704072-0.000559j
[2025-08-19 16:28:32] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -86.620175-0.005163j
[2025-08-19 16:28:42] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -86.634027-0.033197j
[2025-08-19 16:28:51] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -86.699041-0.018158j
[2025-08-19 16:29:01] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -86.725173-0.019457j
[2025-08-19 16:29:10] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -86.752237-0.010021j
[2025-08-19 16:29:19] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -86.798981-0.010503j
[2025-08-19 16:29:29] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -86.845778+0.020583j
[2025-08-19 16:29:38] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -86.828531+0.015458j
[2025-08-19 16:29:48] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -86.679714-0.014853j
[2025-08-19 16:29:57] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -86.796768+0.007063j
[2025-08-19 16:30:06] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -86.853445+0.005717j
[2025-08-19 16:30:16] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -86.742831+0.010468j
[2025-08-19 16:30:25] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -86.660496+0.011047j
[2025-08-19 16:30:35] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -86.715733-0.015113j
[2025-08-19 16:30:44] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -86.783428+0.006609j
[2025-08-19 16:30:53] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -86.741154+0.036329j
[2025-08-19 16:31:03] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -86.796516+0.001072j
[2025-08-19 16:31:12] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -86.855767+0.008670j
[2025-08-19 16:31:21] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -86.936221+0.020960j
[2025-08-19 16:31:31] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -86.859431-0.018400j
[2025-08-19 16:31:40] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -86.642524-0.036272j
[2025-08-19 16:31:50] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -86.613694-0.053370j
[2025-08-19 16:31:59] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -86.683921-0.037876j
[2025-08-19 16:32:08] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -86.736499-0.005253j
[2025-08-19 16:32:18] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -86.679397-0.016263j
[2025-08-19 16:32:27] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -86.796511+0.002631j
[2025-08-19 16:32:37] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -86.892887-0.020797j
[2025-08-19 16:32:46] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -86.850552+0.026854j
[2025-08-19 16:32:55] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -86.755391-0.023843j
[2025-08-19 16:33:05] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -86.716504-0.031723j
[2025-08-19 16:33:14] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -86.784418-0.002751j
[2025-08-19 16:33:24] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -86.810533-0.009627j
[2025-08-19 16:33:33] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -86.786814-0.027339j
[2025-08-19 16:33:42] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -86.731931-0.077701j
[2025-08-19 16:33:52] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -86.913111-0.056867j
[2025-08-19 16:34:01] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -86.824508-0.027021j
[2025-08-19 16:34:11] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -86.694184-0.027740j
[2025-08-19 16:34:20] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -86.663910-0.103152j
[2025-08-19 16:34:29] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -86.866149-0.051539j
[2025-08-19 16:34:39] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -86.796276-0.079400j
[2025-08-19 16:34:48] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -86.703853-0.110990j
[2025-08-19 16:34:57] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -86.668096-0.036765j
[2025-08-19 16:35:07] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -86.434051-0.154909j
[2025-08-19 16:35:16] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -86.465538+0.043205j
[2025-08-19 16:35:26] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -86.559110-0.074999j
[2025-08-19 16:35:35] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -86.595517-0.020011j
[2025-08-19 16:35:44] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -86.765819-0.014292j
[2025-08-19 16:35:54] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -86.650427-0.066789j
[2025-08-19 16:36:03] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -86.766674-0.017555j
[2025-08-19 16:36:13] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -86.623176+0.032989j
[2025-08-19 16:36:22] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -86.770398+0.005100j
[2025-08-19 16:36:31] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -86.761812+0.022954j
[2025-08-19 16:36:41] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -86.732420+0.031741j
[2025-08-19 16:36:50] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -86.706190+0.072605j
[2025-08-19 16:37:00] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -86.754932+0.039600j
[2025-08-19 16:37:00] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-19 16:37:09] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -86.603879+0.064069j
[2025-08-19 16:37:18] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -86.680942+0.081419j
[2025-08-19 16:37:28] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -86.622387+0.056692j
[2025-08-19 16:37:37] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -86.767952-0.011832j
[2025-08-19 16:37:46] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -86.869088-0.015329j
[2025-08-19 16:37:56] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -86.793726+0.000921j
[2025-08-19 16:38:05] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -86.817540-0.016949j
[2025-08-19 16:38:15] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -86.679677+0.003969j
[2025-08-19 16:38:24] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -86.685225-0.005265j
[2025-08-19 16:38:33] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -86.701838+0.004014j
[2025-08-19 16:38:43] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -86.746785-0.003539j
[2025-08-19 16:38:52] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -86.751216+0.003791j
[2025-08-19 16:39:02] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -86.610915-0.009279j
[2025-08-19 16:39:11] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -86.533506-0.009791j
[2025-08-19 16:39:20] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -86.490559-0.005406j
[2025-08-19 16:39:30] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -86.664739+0.003667j
[2025-08-19 16:39:39] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -86.625972+0.005657j
[2025-08-19 16:39:48] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -86.735565+0.008009j
[2025-08-19 16:39:58] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -86.649574+0.013446j
[2025-08-19 16:40:07] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -86.748787+0.017310j
[2025-08-19 16:40:17] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -86.727029+0.015833j
[2025-08-19 16:40:26] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -86.817481+0.004187j
[2025-08-19 16:40:35] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -86.574923-0.003048j
[2025-08-19 16:40:45] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -86.589582-0.005480j
[2025-08-19 16:40:54] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -86.667659+0.022243j
[2025-08-19 16:41:04] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -86.761541+0.018752j
[2025-08-19 16:41:13] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -86.790101+0.039436j
[2025-08-19 16:41:22] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -86.741103+0.022137j
[2025-08-19 16:41:32] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -86.742969-0.027988j
[2025-08-19 16:41:41] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -86.849789+0.026710j
[2025-08-19 16:41:51] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -86.640587+0.003894j
[2025-08-19 16:42:00] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -86.737426+0.002708j
[2025-08-19 16:42:09] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -86.614335+0.039334j
[2025-08-19 16:42:19] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -86.701989+0.041720j
[2025-08-19 16:42:28] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -86.731116+0.019023j
[2025-08-19 16:42:37] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -86.762178-0.011761j
[2025-08-19 16:42:47] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -86.632887-0.051805j
[2025-08-19 16:42:56] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -86.707486+0.006996j
[2025-08-19 16:43:06] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -86.690140+0.004232j
[2025-08-19 16:43:15] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -86.792014+0.016150j
[2025-08-19 16:43:24] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -86.708611-0.017498j
[2025-08-19 16:43:34] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -86.797217+0.004055j
[2025-08-19 16:43:43] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -86.820300+0.015241j
[2025-08-19 16:43:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -86.879425+0.011777j
[2025-08-19 16:44:02] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -86.748533+0.001663j
[2025-08-19 16:44:11] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -86.884190-0.003856j
[2025-08-19 16:44:21] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -86.874151-0.014850j
[2025-08-19 16:44:30] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -86.569171-0.008639j
[2025-08-19 16:44:40] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -86.544816-0.050814j
[2025-08-19 16:44:49] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -86.808213-0.030381j
[2025-08-19 16:44:58] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -86.553615-0.010268j
[2025-08-19 16:45:08] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -86.510806+0.082897j
[2025-08-19 16:45:17] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -86.563232+0.014780j
[2025-08-19 16:45:27] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -86.763215+0.044145j
[2025-08-19 16:45:36] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -86.890728+0.009192j
[2025-08-19 16:45:45] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -86.811437-0.020576j
[2025-08-19 16:45:55] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -86.792584-0.009920j
[2025-08-19 16:46:04] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -86.775893+0.011639j
[2025-08-19 16:46:13] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -86.755028-0.013788j
[2025-08-19 16:46:23] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -86.920943-0.004943j
[2025-08-19 16:46:32] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -86.905960-0.005069j
[2025-08-19 16:46:42] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -87.035892-0.012183j
[2025-08-19 16:46:51] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -87.040007-0.023206j
[2025-08-19 16:47:00] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -86.992295-0.011561j
[2025-08-19 16:47:10] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -86.937849-0.001864j
[2025-08-19 16:47:19] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -86.943233+0.000689j
[2025-08-19 16:47:29] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -86.930355+0.007410j
[2025-08-19 16:47:38] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -86.897438-0.014007j
[2025-08-19 16:47:47] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -86.872932+0.033397j
[2025-08-19 16:47:57] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -86.812069-0.017058j
[2025-08-19 16:48:06] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -86.808176-0.002891j
[2025-08-19 16:48:15] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -86.744550+0.008525j
[2025-08-19 16:48:25] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -86.542091+0.047892j
[2025-08-19 16:48:34] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -86.823574-0.010552j
[2025-08-19 16:48:44] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -86.687261-0.017850j
[2025-08-19 16:48:53] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -86.910482-0.031443j
[2025-08-19 16:49:02] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -86.832161-0.016054j
[2025-08-19 16:49:12] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -86.890495-0.035737j
[2025-08-19 16:49:21] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -86.857521-0.028185j
[2025-08-19 16:49:31] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -86.942172-0.020058j
[2025-08-19 16:49:40] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -86.903118+0.005625j
[2025-08-19 16:49:49] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -86.837440-0.043131j
[2025-08-19 16:49:59] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -86.740799-0.027909j
[2025-08-19 16:50:08] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -86.739056-0.015941j
[2025-08-19 16:50:18] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -86.731369-0.011261j
[2025-08-19 16:50:27] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -86.763304-0.014291j
[2025-08-19 16:50:36] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -86.661758-0.007103j
[2025-08-19 16:50:46] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -86.704350+0.006886j
[2025-08-19 16:50:55] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -86.610388-0.017015j
[2025-08-19 16:51:05] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -86.763089-0.012272j
[2025-08-19 16:51:14] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -86.700012-0.036102j
[2025-08-19 16:51:23] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -86.824518-0.013359j
[2025-08-19 16:51:33] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -86.801294-0.001821j
[2025-08-19 16:51:42] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -86.854501-0.003544j
[2025-08-19 16:51:52] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -86.723760-0.008419j
[2025-08-19 16:52:01] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -86.801556+0.008619j
[2025-08-19 16:52:10] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -86.717370+0.013126j
[2025-08-19 16:52:20] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -86.820104-0.015037j
[2025-08-19 16:52:29] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -86.853765-0.022264j
[2025-08-19 16:52:38] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -86.828769-0.014826j
[2025-08-19 16:52:38] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-19 16:52:48] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -86.888607+0.001132j
[2025-08-19 16:52:57] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -86.833576+0.013675j
[2025-08-19 16:53:07] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -86.869745+0.009504j
[2025-08-19 16:53:16] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -86.751021-0.007357j
[2025-08-19 16:53:25] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -86.695901+0.007071j
[2025-08-19 16:53:35] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -86.756072+0.003881j
[2025-08-19 16:53:44] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -86.621777+0.001281j
[2025-08-19 16:53:54] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -86.617498-0.005772j
[2025-08-19 16:54:03] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -86.683028-0.017146j
[2025-08-19 16:54:12] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -86.726209-0.000896j
[2025-08-19 16:54:22] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -86.790278-0.005107j
[2025-08-19 16:54:31] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -86.769344-0.008375j
[2025-08-19 16:54:40] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -86.837354-0.001150j
[2025-08-19 16:54:50] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -86.753444+0.003215j
[2025-08-19 16:54:59] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -86.804982-0.002396j
[2025-08-19 16:55:09] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -86.825549+0.011642j
[2025-08-19 16:55:18] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -86.919875-0.001924j
[2025-08-19 16:55:27] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -86.962673+0.013741j
[2025-08-19 16:55:37] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -86.841205-0.000802j
[2025-08-19 16:55:46] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -86.837965+0.004456j
[2025-08-19 16:55:56] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -86.956942+0.015528j
[2025-08-19 16:56:05] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -86.816136+0.027405j
[2025-08-19 16:56:14] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -86.859846-0.010766j
[2025-08-19 16:56:24] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -86.703783-0.003685j
[2025-08-19 16:56:33] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -86.661496+0.018391j
[2025-08-19 16:56:42] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -86.655306+0.001778j
[2025-08-19 16:56:52] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -86.626503+0.008743j
[2025-08-19 16:57:01] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -86.630759-0.003008j
[2025-08-19 16:57:11] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -86.611521+0.000888j
[2025-08-19 16:57:20] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -86.733926-0.000824j
[2025-08-19 16:57:29] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -86.786143+0.011935j
[2025-08-19 16:57:39] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -86.815174+0.015425j
[2025-08-19 16:57:48] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -86.667494+0.013204j
[2025-08-19 16:57:58] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -86.798199+0.022031j
[2025-08-19 16:58:07] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -86.789391+0.005460j
[2025-08-19 16:58:16] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -86.692670-0.016596j
[2025-08-19 16:58:26] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -86.807896+0.020464j
[2025-08-19 16:58:35] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -86.846528+0.026534j
[2025-08-19 16:58:44] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -86.767356+0.012908j
[2025-08-19 16:58:54] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -86.807435-0.022450j
[2025-08-19 16:59:03] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -86.828122-0.002626j
[2025-08-19 16:59:13] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -86.720390-0.023802j
[2025-08-19 16:59:22] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -86.850060-0.006206j
[2025-08-19 16:59:31] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -86.563289-0.004425j
[2025-08-19 16:59:41] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -86.499168-0.023020j
[2025-08-19 16:59:50] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -86.463308-0.001509j
[2025-08-19 17:00:00] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -86.583409-0.025400j
[2025-08-19 17:00:09] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -86.710716-0.023113j
[2025-08-19 17:00:18] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -86.669303-0.005602j
[2025-08-19 17:00:28] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -86.512975-0.002131j
[2025-08-19 17:00:37] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -86.639177+0.004528j
[2025-08-19 17:00:46] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -86.513278+0.029734j
[2025-08-19 17:00:56] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -86.546816+0.027763j
[2025-08-19 17:01:05] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -86.563370+0.062203j
[2025-08-19 17:01:15] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -86.607339+0.026121j
[2025-08-19 17:01:24] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -86.534676+0.053123j
[2025-08-19 17:01:33] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -86.632466+0.043079j
[2025-08-19 17:01:43] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -86.598103+0.062220j
[2025-08-19 17:01:52] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -86.583271+0.073815j
[2025-08-19 17:02:01] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -86.512294+0.037527j
[2025-08-19 17:02:11] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -86.694514+0.051034j
[2025-08-19 17:02:20] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -86.559480+0.033903j
[2025-08-19 17:02:30] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -86.660206+0.019852j
[2025-08-19 17:02:39] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -86.563673+0.039088j
[2025-08-19 17:02:48] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -86.615847+0.036725j
[2025-08-19 17:02:58] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -86.567577+0.025081j
[2025-08-19 17:03:07] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -86.695470+0.017336j
[2025-08-19 17:03:17] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -86.623832+0.022305j
[2025-08-19 17:03:26] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -86.530125+0.015934j
[2025-08-19 17:03:35] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -86.673541+0.043624j
[2025-08-19 17:03:45] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -86.658615+0.024047j
[2025-08-19 17:03:54] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -86.678097+0.010116j
[2025-08-19 17:04:04] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -86.709725+0.007312j
[2025-08-19 17:04:13] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -86.724970+0.033781j
[2025-08-19 17:04:22] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -86.585878+0.000211j
[2025-08-19 17:04:32] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -86.626035-0.010079j
[2025-08-19 17:04:41] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -86.681770+0.003962j
[2025-08-19 17:04:51] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -86.656070+0.001763j
[2025-08-19 17:05:00] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -86.736662-0.000530j
[2025-08-19 17:05:09] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -86.798383-0.002835j
[2025-08-19 17:05:19] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -86.861368+0.009203j
[2025-08-19 17:05:28] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -86.955721-0.001889j
[2025-08-19 17:05:37] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -86.942708-0.000641j
[2025-08-19 17:05:47] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -86.764332+0.003186j
[2025-08-19 17:05:56] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -86.834007-0.005930j
[2025-08-19 17:06:06] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -87.021764-0.006986j
[2025-08-19 17:06:15] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -86.826028-0.014231j
[2025-08-19 17:06:24] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -86.800793-0.000021j
[2025-08-19 17:06:34] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -86.687264+0.009254j
[2025-08-19 17:06:43] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -86.699300-0.011301j
[2025-08-19 17:06:53] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -86.756665+0.000125j
[2025-08-19 17:07:02] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -86.820264+0.005003j
[2025-08-19 17:07:11] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -86.767247-0.000902j
[2025-08-19 17:07:21] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -86.815518+0.012454j
[2025-08-19 17:07:30] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -86.826516+0.009705j
[2025-08-19 17:07:39] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -86.891541+0.000454j
[2025-08-19 17:07:49] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -86.859307+0.008685j
[2025-08-19 17:07:58] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -86.867832+0.000774j
[2025-08-19 17:08:08] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -86.928541+0.017952j
[2025-08-19 17:08:17] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -86.985465+0.008467j
[2025-08-19 17:08:17] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-19 17:08:26] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -86.862201+0.022902j
[2025-08-19 17:08:36] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -86.782800+0.025971j
[2025-08-19 17:08:45] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -86.878545-0.001776j
[2025-08-19 17:08:55] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -86.993770+0.006552j
[2025-08-19 17:09:04] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -86.930532-0.003279j
[2025-08-19 17:09:13] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -86.887371-0.009234j
[2025-08-19 17:09:23] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -86.848158-0.003531j
[2025-08-19 17:09:32] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -86.965767-0.011165j
[2025-08-19 17:09:42] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -87.001337-0.004246j
[2025-08-19 17:09:51] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -87.021002-0.003639j
[2025-08-19 17:10:00] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -86.922033+0.006035j
[2025-08-19 17:10:10] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -87.024181+0.003534j
[2025-08-19 17:10:19] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -86.987356+0.014256j
[2025-08-19 17:10:29] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -86.943865+0.013438j
[2025-08-19 17:10:38] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -86.818514+0.026643j
[2025-08-19 17:10:47] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -86.868301+0.021698j
[2025-08-19 17:10:57] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -86.827528+0.003363j
[2025-08-19 17:11:06] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -86.810327+0.025629j
[2025-08-19 17:11:15] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -86.820725+0.027518j
[2025-08-19 17:11:25] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -86.718929+0.032549j
[2025-08-19 17:11:34] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -86.664959-0.005723j
[2025-08-19 17:11:44] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -86.766044+0.001441j
[2025-08-19 17:11:53] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -86.872472-0.017384j
[2025-08-19 17:12:02] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -86.843948+0.004019j
[2025-08-19 17:12:12] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -86.858044-0.005427j
[2025-08-19 17:12:21] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -86.812151-0.002161j
[2025-08-19 17:12:31] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -86.893182+0.004951j
[2025-08-19 17:12:40] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -86.700877+0.014958j
[2025-08-19 17:12:49] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -86.888476+0.013434j
[2025-08-19 17:12:59] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -86.935224+0.012700j
[2025-08-19 17:13:08] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -86.885632-0.020625j
[2025-08-19 17:13:18] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -86.844783+0.003466j
[2025-08-19 17:13:27] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -86.805377-0.009610j
[2025-08-19 17:13:36] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -86.837847-0.012268j
[2025-08-19 17:13:46] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -86.826929-0.018168j
[2025-08-19 17:13:55] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -86.724662+0.020461j
[2025-08-19 17:14:05] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -86.777843+0.000561j
[2025-08-19 17:14:14] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -86.780939-0.002879j
[2025-08-19 17:14:23] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -86.799922+0.023979j
[2025-08-19 17:14:33] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -86.823939+0.010872j
[2025-08-19 17:14:42] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -86.869831+0.004333j
[2025-08-19 17:14:52] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -86.853256+0.015686j
[2025-08-19 17:15:01] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -86.852021-0.000131j
[2025-08-19 17:15:10] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -86.785604+0.011530j
[2025-08-19 17:15:20] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -86.903700-0.008458j
[2025-08-19 17:15:29] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -86.757216-0.011118j
[2025-08-19 17:15:39] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -86.797797-0.006952j
[2025-08-19 17:15:48] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -86.817127-0.008269j
[2025-08-19 17:15:57] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -86.888541+0.018868j
[2025-08-19 17:16:07] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -86.939490-0.012813j
[2025-08-19 17:16:16] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -86.827881+0.022882j
[2025-08-19 17:16:26] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -86.829042+0.007482j
[2025-08-19 17:16:35] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -86.835571+0.022815j
[2025-08-19 17:16:44] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -86.768112+0.013619j
[2025-08-19 17:16:54] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -86.793908+0.021336j
[2025-08-19 17:17:03] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -86.895899+0.005900j
[2025-08-19 17:17:12] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -86.782330+0.019038j
[2025-08-19 17:17:22] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -86.900740+0.013737j
[2025-08-19 17:17:31] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -86.864377-0.000226j
[2025-08-19 17:17:41] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -86.988939+0.041004j
[2025-08-19 17:17:50] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -86.811770+0.029058j
[2025-08-19 17:17:59] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -86.851471-0.011874j
[2025-08-19 17:18:09] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -86.908587-0.012541j
[2025-08-19 17:18:18] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -86.856838+0.012355j
[2025-08-19 17:18:28] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -86.860555-0.026012j
[2025-08-19 17:18:37] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -86.921178-0.001656j
[2025-08-19 17:18:46] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -87.015615-0.011426j
[2025-08-19 17:18:56] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -86.902868-0.004464j
[2025-08-19 17:19:05] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -86.823585+0.017490j
[2025-08-19 17:19:14] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -86.932190+0.015343j
[2025-08-19 17:19:24] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -86.861599+0.025238j
[2025-08-19 17:19:33] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -86.930200+0.025652j
[2025-08-19 17:19:43] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -86.829700+0.015007j
[2025-08-19 17:19:52] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -86.778456-0.003273j
[2025-08-19 17:20:01] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -86.667300-0.040559j
[2025-08-19 17:20:11] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -86.737331-0.002344j
[2025-08-19 17:20:20] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -86.729249+0.025723j
[2025-08-19 17:20:30] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -86.673086-0.008291j
[2025-08-19 17:20:39] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -86.829196-0.011193j
[2025-08-19 17:20:48] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -86.696975-0.012838j
[2025-08-19 17:20:58] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -86.743937-0.007065j
[2025-08-19 17:21:07] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -86.709795-0.004558j
[2025-08-19 17:21:17] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -86.746409-0.003311j
[2025-08-19 17:21:26] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -86.851350-0.006284j
[2025-08-19 17:21:35] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -86.776293-0.008645j
[2025-08-19 17:21:45] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -86.822211-0.016864j
[2025-08-19 17:21:54] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -86.932432-0.011805j
[2025-08-19 17:22:03] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -86.809874-0.004884j
[2025-08-19 17:22:13] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -86.841279-0.019305j
[2025-08-19 17:22:22] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -86.862290-0.023383j
[2025-08-19 17:22:32] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -86.864103-0.010769j
[2025-08-19 17:22:41] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -86.992155-0.004468j
[2025-08-19 17:22:50] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -86.956773-0.026184j
[2025-08-19 17:23:00] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -86.984587-0.009502j
[2025-08-19 17:23:09] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -87.026406-0.024473j
[2025-08-19 17:23:19] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -86.913812+0.003447j
[2025-08-19 17:23:28] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -86.783578+0.003988j
[2025-08-19 17:23:37] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -86.924632-0.009197j
[2025-08-19 17:23:47] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -86.997181+0.015402j
[2025-08-19 17:23:56] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -87.072001+0.005523j
[2025-08-19 17:23:56] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-19 17:24:06] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -86.839397+0.004710j
[2025-08-19 17:24:15] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -86.766575+0.001271j
[2025-08-19 17:24:24] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -86.604775-0.004110j
[2025-08-19 17:24:34] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -86.711333+0.005683j
[2025-08-19 17:24:43] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -86.687107-0.009124j
[2025-08-19 17:24:52] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -86.786776-0.003956j
[2025-08-19 17:25:02] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -86.782203+0.011886j
[2025-08-19 17:25:11] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -86.774642-0.006450j
[2025-08-19 17:25:21] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -86.959538+0.007337j
[2025-08-19 17:25:30] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -86.797756-0.028327j
[2025-08-19 17:25:39] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -86.904843+0.020289j
[2025-08-19 17:25:49] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -86.888526-0.010283j
[2025-08-19 17:25:58] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -86.842077-0.004789j
[2025-08-19 17:26:08] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -86.901932-0.001935j
[2025-08-19 17:26:17] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -86.939123-0.003331j
[2025-08-19 17:26:26] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -86.935717+0.013167j
[2025-08-19 17:26:36] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -87.077274+0.002766j
[2025-08-19 17:26:45] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -86.913528+0.019360j
[2025-08-19 17:26:55] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -86.791253-0.011527j
[2025-08-19 17:27:04] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -86.857871+0.003128j
[2025-08-19 17:27:13] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -86.967096+0.010848j
[2025-08-19 17:27:23] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -86.811908+0.017402j
[2025-08-19 17:27:32] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -86.864057+0.001707j
[2025-08-19 17:27:41] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -86.865676+0.019207j
[2025-08-19 17:27:51] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -86.888258+0.030879j
[2025-08-19 17:28:00] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -86.900383+0.027300j
[2025-08-19 17:28:10] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -86.798383+0.000970j
[2025-08-19 17:28:19] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -86.763456-0.023857j
[2025-08-19 17:28:29] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -86.827993+0.030063j
[2025-08-19 17:28:38] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -86.861649+0.061481j
[2025-08-19 17:28:47] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -86.803872+0.023901j
[2025-08-19 17:28:57] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -86.862944+0.017507j
[2025-08-19 17:29:06] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -86.821496+0.003462j
[2025-08-19 17:29:15] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -86.792000+0.035408j
[2025-08-19 17:29:25] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -86.832412+0.029528j
[2025-08-19 17:29:34] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -86.886785+0.018003j
[2025-08-19 17:29:44] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -86.839621+0.030500j
[2025-08-19 17:29:53] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -86.851280+0.008954j
[2025-08-19 17:30:02] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -86.799593+0.007711j
[2025-08-19 17:30:12] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -86.775678-0.006378j
[2025-08-19 17:30:21] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -86.888981+0.003378j
[2025-08-19 17:30:31] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -86.807837-0.003695j
[2025-08-19 17:30:40] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -86.865949-0.008939j
[2025-08-19 17:30:49] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -86.827145-0.001106j
[2025-08-19 17:30:59] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -86.780562-0.010646j
[2025-08-19 17:31:08] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -86.776477-0.010240j
[2025-08-19 17:31:18] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -86.735343-0.021881j
[2025-08-19 17:31:27] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -86.728682-0.013617j
[2025-08-19 17:31:36] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -86.773875-0.021582j
[2025-08-19 17:31:46] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -86.787714-0.018790j
[2025-08-19 17:31:46] ✅ Training completed | Restarts: 2
[2025-08-19 17:31:46] ============================================================
[2025-08-19 17:31:46] Training completed | Runtime: 9901.5s
[2025-08-19 17:32:00] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-19 17:32:00] ============================================================
