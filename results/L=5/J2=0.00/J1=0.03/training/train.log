[2025-08-18 23:47:04] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-18 23:47:04]   - 迭代次数: final
[2025-08-18 23:47:04]   - 能量: -85.025512+0.000057j ± 0.055059
[2025-08-18 23:47:04]   - 时间戳: 2025-07-30T23:38:53.138432
[2025-08-18 23:47:12] ✓ 变分状态参数已从checkpoint恢复
[2025-08-18 23:47:12] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-18 23:47:12] ==================================================
[2025-08-18 23:47:12] GCNN for Shastry-Sutherland Model
[2025-08-18 23:47:12] ==================================================
[2025-08-18 23:47:12] System parameters:
[2025-08-18 23:47:12]   - System size: L=5, N=100
[2025-08-18 23:47:12]   - System parameters: J1=0.03, J2=0.0, Q=1.0
[2025-08-18 23:47:12] --------------------------------------------------
[2025-08-18 23:47:12] Model parameters:
[2025-08-18 23:47:12]   - Number of layers = 4
[2025-08-18 23:47:12]   - Number of features = 4
[2025-08-18 23:47:12]   - Total parameters = 19628
[2025-08-18 23:47:12] --------------------------------------------------
[2025-08-18 23:47:12] Training parameters:
[2025-08-18 23:47:12]   - Learning rate: 0.015
[2025-08-18 23:47:12]   - Total iterations: 1050
[2025-08-18 23:47:12]   - Annealing cycles: 3
[2025-08-18 23:47:12]   - Initial period: 150
[2025-08-18 23:47:12]   - Period multiplier: 2.0
[2025-08-18 23:47:12]   - Temperature range: 0.0-1.0
[2025-08-18 23:47:12]   - Samples: 4096
[2025-08-18 23:47:12]   - Discarded samples: 0
[2025-08-18 23:47:12]   - Chunk size: 2048
[2025-08-18 23:47:12]   - Diagonal shift: 0.2
[2025-08-18 23:47:12]   - Gradient clipping: 1.0
[2025-08-18 23:47:12]   - Checkpoint enabled: interval=100
[2025-08-18 23:47:12]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.03/training/checkpoints
[2025-08-18 23:47:12] --------------------------------------------------
[2025-08-18 23:47:12] Device status:
[2025-08-18 23:47:12]   - Devices model: A100
[2025-08-18 23:47:12]   - Number of devices: 1
[2025-08-18 23:47:12]   - Sharding: True
[2025-08-18 23:47:12] ============================================================
[2025-08-18 23:47:46] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -84.422142+0.049234j
[2025-08-18 23:48:08] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -84.616088+0.052357j
[2025-08-18 23:48:17] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -84.630734+0.039905j
[2025-08-18 23:48:27] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -84.599417+0.019405j
[2025-08-18 23:48:36] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -84.365260+0.011620j
[2025-08-18 23:48:46] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -84.520580+0.022437j
[2025-08-18 23:48:55] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -84.412615+0.033344j
[2025-08-18 23:49:05] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -84.293302+0.028665j
[2025-08-18 23:49:14] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -84.406805+0.056274j
[2025-08-18 23:49:24] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -84.417905+0.042330j
[2025-08-18 23:49:33] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -84.525558+0.023500j
[2025-08-18 23:49:42] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -84.417771+0.041252j
[2025-08-18 23:49:52] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -84.361261+0.035992j
[2025-08-18 23:50:01] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -84.364683-0.004619j
[2025-08-18 23:50:11] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -84.322792+0.018997j
[2025-08-18 23:50:20] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -84.246587+0.005969j
[2025-08-18 23:50:30] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -84.224942-0.000258j
[2025-08-18 23:50:39] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -84.409255-0.013466j
[2025-08-18 23:50:49] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -84.447911+0.033920j
[2025-08-18 23:50:58] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -84.414274+0.006593j
[2025-08-18 23:51:08] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -84.582189-0.007731j
[2025-08-18 23:51:17] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -84.449171+0.002354j
[2025-08-18 23:51:27] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -84.533434+0.004115j
[2025-08-18 23:51:36] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -84.515636+0.015009j
[2025-08-18 23:51:46] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -84.372832-0.000428j
[2025-08-18 23:51:55] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -84.380378-0.002945j
[2025-08-18 23:52:05] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -84.356557-0.007299j
[2025-08-18 23:52:14] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -84.296262+0.005831j
[2025-08-18 23:52:24] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -84.295386-0.004394j
[2025-08-18 23:52:33] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -84.301537+0.027342j
[2025-08-18 23:52:42] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -84.451509+0.002335j
[2025-08-18 23:52:52] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -84.318745+0.008316j
[2025-08-18 23:53:01] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -84.252316-0.008876j
[2025-08-18 23:53:11] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -84.321413-0.013989j
[2025-08-18 23:53:20] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -84.323553-0.004737j
[2025-08-18 23:53:30] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -84.220401+0.004261j
[2025-08-18 23:53:39] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -84.280114+0.016271j
[2025-08-18 23:53:49] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -84.272092+0.009889j
[2025-08-18 23:53:58] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -84.257403-0.013743j
[2025-08-18 23:54:08] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -84.316848-0.004139j
[2025-08-18 23:54:17] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -84.222953+0.002571j
[2025-08-18 23:54:27] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -84.198396-0.015056j
[2025-08-18 23:54:36] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -84.106879+0.015801j
[2025-08-18 23:54:46] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -84.227593-0.015696j
[2025-08-18 23:54:55] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -84.107855-0.010268j
[2025-08-18 23:55:05] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -84.059455-0.008036j
[2025-08-18 23:55:14] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -84.141105-0.007982j
[2025-08-18 23:55:24] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -84.144588-0.002806j
[2025-08-18 23:55:33] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -84.205818+0.023464j
[2025-08-18 23:55:42] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -84.271304-0.002078j
[2025-08-18 23:55:52] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -84.157840+0.019846j
[2025-08-18 23:56:01] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -84.252662-0.009046j
[2025-08-18 23:56:11] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -84.169142-0.021478j
[2025-08-18 23:56:20] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -84.048228-0.014801j
[2025-08-18 23:56:30] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -84.091848+0.021022j
[2025-08-18 23:56:39] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -84.330561+0.016545j
[2025-08-18 23:56:49] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -84.256700-0.019346j
[2025-08-18 23:56:58] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -84.368670+0.006577j
[2025-08-18 23:57:08] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -84.062374-0.002443j
[2025-08-18 23:57:17] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -84.169761+0.010502j
[2025-08-18 23:57:27] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -84.211283-0.015013j
[2025-08-18 23:57:36] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -84.310650+0.025134j
[2025-08-18 23:57:46] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -84.179865-0.016105j
[2025-08-18 23:57:55] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -84.169563-0.009369j
[2025-08-18 23:58:05] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -84.208610-0.004131j
[2025-08-18 23:58:14] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -84.306362-0.008822j
[2025-08-18 23:58:24] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -84.173573-0.013678j
[2025-08-18 23:58:33] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -84.245535-0.021055j
[2025-08-18 23:58:43] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -84.222076-0.007138j
[2025-08-18 23:58:52] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -84.189088-0.004834j
[2025-08-18 23:59:02] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -84.244722-0.010157j
[2025-08-18 23:59:11] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -84.243574+0.003309j
[2025-08-18 23:59:21] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -84.327631+0.019118j
[2025-08-18 23:59:30] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -84.458458+0.006423j
[2025-08-18 23:59:39] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -84.281848+0.015769j
[2025-08-18 23:59:49] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -84.177668+0.002632j
[2025-08-18 23:59:58] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -84.323939-0.011112j
[2025-08-19 00:00:08] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -84.313872+0.002665j
[2025-08-19 00:00:17] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -84.369792-0.003569j
[2025-08-19 00:00:27] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -84.478500+0.014816j
[2025-08-19 00:00:36] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -84.506432-0.009560j
[2025-08-19 00:00:46] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -84.520755+0.012021j
[2025-08-19 00:00:55] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -84.497233+0.019063j
[2025-08-19 00:01:05] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -84.439623+0.013722j
[2025-08-19 00:01:14] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -84.498440+0.001995j
[2025-08-19 00:01:24] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -84.574434+0.023203j
[2025-08-19 00:01:33] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -84.586926+0.004444j
[2025-08-19 00:01:43] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -84.564694+0.007284j
[2025-08-19 00:01:52] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -84.506123-0.000819j
[2025-08-19 00:02:02] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -84.444817+0.008784j
[2025-08-19 00:02:11] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -84.351763+0.006995j
[2025-08-19 00:02:21] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -84.217387+0.018931j
[2025-08-19 00:02:30] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -84.398930+0.002106j
[2025-08-19 00:02:39] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -84.291887-0.026704j
[2025-08-19 00:02:49] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -84.289034+0.018956j
[2025-08-19 00:02:58] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -84.294288+0.038439j
[2025-08-19 00:03:08] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -84.417567+0.018689j
[2025-08-19 00:03:17] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -84.419423+0.015541j
[2025-08-19 00:03:27] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -84.379782+0.016129j
[2025-08-19 00:03:36] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -84.506233-0.001754j
[2025-08-19 00:03:36] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-19 00:03:46] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -84.456668-0.006771j
[2025-08-19 00:03:55] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -84.515999-0.001983j
[2025-08-19 00:04:05] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -84.450027-0.009270j
[2025-08-19 00:04:14] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -84.384573+0.021793j
[2025-08-19 00:04:24] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -84.504649-0.000598j
[2025-08-19 00:04:33] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -84.527577-0.001655j
[2025-08-19 00:04:43] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -84.374278-0.009584j
[2025-08-19 00:04:52] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -84.406341+0.009217j
[2025-08-19 00:05:02] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -84.587260-0.005008j
[2025-08-19 00:05:11] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -84.377343-0.015856j
[2025-08-19 00:05:21] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -84.362548+0.006826j
[2025-08-19 00:05:30] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -84.503081+0.022375j
[2025-08-19 00:05:39] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -84.500644+0.006439j
[2025-08-19 00:05:49] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -84.504365+0.008008j
[2025-08-19 00:05:58] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -84.555233-0.013309j
[2025-08-19 00:06:08] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -84.465116+0.006007j
[2025-08-19 00:06:17] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -84.390013-0.004719j
[2025-08-19 00:06:27] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -84.504325-0.009135j
[2025-08-19 00:06:36] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -84.430518+0.006738j
[2025-08-19 00:06:46] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -84.357051+0.006825j
[2025-08-19 00:06:55] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -84.456669-0.000712j
[2025-08-19 00:07:05] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -84.436193+0.013701j
[2025-08-19 00:07:14] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -84.416655+0.011465j
[2025-08-19 00:07:24] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -84.276296-0.031294j
[2025-08-19 00:07:33] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -84.209641-0.005948j
[2025-08-19 00:07:43] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -84.248657+0.004607j
[2025-08-19 00:07:52] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -84.272220+0.005456j
[2025-08-19 00:08:02] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -84.246746-0.013769j
[2025-08-19 00:08:11] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -84.212612+0.020433j
[2025-08-19 00:08:20] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -84.321444-0.002741j
[2025-08-19 00:08:30] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -84.302598+0.033102j
[2025-08-19 00:08:39] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -84.394407-0.002358j
[2025-08-19 00:08:49] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -84.498162-0.002379j
[2025-08-19 00:08:58] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -84.397124+0.009920j
[2025-08-19 00:09:08] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -84.424524-0.009538j
[2025-08-19 00:09:17] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -84.266560-0.014977j
[2025-08-19 00:09:27] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -84.184418+0.005012j
[2025-08-19 00:09:36] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -84.210134-0.001476j
[2025-08-19 00:09:46] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -84.184087-0.004428j
[2025-08-19 00:09:55] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -84.093629-0.016490j
[2025-08-19 00:10:05] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -84.237714+0.009972j
[2025-08-19 00:10:14] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -84.208428+0.021904j
[2025-08-19 00:10:24] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -84.200215+0.004477j
[2025-08-19 00:10:33] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -84.362147-0.005779j
[2025-08-19 00:10:43] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -84.344978-0.021629j
[2025-08-19 00:10:52] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -84.176231-0.013892j
[2025-08-19 00:11:02] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -84.192293-0.002423j
[2025-08-19 00:11:11] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -84.217826-0.019605j
[2025-08-19 00:11:20] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -84.190828-0.014128j
[2025-08-19 00:11:30] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -84.249613+0.009013j
[2025-08-19 00:11:30] RESTART #1 | Period: 300
[2025-08-19 00:11:39] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -84.164025+0.004136j
[2025-08-19 00:11:49] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -84.040361+0.009179j
[2025-08-19 00:11:58] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -84.055294+0.005664j
[2025-08-19 00:12:08] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -84.193244-0.005739j
[2025-08-19 00:12:17] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -84.265821-0.006533j
[2025-08-19 00:12:27] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -84.235659+0.002931j
[2025-08-19 00:12:36] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -84.169541+0.033241j
[2025-08-19 00:12:46] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -84.211607-0.010390j
[2025-08-19 00:12:55] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -84.117927+0.016029j
[2025-08-19 00:13:05] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -84.145139-0.019614j
[2025-08-19 00:13:14] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -84.255135-0.026143j
[2025-08-19 00:13:24] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -84.332346-0.008116j
[2025-08-19 00:13:33] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -84.317454-0.010242j
[2025-08-19 00:13:43] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -84.204101-0.020589j
[2025-08-19 00:13:52] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -84.274858-0.006758j
[2025-08-19 00:14:02] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -84.304647-0.006202j
[2025-08-19 00:14:11] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -84.235827+0.002855j
[2025-08-19 00:14:20] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -84.274510+0.021419j
[2025-08-19 00:14:30] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -84.286486+0.010276j
[2025-08-19 00:14:39] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -84.267672+0.026789j
[2025-08-19 00:14:49] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -84.281420-0.013981j
[2025-08-19 00:14:58] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -84.089499-0.024219j
[2025-08-19 00:15:08] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -84.239113+0.011419j
[2025-08-19 00:15:17] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -84.248298-0.008550j
[2025-08-19 00:15:27] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -84.328774+0.007271j
[2025-08-19 00:15:36] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -84.321052-0.001443j
[2025-08-19 00:15:46] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -84.248250-0.006329j
[2025-08-19 00:15:55] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -84.237497+0.013061j
[2025-08-19 00:16:05] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -84.104583-0.016207j
[2025-08-19 00:16:14] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.981679+0.005781j
[2025-08-19 00:16:24] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -84.089530+0.004937j
[2025-08-19 00:16:33] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -84.209607+0.026994j
[2025-08-19 00:16:43] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -84.258577-0.006536j
[2025-08-19 00:16:52] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -84.205227-0.020814j
[2025-08-19 00:17:02] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -84.162432-0.009897j
[2025-08-19 00:17:11] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -84.149042-0.002966j
[2025-08-19 00:17:20] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -84.167397+0.004805j
[2025-08-19 00:17:30] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -84.146516+0.002386j
[2025-08-19 00:17:39] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -84.270485-0.002861j
[2025-08-19 00:17:49] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -84.150832+0.010251j
[2025-08-19 00:17:58] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -84.182873+0.018853j
[2025-08-19 00:18:08] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -84.213432-0.026227j
[2025-08-19 00:18:17] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -84.138058+0.010641j
[2025-08-19 00:18:27] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -84.236992-0.003240j
[2025-08-19 00:18:36] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -84.130414+0.004014j
[2025-08-19 00:18:46] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -84.220894-0.015013j
[2025-08-19 00:18:55] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -84.149861+0.005274j
[2025-08-19 00:19:05] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -84.138301+0.003980j
[2025-08-19 00:19:14] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -84.270715+0.013225j
[2025-08-19 00:19:24] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -84.253303+0.004842j
[2025-08-19 00:19:24] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-19 00:19:33] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -84.208943-0.001877j
[2025-08-19 00:19:43] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -84.292803-0.008784j
[2025-08-19 00:19:52] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -84.280812+0.000282j
[2025-08-19 00:20:02] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -84.260663-0.001823j
[2025-08-19 00:20:11] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -84.405252-0.006773j
[2025-08-19 00:20:21] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -84.429399+0.003285j
[2025-08-19 00:20:30] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -84.518786-0.003810j
[2025-08-19 00:20:39] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -84.329527-0.014661j
[2025-08-19 00:20:49] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -84.347277-0.008219j
[2025-08-19 00:20:58] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -84.362910-0.002624j
[2025-08-19 00:21:08] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -84.358211-0.000058j
[2025-08-19 00:21:17] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -84.237842+0.002486j
[2025-08-19 00:21:27] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -84.314108+0.013538j
[2025-08-19 00:21:36] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -84.322151-0.001690j
[2025-08-19 00:21:46] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -84.267640-0.000306j
[2025-08-19 00:21:55] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -84.259735-0.002115j
[2025-08-19 00:22:05] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -84.322670-0.003065j
[2025-08-19 00:22:14] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -84.178430-0.020060j
[2025-08-19 00:22:24] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -84.281522+0.002519j
[2025-08-19 00:22:33] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -84.339581-0.011233j
[2025-08-19 00:22:43] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -84.170773-0.019988j
[2025-08-19 00:22:52] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -84.264191-0.035173j
[2025-08-19 00:23:02] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -84.277853+0.000218j
[2025-08-19 00:23:11] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -84.326523-0.002246j
[2025-08-19 00:23:21] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -84.242196-0.003243j
[2025-08-19 00:23:30] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -84.279056-0.008571j
[2025-08-19 00:23:39] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -84.175584+0.003909j
[2025-08-19 00:23:49] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -84.109323+0.005629j
[2025-08-19 00:23:58] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -84.243295+0.002657j
[2025-08-19 00:24:08] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -84.151740-0.020085j
[2025-08-19 00:24:17] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -84.020733+0.005331j
[2025-08-19 00:24:27] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -84.160204+0.000310j
[2025-08-19 00:24:36] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -84.143031+0.019305j
[2025-08-19 00:24:46] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -84.110826+0.050629j
[2025-08-19 00:24:55] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -84.343177-0.009542j
[2025-08-19 00:25:05] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -84.211741-0.006062j
[2025-08-19 00:25:14] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -84.186741+0.010138j
[2025-08-19 00:25:24] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -84.285443+0.029650j
[2025-08-19 00:25:33] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -84.337779+0.014635j
[2025-08-19 00:25:43] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -84.364765+0.010390j
[2025-08-19 00:25:52] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -84.325225+0.002384j
[2025-08-19 00:26:02] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -84.295628-0.048833j
[2025-08-19 00:26:11] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -84.393267+0.000463j
[2025-08-19 00:26:20] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -84.271170-0.020751j
[2025-08-19 00:26:30] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -84.347067-0.002740j
[2025-08-19 00:26:39] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -84.277298+0.012730j
[2025-08-19 00:26:49] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -84.312746-0.000814j
[2025-08-19 00:26:58] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -84.304715+0.000690j
[2025-08-19 00:27:08] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -84.404006+0.012178j
[2025-08-19 00:27:17] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -84.246946-0.005625j
[2025-08-19 00:27:27] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -84.082522-0.005848j
[2025-08-19 00:27:36] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -84.120442-0.016634j
[2025-08-19 00:27:46] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -84.142152-0.000346j
[2025-08-19 00:27:55] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -84.297102-0.011660j
[2025-08-19 00:28:05] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -84.240909-0.008215j
[2025-08-19 00:28:14] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -84.447956+0.006567j
[2025-08-19 00:28:24] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -84.346940+0.010619j
[2025-08-19 00:28:33] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -84.181953+0.030439j
[2025-08-19 00:28:43] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -84.223580+0.007797j
[2025-08-19 00:28:52] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -84.239041+0.027410j
[2025-08-19 00:29:02] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -84.262895+0.004277j
[2025-08-19 00:29:11] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -84.340942-0.017189j
[2025-08-19 00:29:20] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -84.342862+0.003365j
[2025-08-19 00:29:30] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -84.377888+0.023991j
[2025-08-19 00:29:39] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -84.411059+0.004203j
[2025-08-19 00:29:49] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -84.374435+0.011414j
[2025-08-19 00:29:58] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -84.390527-0.008540j
[2025-08-19 00:30:08] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -84.443135+0.016548j
[2025-08-19 00:30:17] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -84.437892-0.013032j
[2025-08-19 00:30:27] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -84.388114-0.002988j
[2025-08-19 00:30:36] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -84.434789-0.005844j
[2025-08-19 00:30:46] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -84.289396+0.016615j
[2025-08-19 00:30:55] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -84.314261-0.010906j
[2025-08-19 00:31:05] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -84.281192+0.021029j
[2025-08-19 00:31:14] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -84.242464+0.003851j
[2025-08-19 00:31:24] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -84.083747+0.009930j
[2025-08-19 00:31:33] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -84.133027-0.005726j
[2025-08-19 00:31:43] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -84.171738-0.012756j
[2025-08-19 00:31:52] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -84.347579+0.018648j
[2025-08-19 00:32:02] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -84.342060+0.000224j
[2025-08-19 00:32:11] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -84.150479+0.002327j
[2025-08-19 00:32:20] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -84.220114+0.015750j
[2025-08-19 00:32:30] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -84.299795+0.006138j
[2025-08-19 00:32:39] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -84.323780-0.011648j
[2025-08-19 00:32:49] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -84.331162-0.003090j
[2025-08-19 00:32:58] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -84.279295+0.009490j
[2025-08-19 00:33:08] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -84.341124+0.001802j
[2025-08-19 00:33:17] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -84.479250+0.001820j
[2025-08-19 00:33:27] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -84.377081+0.000562j
[2025-08-19 00:33:36] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -84.294821+0.014252j
[2025-08-19 00:33:46] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -84.389485-0.008070j
[2025-08-19 00:33:55] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -84.328890-0.020438j
[2025-08-19 00:34:05] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -84.382180+0.001471j
[2025-08-19 00:34:14] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -84.503099-0.039975j
[2025-08-19 00:34:24] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -84.330316-0.010289j
[2025-08-19 00:34:33] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -84.328435+0.011230j
[2025-08-19 00:34:43] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -84.214202+0.013320j
[2025-08-19 00:34:52] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -84.311581-0.002394j
[2025-08-19 00:35:02] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -84.315531-0.003378j
[2025-08-19 00:35:11] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -84.299097+0.003056j
[2025-08-19 00:35:11] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-19 00:35:21] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -84.370179-0.000926j
[2025-08-19 00:35:30] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -84.454554-0.008072j
[2025-08-19 00:35:39] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -84.270554-0.002362j
[2025-08-19 00:35:49] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -84.350434-0.004875j
[2025-08-19 00:35:58] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -84.180842-0.011274j
[2025-08-19 00:36:08] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -84.133296+0.016930j
[2025-08-19 00:36:17] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -84.166729-0.008988j
[2025-08-19 00:36:27] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -84.176485-0.017509j
[2025-08-19 00:36:36] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -84.271652+0.009421j
[2025-08-19 00:36:46] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -84.316070+0.019661j
[2025-08-19 00:36:55] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -84.320564+0.017648j
[2025-08-19 00:37:05] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -84.491151+0.013377j
[2025-08-19 00:37:14] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -84.395949+0.015109j
[2025-08-19 00:37:24] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -84.364338+0.000143j
[2025-08-19 00:37:33] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -84.474409-0.011481j
[2025-08-19 00:37:43] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -84.524599+0.022117j
[2025-08-19 00:37:52] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -84.441170+0.026603j
[2025-08-19 00:38:02] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -84.324629-0.000302j
[2025-08-19 00:38:11] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -84.402905+0.001214j
[2025-08-19 00:38:21] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -84.308101-0.008898j
[2025-08-19 00:38:30] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -84.241686-0.010821j
[2025-08-19 00:38:39] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -84.236146-0.021924j
[2025-08-19 00:38:49] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -84.389882+0.004739j
[2025-08-19 00:38:58] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -84.268793+0.017466j
[2025-08-19 00:39:08] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -84.329670-0.026418j
[2025-08-19 00:39:17] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -84.155602-0.010309j
[2025-08-19 00:39:27] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -84.297954-0.030348j
[2025-08-19 00:39:36] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -84.256795-0.004051j
[2025-08-19 00:39:46] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -84.358082-0.027598j
[2025-08-19 00:39:55] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -84.284875-0.006975j
[2025-08-19 00:40:05] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -84.299300+0.002442j
[2025-08-19 00:40:14] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -84.519016-0.013683j
[2025-08-19 00:40:24] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -84.400552-0.005185j
[2025-08-19 00:40:33] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -84.354303+0.025082j
[2025-08-19 00:40:43] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -84.301388-0.007339j
[2025-08-19 00:40:52] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -84.369777-0.006021j
[2025-08-19 00:41:02] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -84.243016+0.008224j
[2025-08-19 00:41:11] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -84.192195-0.000886j
[2025-08-19 00:41:21] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -84.266370+0.011053j
[2025-08-19 00:41:30] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -84.201191-0.019456j
[2025-08-19 00:41:39] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -84.285351+0.001559j
[2025-08-19 00:41:49] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -84.267971-0.012610j
[2025-08-19 00:41:58] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -84.356639+0.005690j
[2025-08-19 00:42:08] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -84.372286+0.023205j
[2025-08-19 00:42:17] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -84.295865-0.000579j
[2025-08-19 00:42:27] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -84.341842+0.011235j
[2025-08-19 00:42:36] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -84.349056+0.014643j
[2025-08-19 00:42:46] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -84.272052-0.007385j
[2025-08-19 00:42:55] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -84.270570-0.002273j
[2025-08-19 00:43:05] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -84.250885+0.006366j
[2025-08-19 00:43:14] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -84.318799+0.007620j
[2025-08-19 00:43:24] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -84.374871+0.001045j
[2025-08-19 00:43:33] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -84.444759+0.006345j
[2025-08-19 00:43:43] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -84.340239-0.003913j
[2025-08-19 00:43:52] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -84.242125-0.009685j
[2025-08-19 00:44:02] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -84.505530-0.009210j
[2025-08-19 00:44:11] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -84.396033-0.017197j
[2025-08-19 00:44:20] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -84.462280+0.008135j
[2025-08-19 00:44:30] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -84.455094+0.012035j
[2025-08-19 00:44:39] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -84.504810+0.001757j
[2025-08-19 00:44:49] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -84.546828-0.013131j
[2025-08-19 00:44:58] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -84.436709+0.017778j
[2025-08-19 00:45:08] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -84.276581-0.022295j
[2025-08-19 00:45:17] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -84.405791-0.006142j
[2025-08-19 00:45:27] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -84.499424+0.006871j
[2025-08-19 00:45:36] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -84.371244+0.013361j
[2025-08-19 00:45:46] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -84.264950+0.003209j
[2025-08-19 00:45:55] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -84.326668+0.013614j
[2025-08-19 00:46:05] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -84.348048+0.005392j
[2025-08-19 00:46:14] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -84.327606+0.008773j
[2025-08-19 00:46:24] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -84.413033-0.002820j
[2025-08-19 00:46:33] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -84.395830-0.019071j
[2025-08-19 00:46:43] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -84.373719+0.000282j
[2025-08-19 00:46:52] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -84.330797-0.008323j
[2025-08-19 00:47:02] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -84.274960+0.006731j
[2025-08-19 00:47:11] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -84.200219-0.013107j
[2025-08-19 00:47:21] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -84.223603-0.015151j
[2025-08-19 00:47:30] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -84.268233+0.010094j
[2025-08-19 00:47:39] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -84.298530+0.014570j
[2025-08-19 00:47:49] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -84.334424-0.010357j
[2025-08-19 00:47:58] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -84.509618+0.012659j
[2025-08-19 00:48:08] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -84.285751+0.026005j
[2025-08-19 00:48:17] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -84.300044-0.005576j
[2025-08-19 00:48:27] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -84.314470-0.018884j
[2025-08-19 00:48:36] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -84.303647+0.001754j
[2025-08-19 00:48:46] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -84.364675-0.006846j
[2025-08-19 00:48:55] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -84.508358-0.010273j
[2025-08-19 00:49:05] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -84.483391-0.009675j
[2025-08-19 00:49:14] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -84.457478+0.025457j
[2025-08-19 00:49:24] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -84.455731+0.003755j
[2025-08-19 00:49:33] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -84.440344-0.011678j
[2025-08-19 00:49:43] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -84.433780-0.004867j
[2025-08-19 00:49:52] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -84.467831-0.002557j
[2025-08-19 00:50:02] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -84.262837-0.028173j
[2025-08-19 00:50:11] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -84.361220+0.018288j
[2025-08-19 00:50:21] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -84.229155+0.003512j
[2025-08-19 00:50:30] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -84.245352+0.031865j
[2025-08-19 00:50:39] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -84.295739+0.048810j
[2025-08-19 00:50:49] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -84.294127+0.010459j
[2025-08-19 00:50:58] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -84.395663+0.019831j
[2025-08-19 00:50:58] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-19 00:51:08] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -84.404430+0.005062j
[2025-08-19 00:51:17] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -84.191459+0.012741j
[2025-08-19 00:51:27] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -84.369918-0.000038j
[2025-08-19 00:51:36] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -84.326063+0.032282j
[2025-08-19 00:51:46] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -84.275745+0.001290j
[2025-08-19 00:51:55] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -84.307916+0.008712j
[2025-08-19 00:52:05] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -84.330717-0.001762j
[2025-08-19 00:52:14] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -84.361933-0.010444j
[2025-08-19 00:52:24] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -84.265858-0.003097j
[2025-08-19 00:52:33] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -84.296349+0.042331j
[2025-08-19 00:52:43] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -84.300336+0.005155j
[2025-08-19 00:52:52] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -84.300656+0.017077j
[2025-08-19 00:53:02] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -84.290809-0.001446j
[2025-08-19 00:53:11] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -84.345551-0.011469j
[2025-08-19 00:53:21] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -84.156539-0.014753j
[2025-08-19 00:53:30] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -84.196324-0.002201j
[2025-08-19 00:53:39] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -84.264165-0.000124j
[2025-08-19 00:53:49] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -84.281939-0.017382j
[2025-08-19 00:53:58] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -84.369936-0.022371j
[2025-08-19 00:54:08] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -84.270755-0.015443j
[2025-08-19 00:54:17] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -84.170995+0.013298j
[2025-08-19 00:54:27] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -84.117285-0.001115j
[2025-08-19 00:54:36] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -84.071890+0.009669j
[2025-08-19 00:54:46] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -84.054858-0.009491j
[2025-08-19 00:54:55] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -84.205179-0.022334j
[2025-08-19 00:55:05] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -84.191512-0.005001j
[2025-08-19 00:55:14] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -84.188175-0.011822j
[2025-08-19 00:55:24] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -84.382723+0.021658j
[2025-08-19 00:55:33] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -84.243877-0.027025j
[2025-08-19 00:55:43] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -84.266856+0.000005j
[2025-08-19 00:55:52] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -84.192140+0.000186j
[2025-08-19 00:56:02] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -84.222772-0.007655j
[2025-08-19 00:56:11] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -84.307547-0.000615j
[2025-08-19 00:56:21] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -84.287978+0.026103j
[2025-08-19 00:56:30] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -84.270845+0.016444j
[2025-08-19 00:56:39] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -84.288217-0.004314j
[2025-08-19 00:56:49] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -84.279545+0.014042j
[2025-08-19 00:56:58] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -84.326589+0.025382j
[2025-08-19 00:57:08] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -84.287391-0.014062j
[2025-08-19 00:57:17] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -84.331282-0.000191j
[2025-08-19 00:57:27] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -84.383887-0.004209j
[2025-08-19 00:57:36] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -84.363407+0.024752j
[2025-08-19 00:57:46] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -84.468062+0.007121j
[2025-08-19 00:57:55] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -84.258625-0.000719j
[2025-08-19 00:58:05] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -84.284275-0.017832j
[2025-08-19 00:58:14] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -84.286423-0.005301j
[2025-08-19 00:58:24] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -84.236463+0.012199j
[2025-08-19 00:58:33] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -84.305108+0.037702j
[2025-08-19 00:58:43] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -84.247352+0.008091j
[2025-08-19 00:58:52] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -84.233373+0.028133j
[2025-08-19 00:58:52] RESTART #2 | Period: 600
[2025-08-19 00:59:02] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -84.161398-0.006971j
[2025-08-19 00:59:11] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -84.153630-0.015192j
[2025-08-19 00:59:21] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -84.156314-0.017890j
[2025-08-19 00:59:30] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -84.170633-0.000364j
[2025-08-19 00:59:39] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -84.262500+0.010430j
[2025-08-19 00:59:49] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -84.172208-0.009848j
[2025-08-19 00:59:58] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -84.089426+0.015048j
[2025-08-19 01:00:08] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -84.304750+0.012963j
[2025-08-19 01:00:17] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -84.358991+0.003251j
[2025-08-19 01:00:27] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -84.214327-0.035203j
[2025-08-19 01:00:36] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -84.249582+0.001073j
[2025-08-19 01:00:46] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -84.321409+0.022595j
[2025-08-19 01:00:55] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -84.275366+0.005918j
[2025-08-19 01:01:05] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -84.262306+0.021917j
[2025-08-19 01:01:14] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -84.253424-0.004297j
[2025-08-19 01:01:24] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -84.353072+0.013562j
[2025-08-19 01:01:33] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -84.292958+0.009912j
[2025-08-19 01:01:43] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -84.292337+0.014356j
[2025-08-19 01:01:52] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -84.324538+0.012696j
[2025-08-19 01:02:02] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -84.418570-0.017110j
[2025-08-19 01:02:11] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -84.395817+0.007128j
[2025-08-19 01:02:20] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -84.311439-0.027562j
[2025-08-19 01:02:30] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -84.323986-0.001417j
[2025-08-19 01:02:39] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -84.298973-0.002359j
[2025-08-19 01:02:49] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -84.365724-0.007266j
[2025-08-19 01:02:58] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -84.378797-0.012093j
[2025-08-19 01:03:08] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -84.335987-0.003986j
[2025-08-19 01:03:17] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -84.418676+0.005407j
[2025-08-19 01:03:27] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -84.301330-0.003305j
[2025-08-19 01:03:36] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -84.446279+0.002354j
[2025-08-19 01:03:46] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -84.434308+0.003556j
[2025-08-19 01:03:55] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -84.251959+0.002680j
[2025-08-19 01:04:05] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -84.283836+0.018437j
[2025-08-19 01:04:14] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -84.506378-0.020834j
[2025-08-19 01:04:24] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -84.544252+0.013777j
[2025-08-19 01:04:33] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -84.593771+0.002164j
[2025-08-19 01:04:43] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -84.366179-0.005603j
[2025-08-19 01:04:52] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -84.478707-0.001242j
[2025-08-19 01:05:02] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -84.498017+0.005660j
[2025-08-19 01:05:11] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -84.417923-0.001096j
[2025-08-19 01:05:21] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -84.439843+0.009233j
[2025-08-19 01:05:30] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -84.430202-0.024462j
[2025-08-19 01:05:39] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -84.381181-0.001818j
[2025-08-19 01:05:49] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -84.352554-0.000561j
[2025-08-19 01:05:58] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -84.419458+0.005927j
[2025-08-19 01:06:08] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -84.292224-0.006479j
[2025-08-19 01:06:17] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -84.289895+0.004435j
[2025-08-19 01:06:27] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -84.320748+0.008258j
[2025-08-19 01:06:36] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -84.358168+0.007598j
[2025-08-19 01:06:46] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -84.365467+0.014969j
[2025-08-19 01:06:46] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-19 01:06:55] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -84.447271+0.003105j
[2025-08-19 01:07:05] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -84.480687+0.001470j
[2025-08-19 01:07:14] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -84.407235+0.003675j
[2025-08-19 01:07:24] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -84.421031+0.019050j
[2025-08-19 01:07:33] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -84.478023+0.026152j
[2025-08-19 01:07:43] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -84.497145+0.000666j
[2025-08-19 01:07:52] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -84.592443-0.006661j
[2025-08-19 01:08:02] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -84.576765-0.010908j
[2025-08-19 01:08:11] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -84.584468-0.014735j
[2025-08-19 01:08:21] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -84.485874-0.020468j
[2025-08-19 01:08:30] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -84.462561+0.002394j
[2025-08-19 01:08:39] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -84.490453-0.005183j
[2025-08-19 01:08:49] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -84.553610+0.004099j
[2025-08-19 01:08:58] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -84.547689+0.009840j
[2025-08-19 01:09:08] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -84.524634-0.027697j
[2025-08-19 01:09:17] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -84.423218-0.027283j
[2025-08-19 01:09:27] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -84.477715-0.016388j
[2025-08-19 01:09:36] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -84.454832-0.012501j
[2025-08-19 01:09:46] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -84.431703-0.018525j
[2025-08-19 01:09:55] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -84.350927-0.035450j
[2025-08-19 01:10:05] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -84.402155-0.013397j
[2025-08-19 01:10:14] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -84.426249-0.007890j
[2025-08-19 01:10:24] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -84.287872-0.030632j
[2025-08-19 01:10:33] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -84.309270-0.020174j
[2025-08-19 01:10:43] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -84.226594-0.005219j
[2025-08-19 01:10:52] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -84.155652+0.003907j
[2025-08-19 01:11:02] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -84.096433-0.025158j
[2025-08-19 01:11:11] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -84.298780-0.035201j
[2025-08-19 01:11:21] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -84.388331-0.002175j
[2025-08-19 01:11:30] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -84.248145-0.011537j
[2025-08-19 01:11:39] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -84.146842-0.009076j
[2025-08-19 01:11:49] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -84.232796+0.021273j
[2025-08-19 01:11:58] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -84.361785+0.009153j
[2025-08-19 01:12:08] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -84.343383-0.006451j
[2025-08-19 01:12:17] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -84.320507-0.008272j
[2025-08-19 01:12:27] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -84.390596-0.007730j
[2025-08-19 01:12:36] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -84.364225+0.009188j
[2025-08-19 01:12:46] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -84.234627-0.004603j
[2025-08-19 01:12:55] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -84.372387+0.008978j
[2025-08-19 01:13:05] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -84.266525+0.018424j
[2025-08-19 01:13:14] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -84.124181+0.009015j
[2025-08-19 01:13:24] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -84.220563+0.007124j
[2025-08-19 01:13:33] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -84.345976-0.012714j
[2025-08-19 01:13:43] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -84.331002+0.054555j
[2025-08-19 01:13:52] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -84.317779+0.011123j
[2025-08-19 01:14:02] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -84.358290-0.015253j
[2025-08-19 01:14:11] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -84.447658+0.013108j
[2025-08-19 01:14:21] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -84.430148+0.025273j
[2025-08-19 01:14:30] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -84.392975+0.006445j
[2025-08-19 01:14:39] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -84.446337+0.015570j
[2025-08-19 01:14:49] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -84.314789+0.027079j
[2025-08-19 01:14:58] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -84.413321+0.015760j
[2025-08-19 01:15:08] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -84.279534+0.011935j
[2025-08-19 01:15:17] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -84.313732-0.031990j
[2025-08-19 01:15:27] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -84.290491+0.026218j
[2025-08-19 01:15:36] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -84.331764+0.016915j
[2025-08-19 01:15:46] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -84.322556-0.003317j
[2025-08-19 01:15:55] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -84.266533+0.011981j
[2025-08-19 01:16:05] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -84.346967+0.002822j
[2025-08-19 01:16:14] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -84.296703+0.004663j
[2025-08-19 01:16:24] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -84.355898-0.007044j
[2025-08-19 01:16:33] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -84.333500+0.009198j
[2025-08-19 01:16:43] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -84.286169-0.019101j
[2025-08-19 01:16:52] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -84.083655+0.039474j
[2025-08-19 01:17:02] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -84.154299-0.006863j
[2025-08-19 01:17:11] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -84.139112-0.054567j
[2025-08-19 01:17:21] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -84.273140-0.023999j
[2025-08-19 01:17:30] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -84.325935+0.009588j
[2025-08-19 01:17:39] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -84.238577-0.004900j
[2025-08-19 01:17:49] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -84.154518+0.046943j
[2025-08-19 01:17:58] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -84.252214+0.008543j
[2025-08-19 01:18:08] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -84.136164-0.037037j
[2025-08-19 01:18:17] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -84.324013+0.016112j
[2025-08-19 01:18:27] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -84.357366-0.021527j
[2025-08-19 01:18:36] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -84.357029-0.010699j
[2025-08-19 01:18:46] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -84.340461+0.015340j
[2025-08-19 01:18:55] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -84.287811-0.020346j
[2025-08-19 01:19:05] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -84.365617+0.007293j
[2025-08-19 01:19:14] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -84.451156+0.010564j
[2025-08-19 01:19:24] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -84.395367+0.003336j
[2025-08-19 01:19:33] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -84.365297-0.005794j
[2025-08-19 01:19:43] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -84.306624+0.021619j
[2025-08-19 01:19:52] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -84.329440-0.026810j
[2025-08-19 01:20:02] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -84.211156+0.021164j
[2025-08-19 01:20:11] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -84.283672-0.002183j
[2025-08-19 01:20:21] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -84.286804+0.002181j
[2025-08-19 01:20:30] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -84.318002-0.003493j
[2025-08-19 01:20:39] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -84.389271+0.008950j
[2025-08-19 01:20:49] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -84.325537+0.003845j
[2025-08-19 01:20:58] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -84.216337+0.006763j
[2025-08-19 01:21:08] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -84.155531+0.017033j
[2025-08-19 01:21:17] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -84.050091+0.003028j
[2025-08-19 01:21:27] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -84.218235+0.005162j
[2025-08-19 01:21:36] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -84.284041+0.008402j
[2025-08-19 01:21:46] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -84.354667-0.027893j
[2025-08-19 01:21:55] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -84.169280+0.027366j
[2025-08-19 01:22:05] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -84.221562-0.008499j
[2025-08-19 01:22:14] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -84.189502+0.048439j
[2025-08-19 01:22:24] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -84.274245-0.001278j
[2025-08-19 01:22:33] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -84.346826-0.004279j
[2025-08-19 01:22:33] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-19 01:22:43] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -84.349404+0.016399j
[2025-08-19 01:22:52] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -84.574103-0.017901j
[2025-08-19 01:23:02] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -84.501260-0.000974j
[2025-08-19 01:23:11] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -84.433504-0.003113j
[2025-08-19 01:23:21] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -84.504886+0.000339j
[2025-08-19 01:23:30] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -84.366066+0.013905j
[2025-08-19 01:23:40] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -84.388700-0.001248j
[2025-08-19 01:23:49] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -84.290057+0.007358j
[2025-08-19 01:23:58] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -84.416446+0.004548j
[2025-08-19 01:24:08] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -84.458057-0.001789j
[2025-08-19 01:24:17] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -84.445597-0.007084j
[2025-08-19 01:24:27] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -84.391919-0.007128j
[2025-08-19 01:24:36] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -84.424528-0.013143j
[2025-08-19 01:24:46] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -84.438515-0.002744j
[2025-08-19 01:24:55] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -84.298225-0.013950j
[2025-08-19 01:25:05] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -84.274002+0.018785j
[2025-08-19 01:25:14] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -84.283427+0.001409j
[2025-08-19 01:25:24] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -84.248007+0.000944j
[2025-08-19 01:25:33] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -84.162359+0.000372j
[2025-08-19 01:25:43] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -84.209157+0.013030j
[2025-08-19 01:25:52] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -84.152254+0.016328j
[2025-08-19 01:26:02] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -84.169087+0.000015j
[2025-08-19 01:26:11] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -84.260823+0.007749j
[2025-08-19 01:26:21] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -84.358549-0.014393j
[2025-08-19 01:26:30] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -84.222688+0.005649j
[2025-08-19 01:26:39] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -84.339953+0.023947j
[2025-08-19 01:26:49] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -84.294115-0.004138j
[2025-08-19 01:26:58] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -84.357336-0.005936j
[2025-08-19 01:27:08] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -84.138631+0.019358j
[2025-08-19 01:27:17] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -84.156443-0.009761j
[2025-08-19 01:27:27] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -84.277733-0.002284j
[2025-08-19 01:27:36] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -84.371006-0.012987j
[2025-08-19 01:27:46] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -84.337752-0.005776j
[2025-08-19 01:27:55] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -84.381625+0.018444j
[2025-08-19 01:28:05] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -84.495292+0.002866j
[2025-08-19 01:28:14] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -84.443007+0.007326j
[2025-08-19 01:28:24] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -84.445382+0.000017j
[2025-08-19 01:28:33] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -84.376746-0.010174j
[2025-08-19 01:28:43] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -84.277983-0.012765j
[2025-08-19 01:28:52] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -84.334426-0.008223j
[2025-08-19 01:29:02] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -84.361526+0.006183j
[2025-08-19 01:29:11] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -84.364951+0.004760j
[2025-08-19 01:29:20] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -84.450070-0.001559j
[2025-08-19 01:29:30] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -84.334956+0.006584j
[2025-08-19 01:29:39] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -84.314996+0.004165j
[2025-08-19 01:29:49] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -84.236096-0.011102j
[2025-08-19 01:29:58] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -84.333716-0.000889j
[2025-08-19 01:30:08] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -84.314559+0.000719j
[2025-08-19 01:30:17] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -84.389061+0.022099j
[2025-08-19 01:30:27] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -84.557961+0.011708j
[2025-08-19 01:30:36] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -84.326272+0.008046j
[2025-08-19 01:30:46] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -84.389765+0.004793j
[2025-08-19 01:30:55] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -84.423686+0.004648j
[2025-08-19 01:31:05] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -84.264998-0.010265j
[2025-08-19 01:31:14] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -84.174186+0.000025j
[2025-08-19 01:31:24] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -84.226733-0.007939j
[2025-08-19 01:31:33] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -84.259493-0.020302j
[2025-08-19 01:31:43] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -84.341757-0.010344j
[2025-08-19 01:31:52] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -84.377357-0.008431j
[2025-08-19 01:32:02] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -84.345987+0.008345j
[2025-08-19 01:32:11] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -84.307652+0.011624j
[2025-08-19 01:32:20] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -84.265425+0.017489j
[2025-08-19 01:32:30] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -84.302375-0.009216j
[2025-08-19 01:32:39] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -84.301343+0.010742j
[2025-08-19 01:32:49] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -84.230511+0.004545j
[2025-08-19 01:32:58] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -84.268165+0.009006j
[2025-08-19 01:33:08] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -84.229582-0.030688j
[2025-08-19 01:33:17] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -84.324055-0.025705j
[2025-08-19 01:33:27] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -84.241916+0.001443j
[2025-08-19 01:33:36] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -84.292476-0.002158j
[2025-08-19 01:33:46] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -84.194986+0.000890j
[2025-08-19 01:33:55] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -84.303397+0.002252j
[2025-08-19 01:34:05] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -84.225388-0.013655j
[2025-08-19 01:34:14] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -84.180391+0.012777j
[2025-08-19 01:34:24] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -84.466258+0.004702j
[2025-08-19 01:34:33] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -84.405299-0.001878j
[2025-08-19 01:34:43] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -84.307541-0.004784j
[2025-08-19 01:34:52] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -84.307723-0.001576j
[2025-08-19 01:35:02] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -84.347598+0.006881j
[2025-08-19 01:35:11] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -84.170820+0.000309j
[2025-08-19 01:35:21] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -84.364436+0.008268j
[2025-08-19 01:35:30] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -84.242028-0.010040j
[2025-08-19 01:35:39] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -84.299474+0.011682j
[2025-08-19 01:35:49] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -84.217909-0.002944j
[2025-08-19 01:35:58] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -84.219067-0.000431j
[2025-08-19 01:36:08] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -84.238141+0.010019j
[2025-08-19 01:36:17] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -84.081097-0.011080j
[2025-08-19 01:36:27] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -84.268953+0.002189j
[2025-08-19 01:36:36] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -84.232608-0.000038j
[2025-08-19 01:36:46] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -84.121208-0.014785j
[2025-08-19 01:36:55] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -84.303458-0.010863j
[2025-08-19 01:37:05] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -84.381770-0.008780j
[2025-08-19 01:37:14] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -84.440747+0.009584j
[2025-08-19 01:37:24] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -84.170032-0.015981j
[2025-08-19 01:37:33] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -84.225283-0.016864j
[2025-08-19 01:37:43] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -84.213326+0.010631j
[2025-08-19 01:37:52] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -84.098090+0.003923j
[2025-08-19 01:38:02] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -84.250773+0.008131j
[2025-08-19 01:38:11] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -84.169696+0.011766j
[2025-08-19 01:38:21] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -84.159999+0.008914j
[2025-08-19 01:38:21] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-19 01:38:30] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -84.091380+0.011899j
[2025-08-19 01:38:40] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -84.212544+0.024208j
[2025-08-19 01:38:49] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -84.172483+0.007588j
[2025-08-19 01:38:59] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -84.051018-0.015637j
[2025-08-19 01:39:08] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -84.184384-0.015834j
[2025-08-19 01:39:18] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -84.221032-0.011247j
[2025-08-19 01:39:27] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -84.277515+0.035198j
[2025-08-19 01:39:37] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -84.441902-0.000026j
[2025-08-19 01:39:46] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -84.451167+0.045614j
[2025-08-19 01:39:56] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -84.386351-0.011819j
[2025-08-19 01:40:05] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -84.412900+0.036112j
[2025-08-19 01:40:14] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -84.218131-0.004365j
[2025-08-19 01:40:24] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -84.317350+0.010645j
[2025-08-19 01:40:33] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -84.484372+0.005830j
[2025-08-19 01:40:43] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -84.566305+0.045753j
[2025-08-19 01:40:52] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -84.518588-0.002680j
[2025-08-19 01:41:02] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -84.543590-0.011289j
[2025-08-19 01:41:11] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -84.630451-0.011340j
[2025-08-19 01:41:21] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -84.661458-0.011174j
[2025-08-19 01:41:30] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -84.445175-0.009014j
[2025-08-19 01:41:40] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -84.450787-0.018405j
[2025-08-19 01:41:49] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -84.491237+0.001364j
[2025-08-19 01:41:59] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -84.407221-0.017594j
[2025-08-19 01:42:08] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -84.518597-0.010227j
[2025-08-19 01:42:18] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -84.376979+0.021157j
[2025-08-19 01:42:27] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -84.474006-0.012364j
[2025-08-19 01:42:37] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -84.469907+0.016389j
[2025-08-19 01:42:46] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -84.453614-0.015072j
[2025-08-19 01:42:56] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -84.615910+0.001060j
[2025-08-19 01:43:05] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -84.562209-0.000951j
[2025-08-19 01:43:15] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -84.462311-0.018662j
[2025-08-19 01:43:24] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -84.296085+0.017342j
[2025-08-19 01:43:34] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -84.284269+0.008534j
[2025-08-19 01:43:43] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -84.336765-0.004495j
[2025-08-19 01:43:52] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -84.382986+0.014682j
[2025-08-19 01:44:02] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -84.283082+0.008429j
[2025-08-19 01:44:11] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -84.399221-0.006418j
[2025-08-19 01:44:21] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -84.310354+0.007158j
[2025-08-19 01:44:30] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -84.328834-0.009502j
[2025-08-19 01:44:40] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -84.125567-0.004625j
[2025-08-19 01:44:49] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -84.127288-0.000142j
[2025-08-19 01:44:59] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -84.130600-0.010184j
[2025-08-19 01:45:08] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -84.151569-0.008875j
[2025-08-19 01:45:18] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -84.270732-0.016384j
[2025-08-19 01:45:27] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -84.224949-0.008163j
[2025-08-19 01:45:37] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -84.268427-0.016278j
[2025-08-19 01:45:46] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -84.345402-0.016776j
[2025-08-19 01:45:56] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -84.264764-0.011975j
[2025-08-19 01:46:05] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -84.244875-0.015343j
[2025-08-19 01:46:15] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -84.376766+0.004674j
[2025-08-19 01:46:24] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -84.569159+0.002000j
[2025-08-19 01:46:34] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -84.350055-0.004339j
[2025-08-19 01:46:43] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -84.185488+0.023082j
[2025-08-19 01:46:52] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -84.213192+0.018922j
[2025-08-19 01:47:02] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -84.095060+0.025841j
[2025-08-19 01:47:11] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -84.333385-0.001363j
[2025-08-19 01:47:21] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -84.284531-0.022632j
[2025-08-19 01:47:30] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -84.321126-0.005926j
[2025-08-19 01:47:40] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -84.256136-0.043552j
[2025-08-19 01:47:49] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -84.337063-0.000572j
[2025-08-19 01:47:59] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -84.334074-0.014179j
[2025-08-19 01:48:08] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -84.430017-0.003361j
[2025-08-19 01:48:18] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -84.363201-0.019909j
[2025-08-19 01:48:27] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -84.393011+0.013507j
[2025-08-19 01:48:37] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -84.322023+0.017885j
[2025-08-19 01:48:46] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -84.525511+0.013486j
[2025-08-19 01:48:56] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -84.489084+0.013260j
[2025-08-19 01:49:05] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -84.363885+0.020702j
[2025-08-19 01:49:15] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -84.481301-0.012175j
[2025-08-19 01:49:24] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -84.396270-0.011572j
[2025-08-19 01:49:34] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -84.204753-0.008166j
[2025-08-19 01:49:43] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -84.206665+0.001755j
[2025-08-19 01:49:52] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -84.297939+0.003681j
[2025-08-19 01:50:02] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -84.401955-0.001112j
[2025-08-19 01:50:11] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -84.344400+0.007886j
[2025-08-19 01:50:21] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -84.298087-0.006069j
[2025-08-19 01:50:30] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -84.355021+0.008841j
[2025-08-19 01:50:40] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -84.433452-0.008108j
[2025-08-19 01:50:49] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -84.452827-0.004228j
[2025-08-19 01:50:59] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -84.529966+0.018429j
[2025-08-19 01:51:08] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -84.368772+0.008462j
[2025-08-19 01:51:18] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -84.377686-0.000619j
[2025-08-19 01:51:27] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -84.188605-0.000598j
[2025-08-19 01:51:37] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -84.317262+0.008596j
[2025-08-19 01:51:46] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -84.198098+0.008109j
[2025-08-19 01:51:56] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -84.209241-0.000222j
[2025-08-19 01:52:05] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -84.312885-0.010957j
[2025-08-19 01:52:15] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -84.280995+0.011611j
[2025-08-19 01:52:24] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -84.250682-0.006156j
[2025-08-19 01:52:34] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -84.235491+0.035539j
[2025-08-19 01:52:43] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -84.380498+0.016067j
[2025-08-19 01:52:52] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -84.362087+0.000007j
[2025-08-19 01:53:02] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -84.159562-0.010758j
[2025-08-19 01:53:11] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -84.301844+0.000064j
[2025-08-19 01:53:21] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -84.357318+0.002524j
[2025-08-19 01:53:30] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -84.266255+0.003248j
[2025-08-19 01:53:40] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -84.105574+0.023026j
[2025-08-19 01:53:49] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -84.233967-0.005485j
[2025-08-19 01:53:59] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -84.243568+0.001342j
[2025-08-19 01:54:08] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -84.191967-0.002190j
[2025-08-19 01:54:08] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-19 01:54:18] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -84.223170-0.008369j
[2025-08-19 01:54:27] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -84.278016-0.001932j
[2025-08-19 01:54:37] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -84.242840+0.005782j
[2025-08-19 01:54:46] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -84.278840+0.012570j
[2025-08-19 01:54:56] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -84.266194+0.008549j
[2025-08-19 01:55:05] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -84.411390-0.006330j
[2025-08-19 01:55:15] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -84.359882-0.017766j
[2025-08-19 01:55:24] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -84.418582+0.012508j
[2025-08-19 01:55:34] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -84.365243-0.000100j
[2025-08-19 01:55:43] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -84.305463+0.003352j
[2025-08-19 01:55:52] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -84.428671-0.008564j
[2025-08-19 01:56:02] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -84.439020+0.008310j
[2025-08-19 01:56:11] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -84.368468-0.006230j
[2025-08-19 01:56:21] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -84.367254+0.002577j
[2025-08-19 01:56:30] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -84.224204-0.006945j
[2025-08-19 01:56:40] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -84.170469+0.001883j
[2025-08-19 01:56:49] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -84.279743+0.033639j
[2025-08-19 01:56:59] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -84.327632-0.001304j
[2025-08-19 01:57:08] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -84.177240+0.009904j
[2025-08-19 01:57:18] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -84.187240-0.002179j
[2025-08-19 01:57:27] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -84.318920+0.007842j
[2025-08-19 01:57:37] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -84.270592+0.013434j
[2025-08-19 01:57:46] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -84.237315-0.008926j
[2025-08-19 01:57:56] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -84.254032+0.013336j
[2025-08-19 01:58:05] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -84.196585+0.012080j
[2025-08-19 01:58:15] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -84.147365-0.002901j
[2025-08-19 01:58:24] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -84.283034-0.012251j
[2025-08-19 01:58:33] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -84.175677-0.012052j
[2025-08-19 01:58:43] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -84.276830-0.006465j
[2025-08-19 01:58:52] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -84.350226-0.005140j
[2025-08-19 01:59:02] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -84.308913+0.002853j
[2025-08-19 01:59:11] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -84.453629-0.028444j
[2025-08-19 01:59:21] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -84.436054-0.021892j
[2025-08-19 01:59:30] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -84.390384-0.011416j
[2025-08-19 01:59:40] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -84.339735-0.027117j
[2025-08-19 01:59:49] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -84.425541-0.030761j
[2025-08-19 01:59:59] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -84.443990-0.025757j
[2025-08-19 02:00:08] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -84.390620-0.030045j
[2025-08-19 02:00:18] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -84.361999-0.015759j
[2025-08-19 02:00:27] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -84.341467-0.032550j
[2025-08-19 02:00:37] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -84.270390-0.043794j
[2025-08-19 02:00:46] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -84.332912-0.042460j
[2025-08-19 02:00:56] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -84.373060-0.040336j
[2025-08-19 02:01:05] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -84.550340-0.025001j
[2025-08-19 02:01:14] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -84.623226-0.020070j
[2025-08-19 02:01:24] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -84.481118-0.028714j
[2025-08-19 02:01:33] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -84.381041-0.030827j
[2025-08-19 02:01:43] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -84.444483-0.012324j
[2025-08-19 02:01:52] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -84.330925-0.009764j
[2025-08-19 02:02:02] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -84.378088-0.002506j
[2025-08-19 02:02:11] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -84.347597-0.013165j
[2025-08-19 02:02:21] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -84.228291-0.006956j
[2025-08-19 02:02:30] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -84.331730-0.018421j
[2025-08-19 02:02:40] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -84.279547-0.013934j
[2025-08-19 02:02:49] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -84.222581-0.001994j
[2025-08-19 02:02:59] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -84.394368-0.004801j
[2025-08-19 02:03:08] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -84.455080-0.002308j
[2025-08-19 02:03:18] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -84.316767-0.006275j
[2025-08-19 02:03:27] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -84.289799+0.000595j
[2025-08-19 02:03:36] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -84.326281-0.000110j
[2025-08-19 02:03:46] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -84.293964-0.001263j
[2025-08-19 02:03:56] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -84.194110+0.002725j
[2025-08-19 02:04:05] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -84.171650+0.015121j
[2025-08-19 02:04:14] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -84.288957-0.000388j
[2025-08-19 02:04:24] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -84.182904-0.008804j
[2025-08-19 02:04:33] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -84.220624-0.015224j
[2025-08-19 02:04:43] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -84.405991-0.004274j
[2025-08-19 02:04:52] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -84.457072-0.003980j
[2025-08-19 02:05:02] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -84.333226-0.000400j
[2025-08-19 02:05:11] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -84.298534-0.008307j
[2025-08-19 02:05:21] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -84.221881+0.004990j
[2025-08-19 02:05:30] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -84.191987-0.015577j
[2025-08-19 02:05:40] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -84.252497-0.004209j
[2025-08-19 02:05:49] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -84.377643+0.014538j
[2025-08-19 02:05:59] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -84.419658-0.007290j
[2025-08-19 02:06:08] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -84.370042+0.021487j
[2025-08-19 02:06:18] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -84.564027-0.006219j
[2025-08-19 02:06:27] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -84.449140+0.011904j
[2025-08-19 02:06:37] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -84.420637-0.017840j
[2025-08-19 02:06:46] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -84.492445-0.001564j
[2025-08-19 02:06:56] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -84.446706+0.006528j
[2025-08-19 02:07:05] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -84.489069+0.014325j
[2025-08-19 02:07:14] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -84.568020+0.020050j
[2025-08-19 02:07:24] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -84.509391+0.004779j
[2025-08-19 02:07:33] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -84.520686+0.007744j
[2025-08-19 02:07:43] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -84.591809-0.013222j
[2025-08-19 02:07:52] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -84.548013+0.015804j
[2025-08-19 02:08:02] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -84.366491+0.007061j
[2025-08-19 02:08:11] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -84.240439-0.027225j
[2025-08-19 02:08:21] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -84.246792+0.017240j
[2025-08-19 02:08:30] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -84.337631-0.019570j
[2025-08-19 02:08:40] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -84.317610-0.030334j
[2025-08-19 02:08:49] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -84.403872-0.016820j
[2025-08-19 02:08:59] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -84.391661+0.063671j
[2025-08-19 02:09:08] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -84.410185+0.012410j
[2025-08-19 02:09:18] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -84.320629+0.030957j
[2025-08-19 02:09:27] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -84.358236+0.006413j
[2025-08-19 02:09:37] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -84.330689-0.026322j
[2025-08-19 02:09:46] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -84.452635-0.009207j
[2025-08-19 02:09:56] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -84.479440-0.003802j
[2025-08-19 02:09:56] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-19 02:10:05] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -84.544419-0.012532j
[2025-08-19 02:10:14] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -84.435444+0.014460j
[2025-08-19 02:10:24] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -84.446143+0.021359j
[2025-08-19 02:10:33] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -84.356881-0.027721j
[2025-08-19 02:10:43] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -84.188777+0.012776j
[2025-08-19 02:10:52] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -84.329303+0.001893j
[2025-08-19 02:11:02] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -84.339406-0.009554j
[2025-08-19 02:11:11] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -84.345215-0.020958j
[2025-08-19 02:11:21] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -84.520493-0.007754j
[2025-08-19 02:11:30] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -84.322982+0.001282j
[2025-08-19 02:11:40] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -84.308564-0.008337j
[2025-08-19 02:11:49] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -84.491325-0.007751j
[2025-08-19 02:11:59] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -84.475901-0.025291j
[2025-08-19 02:12:08] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -84.410518-0.003250j
[2025-08-19 02:12:18] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -84.379124-0.014060j
[2025-08-19 02:12:27] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -84.454683-0.000847j
[2025-08-19 02:12:37] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -84.296807-0.006649j
[2025-08-19 02:12:46] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -84.210461+0.001475j
[2025-08-19 02:12:56] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -84.121517+0.023788j
[2025-08-19 02:13:05] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -84.361049-0.004335j
[2025-08-19 02:13:14] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -84.332266+0.002762j
[2025-08-19 02:13:24] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -84.416419+0.004092j
[2025-08-19 02:13:33] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -84.385327+0.008331j
[2025-08-19 02:13:43] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -84.366603-0.000899j
[2025-08-19 02:13:52] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -84.213553+0.009177j
[2025-08-19 02:14:02] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -84.193160+0.011608j
[2025-08-19 02:14:11] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -84.253386-0.006497j
[2025-08-19 02:14:21] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -84.312988+0.022311j
[2025-08-19 02:14:30] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -84.326808+0.012467j
[2025-08-19 02:14:40] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -84.361453-0.005196j
[2025-08-19 02:14:49] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -84.318088+0.004557j
[2025-08-19 02:14:59] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -84.237607+0.019263j
[2025-08-19 02:15:08] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -84.272389+0.019481j
[2025-08-19 02:15:18] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -84.394535+0.015907j
[2025-08-19 02:15:27] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -84.276443-0.004014j
[2025-08-19 02:15:37] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -84.316887+0.016734j
[2025-08-19 02:15:46] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -84.396524+0.016500j
[2025-08-19 02:15:56] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -84.312483+0.019484j
[2025-08-19 02:16:05] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -84.263568+0.027682j
[2025-08-19 02:16:14] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -84.397480+0.022455j
[2025-08-19 02:16:24] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -84.272314+0.000631j
[2025-08-19 02:16:33] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -84.369038-0.019455j
[2025-08-19 02:16:43] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -84.434150+0.000182j
[2025-08-19 02:16:52] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -84.351111-0.005838j
[2025-08-19 02:17:02] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -84.230970-0.024856j
[2025-08-19 02:17:11] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -84.254897-0.007010j
[2025-08-19 02:17:21] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -84.227649-0.003435j
[2025-08-19 02:17:30] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -84.258091+0.013374j
[2025-08-19 02:17:40] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -84.301671-0.028882j
[2025-08-19 02:17:49] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -84.197037+0.010719j
[2025-08-19 02:17:59] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -84.198953-0.014081j
[2025-08-19 02:18:08] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -84.235622-0.017763j
[2025-08-19 02:18:18] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -84.211870+0.015628j
[2025-08-19 02:18:27] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -84.238723-0.005316j
[2025-08-19 02:18:37] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -84.296462-0.039239j
[2025-08-19 02:18:46] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -84.250418-0.002649j
[2025-08-19 02:18:56] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -84.213317-0.009042j
[2025-08-19 02:19:05] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -84.185703-0.011787j
[2025-08-19 02:19:14] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -84.216628-0.009453j
[2025-08-19 02:19:24] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -84.142264+0.014261j
[2025-08-19 02:19:33] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -84.237316+0.007096j
[2025-08-19 02:19:43] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -84.310882-0.000841j
[2025-08-19 02:19:52] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -84.401887+0.000665j
[2025-08-19 02:20:02] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -84.359106-0.015564j
[2025-08-19 02:20:11] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -84.506172-0.028339j
[2025-08-19 02:20:21] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -84.332830-0.016488j
[2025-08-19 02:20:30] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -84.406463-0.001119j
[2025-08-19 02:20:40] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -84.408448+0.002920j
[2025-08-19 02:20:49] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -84.243383-0.008442j
[2025-08-19 02:20:59] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -84.230666-0.004297j
[2025-08-19 02:21:08] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -84.111247+0.020313j
[2025-08-19 02:21:18] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -84.201691+0.016267j
[2025-08-19 02:21:27] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -84.299871-0.017073j
[2025-08-19 02:21:37] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -84.343939+0.001603j
[2025-08-19 02:21:46] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -84.299349+0.005340j
[2025-08-19 02:21:56] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -84.229117-0.007384j
[2025-08-19 02:22:05] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -84.253645-0.001568j
[2025-08-19 02:22:14] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -84.219556+0.005114j
[2025-08-19 02:22:24] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -84.232582+0.018554j
[2025-08-19 02:22:33] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -84.243660+0.004513j
[2025-08-19 02:22:43] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -84.233468-0.000054j
[2025-08-19 02:22:52] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -84.362650+0.015236j
[2025-08-19 02:23:02] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -84.383651+0.006965j
[2025-08-19 02:23:11] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -84.427986+0.005386j
[2025-08-19 02:23:21] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -84.346346+0.010803j
[2025-08-19 02:23:30] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -84.541253+0.010182j
[2025-08-19 02:23:40] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -84.419699+0.004085j
[2025-08-19 02:23:49] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -84.425571-0.010174j
[2025-08-19 02:23:59] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -84.286578+0.002458j
[2025-08-19 02:24:08] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -84.292983+0.005894j
[2025-08-19 02:24:18] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -84.241733-0.003281j
[2025-08-19 02:24:27] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -84.276404+0.004006j
[2025-08-19 02:24:37] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -84.238984-0.013047j
[2025-08-19 02:24:46] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -84.401155+0.003391j
[2025-08-19 02:24:56] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -84.240043+0.003497j
[2025-08-19 02:25:05] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -84.355652+0.015119j
[2025-08-19 02:25:14] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -84.414059+0.015308j
[2025-08-19 02:25:24] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -84.399620-0.007462j
[2025-08-19 02:25:33] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -84.346768+0.015776j
[2025-08-19 02:25:43] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -84.432632+0.017100j
[2025-08-19 02:25:43] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-19 02:25:52] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -84.522101-0.011780j
[2025-08-19 02:26:02] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -84.456997-0.012380j
[2025-08-19 02:26:11] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -84.360769+0.002968j
[2025-08-19 02:26:21] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -84.248231-0.008846j
[2025-08-19 02:26:30] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -84.345384-0.036721j
[2025-08-19 02:26:40] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -84.328105-0.021568j
[2025-08-19 02:26:49] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -84.437696+0.028602j
[2025-08-19 02:26:59] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -84.483141-0.003413j
[2025-08-19 02:27:08] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -84.395507+0.004486j
[2025-08-19 02:27:18] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -84.506265+0.005397j
[2025-08-19 02:27:27] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -84.357012-0.019173j
[2025-08-19 02:27:36] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -84.433952-0.016074j
[2025-08-19 02:27:46] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -84.510265-0.003986j
[2025-08-19 02:27:56] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -84.470718+0.004443j
[2025-08-19 02:28:05] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -84.494539+0.005903j
[2025-08-19 02:28:14] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -84.441475+0.019354j
[2025-08-19 02:28:24] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -84.419203-0.017738j
[2025-08-19 02:28:33] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -84.478769-0.014444j
[2025-08-19 02:28:43] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -84.364493-0.007307j
[2025-08-19 02:28:52] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -84.416178-0.008057j
[2025-08-19 02:29:02] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -84.595612-0.025187j
[2025-08-19 02:29:11] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -84.587427+0.012084j
[2025-08-19 02:29:21] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -84.530401+0.012446j
[2025-08-19 02:29:30] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -84.504159+0.011673j
[2025-08-19 02:29:40] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -84.454491-0.001993j
[2025-08-19 02:29:49] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -84.409586-0.004581j
[2025-08-19 02:29:59] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -84.528972-0.008093j
[2025-08-19 02:30:08] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -84.466437+0.025916j
[2025-08-19 02:30:18] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -84.560755+0.012347j
[2025-08-19 02:30:27] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -84.598370+0.026099j
[2025-08-19 02:30:37] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -84.473906-0.004910j
[2025-08-19 02:30:46] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -84.593269-0.004093j
[2025-08-19 02:30:56] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -84.508147+0.002221j
[2025-08-19 02:31:05] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -84.520792+0.001740j
[2025-08-19 02:31:14] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -84.589318-0.010881j
[2025-08-19 02:31:24] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -84.582805+0.012047j
[2025-08-19 02:31:33] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -84.365837-0.021700j
[2025-08-19 02:31:43] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -84.434267+0.005055j
[2025-08-19 02:31:52] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -84.415937-0.014925j
[2025-08-19 02:32:02] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -84.469630-0.010746j
[2025-08-19 02:32:11] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -84.303826-0.007798j
[2025-08-19 02:32:21] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -84.321996-0.018007j
[2025-08-19 02:32:30] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -84.500120+0.012217j
[2025-08-19 02:32:40] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -84.337003+0.002742j
[2025-08-19 02:32:49] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -84.301150-0.007552j
[2025-08-19 02:32:59] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -84.313056-0.009052j
[2025-08-19 02:33:08] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -84.278958-0.018736j
[2025-08-19 02:33:18] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -84.295553-0.007970j
[2025-08-19 02:33:27] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -84.385378-0.003153j
[2025-08-19 02:33:37] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -84.310441-0.011101j
[2025-08-19 02:33:37] ✅ Training completed | Restarts: 2
[2025-08-19 02:33:37] ============================================================
[2025-08-19 02:33:37] Training completed | Runtime: 9985.1s
[2025-08-19 02:33:51] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-19 02:33:51] ============================================================
