[2025-08-19 10:20:27] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-08-19 10:20:27]   - 迭代次数: final
[2025-08-19 10:20:27]   - 能量: -84.282693-0.000905j ± 0.110537
[2025-08-19 10:20:27]   - 时间戳: 2025-08-19T02:33:51.146882+08:00
[2025-08-19 10:20:35] ✓ 变分状态参数已从checkpoint恢复
[2025-08-19 10:20:35] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-19 10:20:35] ==================================================
[2025-08-19 10:20:35] GCNN for Shastry-Sutherland Model
[2025-08-19 10:20:35] ==================================================
[2025-08-19 10:20:35] System parameters:
[2025-08-19 10:20:35]   - System size: L=5, N=100
[2025-08-19 10:20:35]   - System parameters: J1=0.02, J2=0.0, Q=1.0
[2025-08-19 10:20:35] --------------------------------------------------
[2025-08-19 10:20:35] Model parameters:
[2025-08-19 10:20:35]   - Number of layers = 4
[2025-08-19 10:20:35]   - Number of features = 4
[2025-08-19 10:20:35]   - Total parameters = 19628
[2025-08-19 10:20:35] --------------------------------------------------
[2025-08-19 10:20:35] Training parameters:
[2025-08-19 10:20:35]   - Learning rate: 0.015
[2025-08-19 10:20:35]   - Total iterations: 1050
[2025-08-19 10:20:35]   - Annealing cycles: 3
[2025-08-19 10:20:35]   - Initial period: 150
[2025-08-19 10:20:35]   - Period multiplier: 2.0
[2025-08-19 10:20:35]   - Temperature range: 0.0-1.0
[2025-08-19 10:20:35]   - Samples: 4096
[2025-08-19 10:20:35]   - Discarded samples: 0
[2025-08-19 10:20:35]   - Chunk size: 2048
[2025-08-19 10:20:35]   - Diagonal shift: 0.2
[2025-08-19 10:20:35]   - Gradient clipping: 1.0
[2025-08-19 10:20:35]   - Checkpoint enabled: interval=100
[2025-08-19 10:20:35]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.02/training/checkpoints
[2025-08-19 10:20:35] --------------------------------------------------
[2025-08-19 10:20:35] Device status:
[2025-08-19 10:20:35]   - Devices model: A100
[2025-08-19 10:20:35]   - Number of devices: 1
[2025-08-19 10:20:35]   - Sharding: True
[2025-08-19 10:20:35] ============================================================
[2025-08-19 10:21:11] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -83.314769+0.146507j
[2025-08-19 10:21:32] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -83.503019+0.123246j
[2025-08-19 10:21:42] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -83.467864+0.087580j
[2025-08-19 10:21:51] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -83.594090+0.076922j
[2025-08-19 10:22:00] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -83.547666+0.076395j
[2025-08-19 10:22:10] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -83.746715+0.067046j
[2025-08-19 10:22:19] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -83.567340+0.068573j
[2025-08-19 10:22:28] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -83.637222+0.048805j
[2025-08-19 10:22:38] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -83.621193+0.044365j
[2025-08-19 10:22:47] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -83.881546+0.040838j
[2025-08-19 10:22:56] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -83.700353+0.030924j
[2025-08-19 10:23:06] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -83.696036+0.051818j
[2025-08-19 10:23:15] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -83.769040+0.030638j
[2025-08-19 10:23:25] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -83.736058+0.030555j
[2025-08-19 10:23:34] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -83.715735+0.027822j
[2025-08-19 10:23:43] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -83.711007+0.041558j
[2025-08-19 10:23:53] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -83.602602+0.013390j
[2025-08-19 10:24:02] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -83.634145+0.012334j
[2025-08-19 10:24:11] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -83.647952-0.005505j
[2025-08-19 10:24:21] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -83.628022-0.015143j
[2025-08-19 10:24:30] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -83.757960+0.010898j
[2025-08-19 10:24:39] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -83.881444+0.015859j
[2025-08-19 10:24:49] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -83.825472+0.004871j
[2025-08-19 10:24:58] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -83.734483+0.006985j
[2025-08-19 10:25:07] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -83.731568-0.004646j
[2025-08-19 10:25:17] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -83.617695-0.005451j
[2025-08-19 10:25:26] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -83.589338+0.009051j
[2025-08-19 10:25:35] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -83.687325-0.002332j
[2025-08-19 10:25:45] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -83.779925-0.015678j
[2025-08-19 10:25:54] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -83.694937-0.005628j
[2025-08-19 10:26:03] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -83.709220+0.020606j
[2025-08-19 10:26:13] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -83.799664-0.004557j
[2025-08-19 10:26:22] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -83.641144+0.005779j
[2025-08-19 10:26:31] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -83.658612+0.008945j
[2025-08-19 10:26:41] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -83.647829-0.011009j
[2025-08-19 10:26:50] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -83.652270+0.008012j
[2025-08-19 10:27:00] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -83.732974+0.006134j
[2025-08-19 10:27:09] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -83.731625-0.008419j
[2025-08-19 10:27:18] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -83.701867+0.008014j
[2025-08-19 10:27:28] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -83.698314+0.001017j
[2025-08-19 10:27:37] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -83.595641+0.001059j
[2025-08-19 10:27:46] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -83.596940+0.002812j
[2025-08-19 10:27:56] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -83.660086-0.006665j
[2025-08-19 10:28:05] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -83.618932+0.013771j
[2025-08-19 10:28:14] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -83.555506+0.012880j
[2025-08-19 10:28:24] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -83.721680-0.003638j
[2025-08-19 10:28:33] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -83.788503+0.009499j
[2025-08-19 10:28:42] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -83.726860-0.018408j
[2025-08-19 10:28:52] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -83.816487-0.012215j
[2025-08-19 10:29:01] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -83.869092-0.008555j
[2025-08-19 10:29:11] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -83.855310-0.025500j
[2025-08-19 10:29:20] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -83.906800-0.008965j
[2025-08-19 10:29:29] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -83.914798+0.016038j
[2025-08-19 10:29:39] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -83.778943+0.007493j
[2025-08-19 10:29:48] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -83.723948+0.051019j
[2025-08-19 10:29:58] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -83.717953-0.007163j
[2025-08-19 10:30:07] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -83.750339+0.067647j
[2025-08-19 10:30:16] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -83.897369+0.023925j
[2025-08-19 10:30:26] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -83.951463+0.032779j
[2025-08-19 10:30:35] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -84.011754+0.020210j
[2025-08-19 10:30:44] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -83.843000+0.027367j
[2025-08-19 10:30:54] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -83.889271-0.013543j
[2025-08-19 10:31:03] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -83.960776-0.000879j
[2025-08-19 10:31:12] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -83.820186+0.006234j
[2025-08-19 10:31:22] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -83.855984+0.006974j
[2025-08-19 10:31:31] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -83.798768+0.000234j
[2025-08-19 10:31:40] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -83.886445+0.004744j
[2025-08-19 10:31:50] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -83.852018-0.012611j
[2025-08-19 10:31:59] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -83.815010-0.007220j
[2025-08-19 10:32:08] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -83.805657+0.010914j
[2025-08-19 10:32:18] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -83.738332-0.003135j
[2025-08-19 10:32:27] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -83.834184-0.012444j
[2025-08-19 10:32:36] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -83.787399+0.004966j
[2025-08-19 10:32:46] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -83.698558+0.015754j
[2025-08-19 10:32:55] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -83.695804-0.009143j
[2025-08-19 10:33:04] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -83.617411-0.029798j
[2025-08-19 10:33:14] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -83.658357+0.003211j
[2025-08-19 10:33:23] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -83.656926-0.054359j
[2025-08-19 10:33:32] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -83.722272-0.011696j
[2025-08-19 10:33:42] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -83.666573+0.026591j
[2025-08-19 10:33:51] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -83.740788-0.014974j
[2025-08-19 10:34:01] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -83.625386+0.045868j
[2025-08-19 10:34:10] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -83.662494-0.006862j
[2025-08-19 10:34:19] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -83.533500+0.025751j
[2025-08-19 10:34:29] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -83.605668-0.012582j
[2025-08-19 10:34:38] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -83.744067+0.011417j
[2025-08-19 10:34:47] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -83.743262-0.009186j
[2025-08-19 10:34:57] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -83.656061-0.020929j
[2025-08-19 10:35:06] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -83.663310-0.003933j
[2025-08-19 10:35:15] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -83.668418+0.008696j
[2025-08-19 10:35:25] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -83.709932-0.006554j
[2025-08-19 10:35:34] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -83.675421-0.011800j
[2025-08-19 10:35:43] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -83.624553+0.005199j
[2025-08-19 10:35:53] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -83.584313-0.010686j
[2025-08-19 10:36:02] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -83.512259-0.000090j
[2025-08-19 10:36:11] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -83.502409+0.002709j
[2025-08-19 10:36:21] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -83.611488-0.022210j
[2025-08-19 10:36:30] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -83.634029+0.023432j
[2025-08-19 10:36:39] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -83.564968-0.013015j
[2025-08-19 10:36:49] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -83.481990-0.015410j
[2025-08-19 10:36:49] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-19 10:36:58] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -83.597771-0.020474j
[2025-08-19 10:37:07] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -83.653485+0.013913j
[2025-08-19 10:37:17] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -83.508705-0.006234j
[2025-08-19 10:37:26] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -83.610268-0.027720j
[2025-08-19 10:37:35] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -83.693357+0.020112j
[2025-08-19 10:37:45] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -83.722562-0.024888j
[2025-08-19 10:37:54] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -83.652773+0.024577j
[2025-08-19 10:38:04] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -83.656166+0.022939j
[2025-08-19 10:38:13] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -83.721845-0.007469j
[2025-08-19 10:38:22] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -83.615322+0.023266j
[2025-08-19 10:38:32] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -83.615975+0.002544j
[2025-08-19 10:38:41] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -83.507554+0.027126j
[2025-08-19 10:38:50] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -83.586428+0.024191j
[2025-08-19 10:39:00] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.563307+0.046869j
[2025-08-19 10:39:09] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -83.512388+0.013755j
[2025-08-19 10:39:18] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -83.573554+0.005542j
[2025-08-19 10:39:28] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -83.645037+0.028822j
[2025-08-19 10:39:37] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -83.881764+0.008568j
[2025-08-19 10:39:46] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -83.825562+0.021292j
[2025-08-19 10:39:56] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -83.808937-0.011473j
[2025-08-19 10:40:05] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -83.690313+0.006874j
[2025-08-19 10:40:14] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -83.563162+0.014623j
[2025-08-19 10:40:24] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -83.547821-0.004805j
[2025-08-19 10:40:33] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -83.632444+0.003346j
[2025-08-19 10:40:42] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -83.725264-0.018530j
[2025-08-19 10:40:52] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -83.514036-0.012170j
[2025-08-19 10:41:01] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -83.571492-0.016758j
[2025-08-19 10:41:11] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -83.529254+0.022288j
[2025-08-19 10:41:20] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -83.801051-0.006355j
[2025-08-19 10:41:29] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -83.654731-0.010617j
[2025-08-19 10:41:39] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -83.608878+0.004742j
[2025-08-19 10:41:48] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -83.660233-0.036962j
[2025-08-19 10:41:58] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -83.646739-0.004330j
[2025-08-19 10:42:07] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -83.600729-0.009037j
[2025-08-19 10:42:16] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -83.580461-0.005893j
[2025-08-19 10:42:26] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -83.620762-0.045219j
[2025-08-19 10:42:35] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -83.570249-0.006324j
[2025-08-19 10:42:44] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -83.608993-0.013338j
[2025-08-19 10:42:54] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -83.457971-0.007986j
[2025-08-19 10:43:03] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -83.529243-0.049888j
[2025-08-19 10:43:12] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -83.552400-0.011069j
[2025-08-19 10:43:22] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -83.477103-0.000014j
[2025-08-19 10:43:31] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -83.616663+0.011780j
[2025-08-19 10:43:40] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -83.595310-0.015621j
[2025-08-19 10:43:50] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -83.540182-0.016595j
[2025-08-19 10:43:59] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -83.611160-0.023743j
[2025-08-19 10:44:08] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -83.730496-0.000853j
[2025-08-19 10:44:18] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -83.867631-0.008274j
[2025-08-19 10:44:27] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -83.788374+0.026686j
[2025-08-19 10:44:36] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -83.861434-0.000332j
[2025-08-19 10:44:36] RESTART #1 | Period: 300
[2025-08-19 10:44:46] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -83.777561-0.001940j
[2025-08-19 10:44:55] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -83.809379+0.006507j
[2025-08-19 10:45:04] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -83.716300-0.016448j
[2025-08-19 10:45:14] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -83.872037-0.000069j
[2025-08-19 10:45:23] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -83.856096+0.000966j
[2025-08-19 10:45:32] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -83.874619-0.012091j
[2025-08-19 10:45:42] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -84.030954-0.006137j
[2025-08-19 10:45:51] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -83.934965-0.009578j
[2025-08-19 10:46:01] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -83.875046-0.000852j
[2025-08-19 10:46:10] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -83.885933-0.019437j
[2025-08-19 10:46:19] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -83.876879+0.002931j
[2025-08-19 10:46:29] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -83.880189-0.020282j
[2025-08-19 10:46:38] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -83.801457-0.024916j
[2025-08-19 10:46:47] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -83.773617-0.024193j
[2025-08-19 10:46:57] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -83.792961-0.034454j
[2025-08-19 10:47:06] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -83.727378-0.022522j
[2025-08-19 10:47:15] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -83.752267-0.036748j
[2025-08-19 10:47:25] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -83.774396-0.020032j
[2025-08-19 10:47:34] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -83.636859+0.008697j
[2025-08-19 10:47:43] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -83.733378+0.030133j
[2025-08-19 10:47:53] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -83.634411-0.019870j
[2025-08-19 10:48:02] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -83.473637+0.003167j
[2025-08-19 10:48:11] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -83.658860-0.002720j
[2025-08-19 10:48:21] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -83.433991-0.010364j
[2025-08-19 10:48:30] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -83.563705+0.004058j
[2025-08-19 10:48:39] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -83.477974-0.062810j
[2025-08-19 10:48:49] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -83.487341+0.000084j
[2025-08-19 10:48:58] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -83.635023+0.004716j
[2025-08-19 10:49:07] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -83.610527+0.010327j
[2025-08-19 10:49:17] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.759109-0.026748j
[2025-08-19 10:49:26] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -83.708760+0.015570j
[2025-08-19 10:49:35] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -83.720188+0.023450j
[2025-08-19 10:49:45] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -83.661983+0.041497j
[2025-08-19 10:49:54] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -83.637132+0.008899j
[2025-08-19 10:50:03] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -83.575813-0.018018j
[2025-08-19 10:50:13] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -83.699484+0.003543j
[2025-08-19 10:50:22] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -83.681730+0.008793j
[2025-08-19 10:50:32] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -83.715065+0.016733j
[2025-08-19 10:50:41] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -83.655927+0.015389j
[2025-08-19 10:50:50] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -83.695745+0.028516j
[2025-08-19 10:51:00] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -83.651378+0.014296j
[2025-08-19 10:51:09] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -83.541633+0.008358j
[2025-08-19 10:51:18] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -83.583999+0.022559j
[2025-08-19 10:51:28] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -83.768687+0.023737j
[2025-08-19 10:51:37] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -83.838414+0.006422j
[2025-08-19 10:51:46] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -83.656289+0.024972j
[2025-08-19 10:51:56] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -83.726340+0.008022j
[2025-08-19 10:52:05] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -83.709991+0.002487j
[2025-08-19 10:52:14] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -83.693429-0.002156j
[2025-08-19 10:52:24] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -83.637654-0.004219j
[2025-08-19 10:52:24] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-19 10:52:33] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -83.571484-0.007009j
[2025-08-19 10:52:42] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -83.450145+0.009972j
[2025-08-19 10:52:52] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -83.581635+0.019543j
[2025-08-19 10:53:01] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -83.604836+0.016409j
[2025-08-19 10:53:11] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -83.456897+0.006024j
[2025-08-19 10:53:20] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -83.612062+0.019305j
[2025-08-19 10:53:29] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -83.553022+0.009630j
[2025-08-19 10:53:39] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -83.539904-0.010202j
[2025-08-19 10:53:48] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -83.540122+0.007963j
[2025-08-19 10:53:58] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -83.594363-0.014132j
[2025-08-19 10:54:07] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -83.560044+0.048766j
[2025-08-19 10:54:16] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -83.629685+0.017016j
[2025-08-19 10:54:26] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -83.473753+0.028970j
[2025-08-19 10:54:35] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -83.634570+0.025145j
[2025-08-19 10:54:44] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -83.681245+0.016786j
[2025-08-19 10:54:54] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -83.587849+0.052510j
[2025-08-19 10:55:03] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -83.652972+0.007074j
[2025-08-19 10:55:12] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -83.715641-0.010547j
[2025-08-19 10:55:22] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -83.641876-0.032805j
[2025-08-19 10:55:31] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -83.632032-0.014875j
[2025-08-19 10:55:40] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -83.652640-0.056322j
[2025-08-19 10:55:50] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -83.603741+0.003775j
[2025-08-19 10:55:59] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -83.692859-0.024050j
[2025-08-19 10:56:08] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -83.783459+0.016249j
[2025-08-19 10:56:18] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -83.600709+0.037477j
[2025-08-19 10:56:27] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -83.775943-0.009501j
[2025-08-19 10:56:36] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -83.674756+0.011106j
[2025-08-19 10:56:46] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -83.614079+0.007925j
[2025-08-19 10:56:55] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -83.620753-0.004357j
[2025-08-19 10:57:04] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -83.705875-0.006103j
[2025-08-19 10:57:14] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -83.628286+0.009714j
[2025-08-19 10:57:23] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -83.668237-0.003778j
[2025-08-19 10:57:32] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -83.796526-0.001792j
[2025-08-19 10:57:42] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -83.741342-0.009357j
[2025-08-19 10:57:51] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -83.807930+0.007499j
[2025-08-19 10:58:01] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -83.778142+0.004275j
[2025-08-19 10:58:10] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -83.860780-0.052603j
[2025-08-19 10:58:19] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -83.780535-0.002536j
[2025-08-19 10:58:29] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -83.761808-0.007382j
[2025-08-19 10:58:38] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -83.733644-0.003363j
[2025-08-19 10:58:47] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -83.717231+0.025381j
[2025-08-19 10:58:57] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -83.671401-0.006152j
[2025-08-19 10:59:06] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -83.705238+0.011743j
[2025-08-19 10:59:15] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -83.825285-0.007258j
[2025-08-19 10:59:25] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -83.766531+0.022489j
[2025-08-19 10:59:34] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -83.711638-0.001984j
[2025-08-19 10:59:43] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -83.750618+0.003832j
[2025-08-19 10:59:53] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -83.811214+0.011925j
[2025-08-19 11:00:02] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -83.755481-0.047603j
[2025-08-19 11:00:11] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -83.713024+0.001936j
[2025-08-19 11:00:21] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -83.746459-0.000181j
[2025-08-19 11:00:30] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -83.720664-0.017655j
[2025-08-19 11:00:39] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -83.882173-0.020100j
[2025-08-19 11:00:49] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -83.703287-0.001683j
[2025-08-19 11:00:58] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -83.594276+0.033744j
[2025-08-19 11:01:07] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -83.600337+0.009771j
[2025-08-19 11:01:17] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -83.583733+0.022536j
[2025-08-19 11:01:26] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -83.540274+0.023340j
[2025-08-19 11:01:35] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -83.744189-0.017444j
[2025-08-19 11:01:45] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -83.606182-0.011684j
[2025-08-19 11:01:54] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -83.716506+0.007255j
[2025-08-19 11:02:04] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -83.777822-0.024615j
[2025-08-19 11:02:13] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -83.764353-0.059947j
[2025-08-19 11:02:22] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -83.636693-0.019345j
[2025-08-19 11:02:32] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -83.684783-0.028024j
[2025-08-19 11:02:41] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -83.795852-0.000416j
[2025-08-19 11:02:50] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -83.719634+0.022804j
[2025-08-19 11:03:00] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -83.713284+0.003062j
[2025-08-19 11:03:09] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -83.578501-0.020188j
[2025-08-19 11:03:18] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -83.656495-0.001123j
[2025-08-19 11:03:28] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -83.662634-0.020706j
[2025-08-19 11:03:37] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -83.624673-0.013894j
[2025-08-19 11:03:46] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -83.720637-0.013286j
[2025-08-19 11:03:56] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -83.766027+0.008488j
[2025-08-19 11:04:05] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -83.794957-0.008683j
[2025-08-19 11:04:14] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -83.805378+0.019300j
[2025-08-19 11:04:24] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -83.802063+0.013656j
[2025-08-19 11:04:33] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -83.847924-0.007874j
[2025-08-19 11:04:42] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -83.701174+0.024651j
[2025-08-19 11:04:52] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -83.733168+0.011866j
[2025-08-19 11:05:01] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -83.744214-0.009535j
[2025-08-19 11:05:11] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -83.819054-0.029042j
[2025-08-19 11:05:20] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -83.658029+0.033921j
[2025-08-19 11:05:29] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -83.597180-0.024933j
[2025-08-19 11:05:39] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -83.719686-0.052822j
[2025-08-19 11:05:48] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -83.753601-0.017599j
[2025-08-19 11:05:58] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -83.638030-0.056562j
[2025-08-19 11:06:07] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -83.760050-0.019376j
[2025-08-19 11:06:16] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -83.790755+0.012912j
[2025-08-19 11:06:26] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -83.746853+0.007053j
[2025-08-19 11:06:35] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -83.712630-0.004321j
[2025-08-19 11:06:44] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -83.879577-0.003066j
[2025-08-19 11:06:54] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -83.848735+0.001435j
[2025-08-19 11:07:03] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -83.789118-0.006612j
[2025-08-19 11:07:12] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -83.809745+0.065930j
[2025-08-19 11:07:22] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -83.864146+0.018145j
[2025-08-19 11:07:31] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -83.619082+0.021187j
[2025-08-19 11:07:40] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -83.606928+0.014335j
[2025-08-19 11:07:50] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -83.504602+0.093174j
[2025-08-19 11:07:59] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -83.707539+0.017989j
[2025-08-19 11:07:59] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-19 11:08:08] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -83.821423-0.014430j
[2025-08-19 11:08:18] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -83.701637+0.062991j
[2025-08-19 11:08:27] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -83.884241-0.000842j
[2025-08-19 11:08:36] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -83.812753+0.005916j
[2025-08-19 11:08:46] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -83.923980+0.006498j
[2025-08-19 11:08:55] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -83.919888+0.015582j
[2025-08-19 11:09:04] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -83.889732+0.028480j
[2025-08-19 11:09:14] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -83.927390+0.014941j
[2025-08-19 11:09:23] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -83.736506-0.057902j
[2025-08-19 11:09:32] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -83.637255-0.011357j
[2025-08-19 11:09:42] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -83.521219+0.018227j
[2025-08-19 11:09:51] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -83.637502-0.006571j
[2025-08-19 11:10:00] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -83.638036-0.000151j
[2025-08-19 11:10:10] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -83.615935-0.007823j
[2025-08-19 11:10:19] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -83.620334-0.003902j
[2025-08-19 11:10:29] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -83.536940-0.019880j
[2025-08-19 11:10:38] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -83.484721-0.032740j
[2025-08-19 11:10:47] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -83.421078+0.024467j
[2025-08-19 11:10:57] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.504316+0.038011j
[2025-08-19 11:11:06] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -83.762484-0.034723j
[2025-08-19 11:11:15] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -83.594037+0.033157j
[2025-08-19 11:11:25] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -83.698834+0.010196j
[2025-08-19 11:11:34] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -83.797807+0.043325j
[2025-08-19 11:11:43] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -83.734857+0.010779j
[2025-08-19 11:11:53] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -83.755554-0.003371j
[2025-08-19 11:12:02] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -83.649709+0.004569j
[2025-08-19 11:12:11] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -83.738638-0.010372j
[2025-08-19 11:12:21] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -83.670181-0.002410j
[2025-08-19 11:12:30] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -83.623957-0.007259j
[2025-08-19 11:12:39] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -83.581570+0.007846j
[2025-08-19 11:12:49] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -83.573203-0.015436j
[2025-08-19 11:12:58] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -83.690714-0.000640j
[2025-08-19 11:13:07] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -83.574944+0.003276j
[2025-08-19 11:13:17] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -83.615649+0.002078j
[2025-08-19 11:13:26] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -83.681743+0.016635j
[2025-08-19 11:13:35] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -83.701213-0.001709j
[2025-08-19 11:13:45] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -83.608269+0.014902j
[2025-08-19 11:13:54] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -83.625046-0.031029j
[2025-08-19 11:14:03] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -83.568591-0.034530j
[2025-08-19 11:14:13] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -83.560950+0.001337j
[2025-08-19 11:14:22] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -83.528764-0.002111j
[2025-08-19 11:14:32] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -83.585861-0.012760j
[2025-08-19 11:14:41] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -83.599919+0.010054j
[2025-08-19 11:14:50] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -83.633509-0.028207j
[2025-08-19 11:15:00] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -83.570938-0.021941j
[2025-08-19 11:15:09] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -83.639347-0.001164j
[2025-08-19 11:15:18] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -83.548004+0.009671j
[2025-08-19 11:15:28] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -83.734942+0.031217j
[2025-08-19 11:15:37] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -83.596017+0.001683j
[2025-08-19 11:15:46] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -83.584450-0.033024j
[2025-08-19 11:15:56] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -83.657417-0.002974j
[2025-08-19 11:16:05] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -83.596871+0.008113j
[2025-08-19 11:16:14] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -83.490335-0.010804j
[2025-08-19 11:16:24] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -83.702975+0.006910j
[2025-08-19 11:16:33] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -83.786752+0.002452j
[2025-08-19 11:16:42] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -83.714390+0.016191j
[2025-08-19 11:16:52] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -83.590573-0.003186j
[2025-08-19 11:17:01] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -83.691374-0.012576j
[2025-08-19 11:17:11] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -83.657965+0.005333j
[2025-08-19 11:17:20] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -83.790869-0.007557j
[2025-08-19 11:17:29] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -83.733505+0.006317j
[2025-08-19 11:17:39] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -83.800369-0.009115j
[2025-08-19 11:17:48] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -83.793379-0.007475j
[2025-08-19 11:17:57] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -83.850994+0.000010j
[2025-08-19 11:18:07] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -83.799562-0.003798j
[2025-08-19 11:18:16] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -83.810956+0.001645j
[2025-08-19 11:18:25] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -83.763367+0.004996j
[2025-08-19 11:18:35] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -83.745877+0.001313j
[2025-08-19 11:18:44] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -83.656059-0.007011j
[2025-08-19 11:18:53] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -83.627583-0.007195j
[2025-08-19 11:19:03] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -83.672398-0.005267j
[2025-08-19 11:19:12] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -83.601019-0.016464j
[2025-08-19 11:19:21] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -83.671048+0.018180j
[2025-08-19 11:19:31] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -83.728456-0.010511j
[2025-08-19 11:19:40] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -83.645995+0.014887j
[2025-08-19 11:19:49] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -83.719204-0.016756j
[2025-08-19 11:19:59] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -83.714526+0.016149j
[2025-08-19 11:20:08] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -83.562318-0.001489j
[2025-08-19 11:20:18] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -83.643693-0.022981j
[2025-08-19 11:20:27] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -83.672853+0.011356j
[2025-08-19 11:20:36] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -83.643003-0.002893j
[2025-08-19 11:20:46] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -83.738924+0.001469j
[2025-08-19 11:20:55] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -83.725203-0.009184j
[2025-08-19 11:21:04] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -83.772316+0.000747j
[2025-08-19 11:21:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -83.700941-0.018501j
[2025-08-19 11:21:23] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -83.641103-0.005683j
[2025-08-19 11:21:32] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -83.679966+0.008924j
[2025-08-19 11:21:42] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -83.776857-0.015859j
[2025-08-19 11:21:51] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -83.687889+0.011304j
[2025-08-19 11:22:00] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -83.816981-0.011367j
[2025-08-19 11:22:10] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -83.779276-0.006891j
[2025-08-19 11:22:19] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -83.763421+0.000480j
[2025-08-19 11:22:29] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -83.688627-0.014596j
[2025-08-19 11:22:38] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -83.615663+0.012050j
[2025-08-19 11:22:47] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -83.625568-0.000924j
[2025-08-19 11:22:57] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -83.693786-0.012484j
[2025-08-19 11:23:06] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -83.668568-0.018291j
[2025-08-19 11:23:15] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -83.606876-0.025608j
[2025-08-19 11:23:25] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -83.864353+0.006581j
[2025-08-19 11:23:34] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -83.827426-0.009807j
[2025-08-19 11:23:34] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-19 11:23:43] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -83.670874+0.002137j
[2025-08-19 11:23:53] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -83.653445-0.001797j
[2025-08-19 11:24:02] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -83.687147+0.003082j
[2025-08-19 11:24:11] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -83.719982+0.004100j
[2025-08-19 11:24:21] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -83.642954-0.025170j
[2025-08-19 11:24:30] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -83.762973+0.007832j
[2025-08-19 11:24:39] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -83.794666+0.014402j
[2025-08-19 11:24:49] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -83.830086-0.011520j
[2025-08-19 11:24:58] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -83.798083-0.008870j
[2025-08-19 11:25:07] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -83.806990-0.001552j
[2025-08-19 11:25:17] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -83.646973+0.010483j
[2025-08-19 11:25:26] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -83.834168-0.019386j
[2025-08-19 11:25:35] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -83.706931+0.006289j
[2025-08-19 11:25:45] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -83.758331+0.021321j
[2025-08-19 11:25:54] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -83.715487-0.028921j
[2025-08-19 11:26:03] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -83.746530-0.002308j
[2025-08-19 11:26:13] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -83.744357-0.005979j
[2025-08-19 11:26:22] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -83.885413-0.001191j
[2025-08-19 11:26:31] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -83.887203+0.057864j
[2025-08-19 11:26:41] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -83.901256-0.002452j
[2025-08-19 11:26:50] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -83.834658-0.021346j
[2025-08-19 11:27:00] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -83.774658-0.011098j
[2025-08-19 11:27:09] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -83.834693+0.034155j
[2025-08-19 11:27:18] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -83.834706+0.019198j
[2025-08-19 11:27:28] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -83.898541-0.003797j
[2025-08-19 11:27:37] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -83.724189-0.007900j
[2025-08-19 11:27:46] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -83.614988+0.041298j
[2025-08-19 11:27:56] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -83.608781+0.036619j
[2025-08-19 11:28:05] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -83.593893-0.001703j
[2025-08-19 11:28:14] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -83.582000+0.000186j
[2025-08-19 11:28:24] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -83.646098-0.032924j
[2025-08-19 11:28:33] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -83.666715-0.029205j
[2025-08-19 11:28:42] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -83.615552-0.028491j
[2025-08-19 11:28:52] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -83.754961-0.061432j
[2025-08-19 11:29:01] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -83.693309-0.034862j
[2025-08-19 11:29:10] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -83.872796+0.001156j
[2025-08-19 11:29:20] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -83.875911-0.027706j
[2025-08-19 11:29:29] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -83.904549-0.021756j
[2025-08-19 11:29:38] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -83.973229-0.003057j
[2025-08-19 11:29:48] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -84.059716-0.006411j
[2025-08-19 11:29:57] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -83.926457+0.008228j
[2025-08-19 11:30:06] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -83.950180+0.010852j
[2025-08-19 11:30:16] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -83.854955+0.038585j
[2025-08-19 11:30:25] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -83.873122+0.009995j
[2025-08-19 11:30:34] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -83.791169+0.023015j
[2025-08-19 11:30:44] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -83.838482+0.023022j
[2025-08-19 11:30:53] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -83.849470-0.021955j
[2025-08-19 11:31:03] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -83.823815-0.032293j
[2025-08-19 11:31:12] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -83.694047+0.037100j
[2025-08-19 11:31:21] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -83.838393-0.018157j
[2025-08-19 11:31:21] RESTART #2 | Period: 600
[2025-08-19 11:31:31] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -83.671089+0.021574j
[2025-08-19 11:31:40] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -83.859044-0.010204j
[2025-08-19 11:31:49] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -83.859201+0.024929j
[2025-08-19 11:31:59] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -83.734919-0.004022j
[2025-08-19 11:32:08] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -83.656851+0.009169j
[2025-08-19 11:32:17] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -83.847139-0.016128j
[2025-08-19 11:32:27] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -83.740489+0.002020j
[2025-08-19 11:32:36] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -83.718719-0.011819j
[2025-08-19 11:32:45] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -83.696486+0.008261j
[2025-08-19 11:32:55] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -83.854968-0.008276j
[2025-08-19 11:33:04] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -83.862442-0.011017j
[2025-08-19 11:33:13] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -83.825568-0.015672j
[2025-08-19 11:33:23] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.796426-0.018836j
[2025-08-19 11:33:32] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -83.771545+0.023453j
[2025-08-19 11:33:41] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -83.779510+0.015586j
[2025-08-19 11:33:51] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -83.800325+0.001720j
[2025-08-19 11:34:00] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -83.912418+0.016970j
[2025-08-19 11:34:09] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -83.879710-0.006667j
[2025-08-19 11:34:19] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -83.734020+0.040245j
[2025-08-19 11:34:28] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -83.781385+0.014035j
[2025-08-19 11:34:37] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -83.721464-0.005715j
[2025-08-19 11:34:47] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -83.699875-0.005399j
[2025-08-19 11:34:56] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -83.735851-0.002432j
[2025-08-19 11:35:05] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -83.800748+0.011443j
[2025-08-19 11:35:15] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -83.771856+0.016143j
[2025-08-19 11:35:24] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -83.821797-0.019716j
[2025-08-19 11:35:34] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -83.769244-0.023980j
[2025-08-19 11:35:43] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -83.762789-0.002832j
[2025-08-19 11:35:52] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -83.651091+0.002497j
[2025-08-19 11:36:02] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -83.535443+0.019583j
[2025-08-19 11:36:11] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -83.485603+0.050198j
[2025-08-19 11:36:20] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -83.535763+0.023513j
[2025-08-19 11:36:30] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -83.522237+0.006414j
[2025-08-19 11:36:39] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -83.566452-0.029191j
[2025-08-19 11:36:48] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -83.554766+0.006655j
[2025-08-19 11:36:58] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -83.550188-0.007763j
[2025-08-19 11:37:07] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -83.575010-0.010779j
[2025-08-19 11:37:16] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -83.647369+0.006723j
[2025-08-19 11:37:26] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -83.474628+0.043987j
[2025-08-19 11:37:35] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -83.518416+0.016346j
[2025-08-19 11:37:44] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -83.497424-0.026449j
[2025-08-19 11:37:54] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -83.616387-0.013667j
[2025-08-19 11:38:03] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -83.497584+0.015608j
[2025-08-19 11:38:12] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -83.468616-0.015225j
[2025-08-19 11:38:22] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -83.336156+0.005779j
[2025-08-19 11:38:31] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -83.456291+0.002859j
[2025-08-19 11:38:41] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -83.520084-0.006700j
[2025-08-19 11:38:50] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -83.562636-0.007750j
[2025-08-19 11:38:59] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -83.506425-0.018929j
[2025-08-19 11:39:09] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -83.688506+0.013914j
[2025-08-19 11:39:09] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-19 11:39:18] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -83.725313+0.002008j
[2025-08-19 11:39:28] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -83.752642+0.012515j
[2025-08-19 11:39:37] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -83.599790-0.032469j
[2025-08-19 11:39:46] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -83.635466-0.087693j
[2025-08-19 11:39:56] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -83.685406+0.012014j
[2025-08-19 11:40:05] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -83.598455-0.011148j
[2025-08-19 11:40:14] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -83.528711-0.009843j
[2025-08-19 11:40:24] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -83.588839+0.021929j
[2025-08-19 11:40:33] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -83.581809+0.025607j
[2025-08-19 11:40:42] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -83.721935-0.030156j
[2025-08-19 11:40:52] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -83.694990-0.016437j
[2025-08-19 11:41:01] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -83.790186+0.002171j
[2025-08-19 11:41:10] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -83.809568+0.045298j
[2025-08-19 11:41:20] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -83.764472-0.019554j
[2025-08-19 11:41:29] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -83.621574-0.033334j
[2025-08-19 11:41:38] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -83.784436-0.022521j
[2025-08-19 11:41:48] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -83.727561-0.065395j
[2025-08-19 11:41:57] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -83.678263-0.024039j
[2025-08-19 11:42:06] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -83.586778-0.070026j
[2025-08-19 11:42:16] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -83.720564-0.012133j
[2025-08-19 11:42:25] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -83.719973+0.026578j
[2025-08-19 11:42:34] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -83.864454-0.035016j
[2025-08-19 11:42:44] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -83.741706-0.036803j
[2025-08-19 11:42:53] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -83.618630-0.001519j
[2025-08-19 11:43:03] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -83.622899+0.008632j
[2025-08-19 11:43:12] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -83.674646+0.008972j
[2025-08-19 11:43:21] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -83.644499-0.010213j
[2025-08-19 11:43:31] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -83.744899+0.018692j
[2025-08-19 11:43:40] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -83.667440+0.013164j
[2025-08-19 11:43:49] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -83.744694+0.018933j
[2025-08-19 11:43:59] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -83.714251+0.004773j
[2025-08-19 11:44:08] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -83.649553-0.001575j
[2025-08-19 11:44:17] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -83.788503+0.014863j
[2025-08-19 11:44:27] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -83.677415-0.010180j
[2025-08-19 11:44:36] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -83.729726-0.005356j
[2025-08-19 11:44:45] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -83.617746-0.002975j
[2025-08-19 11:44:55] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -83.480492-0.011178j
[2025-08-19 11:45:04] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -83.619521+0.004149j
[2025-08-19 11:45:13] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -83.580206+0.002699j
[2025-08-19 11:45:23] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -83.648157+0.003206j
[2025-08-19 11:45:32] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -83.671478+0.007096j
[2025-08-19 11:45:41] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -83.791488+0.001001j
[2025-08-19 11:45:51] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -83.776355-0.027194j
[2025-08-19 11:46:00] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -83.740704+0.013790j
[2025-08-19 11:46:09] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -83.674512-0.000607j
[2025-08-19 11:46:19] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -83.822057+0.011886j
[2025-08-19 11:46:28] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -83.634427+0.012212j
[2025-08-19 11:46:37] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -83.759150+0.024000j
[2025-08-19 11:46:47] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -83.802325+0.034314j
[2025-08-19 11:46:56] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -83.682218+0.029009j
[2025-08-19 11:47:05] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -83.763710+0.045865j
[2025-08-19 11:47:15] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -83.726071+0.015344j
[2025-08-19 11:47:24] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -83.687618-0.009002j
[2025-08-19 11:47:34] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -83.815768-0.008134j
[2025-08-19 11:47:43] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -83.737643-0.008192j
[2025-08-19 11:47:52] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -83.783830+0.041952j
[2025-08-19 11:48:02] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -83.707206+0.040171j
[2025-08-19 11:48:11] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -83.672083-0.004979j
[2025-08-19 11:48:20] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -83.614399+0.015923j
[2025-08-19 11:48:30] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -83.741823+0.020913j
[2025-08-19 11:48:39] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -83.830490+0.012310j
[2025-08-19 11:48:48] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -83.759184+0.011516j
[2025-08-19 11:48:58] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -83.671171-0.015649j
[2025-08-19 11:49:07] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -83.602828+0.010760j
[2025-08-19 11:49:16] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -83.640185+0.025332j
[2025-08-19 11:49:26] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -83.765369-0.000553j
[2025-08-19 11:49:35] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -83.634511+0.004898j
[2025-08-19 11:49:44] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -83.717312+0.002486j
[2025-08-19 11:49:54] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -83.719055-0.034135j
[2025-08-19 11:50:03] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -83.651460+0.002776j
[2025-08-19 11:50:12] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -83.561651-0.031946j
[2025-08-19 11:50:22] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -83.482189+0.007648j
[2025-08-19 11:50:31] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -83.530231+0.003552j
[2025-08-19 11:50:41] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -83.661708-0.007526j
[2025-08-19 11:50:50] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -83.662137-0.001648j
[2025-08-19 11:50:59] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -83.576388+0.019750j
[2025-08-19 11:51:09] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -83.631641-0.052847j
[2025-08-19 11:51:18] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -83.699234+0.021870j
[2025-08-19 11:51:28] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -83.669579+0.027183j
[2025-08-19 11:51:37] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -83.642756-0.004611j
[2025-08-19 11:51:46] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -83.648825+0.055295j
[2025-08-19 11:51:56] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -83.618136+0.033424j
[2025-08-19 11:52:05] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -83.707458-0.023654j
[2025-08-19 11:52:14] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -83.730293+0.010531j
[2025-08-19 11:52:24] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -83.663821-0.017261j
[2025-08-19 11:52:33] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -83.738878+0.023586j
[2025-08-19 11:52:42] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -83.781309+0.025079j
[2025-08-19 11:52:52] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -83.846533+0.037601j
[2025-08-19 11:53:01] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -83.771394+0.068974j
[2025-08-19 11:53:10] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -83.839611-0.004245j
[2025-08-19 11:53:20] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -83.665817-0.041456j
[2025-08-19 11:53:29] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -83.791948-0.003188j
[2025-08-19 11:53:38] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -83.702038+0.038886j
[2025-08-19 11:53:48] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -83.624295+0.017890j
[2025-08-19 11:53:57] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -83.556617+0.003083j
[2025-08-19 11:54:06] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -83.563239+0.004471j
[2025-08-19 11:54:16] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -83.535624+0.021279j
[2025-08-19 11:54:25] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -83.696112+0.036449j
[2025-08-19 11:54:34] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -83.581756-0.025875j
[2025-08-19 11:54:44] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -83.581518+0.023094j
[2025-08-19 11:54:44] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-19 11:54:53] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -83.574497+0.108680j
[2025-08-19 11:55:02] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -83.697146-0.007840j
[2025-08-19 11:55:12] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -83.628816+0.026222j
[2025-08-19 11:55:21] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -83.694221-0.013125j
[2025-08-19 11:55:30] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -83.642724-0.051684j
[2025-08-19 11:55:40] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -83.662191-0.044950j
[2025-08-19 11:55:49] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -83.612267+0.008734j
[2025-08-19 11:55:59] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -83.699056+0.010266j
[2025-08-19 11:56:08] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -83.578922-0.010477j
[2025-08-19 11:56:17] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -83.591001-0.009017j
[2025-08-19 11:56:27] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -83.644717-0.010256j
[2025-08-19 11:56:36] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -83.703892-0.007260j
[2025-08-19 11:56:45] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -83.651582+0.055050j
[2025-08-19 11:56:55] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -83.657588+0.026941j
[2025-08-19 11:57:04] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -83.722269+0.037089j
[2025-08-19 11:57:13] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -83.683590-0.007452j
[2025-08-19 11:57:23] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -83.736729-0.020474j
[2025-08-19 11:57:32] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -83.750388+0.017889j
[2025-08-19 11:57:41] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -83.805504+0.030170j
[2025-08-19 11:57:51] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -83.625228+0.004265j
[2025-08-19 11:58:00] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -83.760780-0.044658j
[2025-08-19 11:58:09] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -83.658074-0.009181j
[2025-08-19 11:58:19] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -83.643556+0.023248j
[2025-08-19 11:58:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -83.708014-0.009175j
[2025-08-19 11:58:37] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -83.556036+0.003719j
[2025-08-19 11:58:47] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -83.503517-0.002189j
[2025-08-19 11:58:56] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -83.573215-0.055847j
[2025-08-19 11:59:05] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -83.669329-0.000600j
[2025-08-19 11:59:15] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -83.551620-0.053196j
[2025-08-19 11:59:24] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -83.702968+0.001599j
[2025-08-19 11:59:33] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -83.368672-0.037737j
[2025-08-19 11:59:43] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -83.349577-0.017605j
[2025-08-19 11:59:52] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -83.376713+0.005502j
[2025-08-19 12:00:02] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -83.544915+0.022366j
[2025-08-19 12:00:11] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -83.472524+0.037414j
[2025-08-19 12:00:20] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -83.483212+0.013411j
[2025-08-19 12:00:30] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -83.585199-0.027069j
[2025-08-19 12:00:39] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -83.658480-0.000668j
[2025-08-19 12:00:48] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -83.533856-0.052741j
[2025-08-19 12:00:58] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -83.690598-0.033855j
[2025-08-19 12:01:07] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -83.628542+0.034733j
[2025-08-19 12:01:16] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -83.603494-0.003952j
[2025-08-19 12:01:26] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -83.631282-0.010839j
[2025-08-19 12:01:35] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -83.667108-0.003831j
[2025-08-19 12:01:44] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -83.735083+0.013807j
[2025-08-19 12:01:54] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -83.730177+0.026284j
[2025-08-19 12:02:03] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -83.728764+0.018659j
[2025-08-19 12:02:12] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -83.757448-0.012079j
[2025-08-19 12:02:22] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -83.662898+0.021541j
[2025-08-19 12:02:31] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -83.678035-0.003677j
[2025-08-19 12:02:41] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -83.705626+0.016728j
[2025-08-19 12:02:50] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -83.702852+0.028878j
[2025-08-19 12:02:59] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -83.685749+0.039315j
[2025-08-19 12:03:09] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -83.680178+0.012965j
[2025-08-19 12:03:18] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -83.690566+0.008748j
[2025-08-19 12:03:28] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -83.768960+0.025081j
[2025-08-19 12:03:37] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -83.658489-0.000442j
[2025-08-19 12:03:46] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -83.663497-0.018535j
[2025-08-19 12:03:56] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -83.662838+0.021010j
[2025-08-19 12:04:05] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -83.804915-0.014390j
[2025-08-19 12:04:14] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -83.758138-0.002038j
[2025-08-19 12:04:24] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -83.774120-0.005535j
[2025-08-19 12:04:33] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -83.661700+0.010668j
[2025-08-19 12:04:42] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -83.765561-0.017650j
[2025-08-19 12:04:52] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -83.737988-0.013960j
[2025-08-19 12:05:01] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -83.812483-0.018191j
[2025-08-19 12:05:10] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -83.629661-0.001358j
[2025-08-19 12:05:20] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -83.739557-0.033067j
[2025-08-19 12:05:29] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -83.750151-0.001181j
[2025-08-19 12:05:38] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -83.729209-0.016987j
[2025-08-19 12:05:48] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -83.524643+0.020404j
[2025-08-19 12:05:57] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -83.493346-0.021208j
[2025-08-19 12:06:06] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -83.500187-0.051306j
[2025-08-19 12:06:16] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -83.707069-0.013022j
[2025-08-19 12:06:25] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -83.781455-0.031380j
[2025-08-19 12:06:34] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -83.706386-0.007671j
[2025-08-19 12:06:44] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -83.796722-0.001739j
[2025-08-19 12:06:53] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -83.707940-0.023643j
[2025-08-19 12:07:03] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -83.795595-0.006177j
[2025-08-19 12:07:12] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -83.723570+0.001421j
[2025-08-19 12:07:21] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -83.670210-0.012286j
[2025-08-19 12:07:31] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -83.614498-0.000725j
[2025-08-19 12:07:40] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -83.657282+0.026038j
[2025-08-19 12:07:49] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -83.686738-0.006437j
[2025-08-19 12:07:59] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -83.757129+0.012057j
[2025-08-19 12:08:08] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -83.720719+0.013378j
[2025-08-19 12:08:17] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -83.763437-0.020924j
[2025-08-19 12:08:27] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -83.736762+0.010168j
[2025-08-19 12:08:36] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -83.679835+0.013105j
[2025-08-19 12:08:45] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -83.782022-0.019239j
[2025-08-19 12:08:55] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -83.586916-0.002358j
[2025-08-19 12:09:04] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -83.585126-0.032098j
[2025-08-19 12:09:13] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -83.650008+0.020310j
[2025-08-19 12:09:23] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -83.568273+0.002681j
[2025-08-19 12:09:32] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -83.700676-0.005880j
[2025-08-19 12:09:41] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -83.854454-0.003539j
[2025-08-19 12:09:51] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -83.728778-0.006823j
[2025-08-19 12:10:00] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -83.598385-0.027228j
[2025-08-19 12:10:09] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -83.560447+0.032911j
[2025-08-19 12:10:19] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -83.645773-0.020977j
[2025-08-19 12:10:19] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-19 12:10:28] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -83.679244-0.012544j
[2025-08-19 12:10:37] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -83.709760+0.014548j
[2025-08-19 12:10:47] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -83.657545-0.021255j
[2025-08-19 12:10:56] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -83.668327-0.015905j
[2025-08-19 12:11:06] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -83.645208-0.033059j
[2025-08-19 12:11:15] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -83.606277-0.033871j
[2025-08-19 12:11:24] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -83.718150-0.030714j
[2025-08-19 12:11:34] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -83.729229-0.019207j
[2025-08-19 12:11:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -83.771655-0.002580j
[2025-08-19 12:11:52] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -83.864898+0.008508j
[2025-08-19 12:12:02] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -83.841901-0.029168j
[2025-08-19 12:12:11] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -83.811009+0.008380j
[2025-08-19 12:12:20] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -83.692059-0.027562j
[2025-08-19 12:12:30] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -83.669114+0.000110j
[2025-08-19 12:12:39] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -83.733689-0.030179j
[2025-08-19 12:12:48] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -83.727110-0.015269j
[2025-08-19 12:12:58] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -83.677527-0.042390j
[2025-08-19 12:13:07] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -83.563416+0.008344j
[2025-08-19 12:13:16] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -83.482618-0.042043j
[2025-08-19 12:13:26] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -83.637864-0.028324j
[2025-08-19 12:13:35] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -83.643626-0.030259j
[2025-08-19 12:13:44] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -83.690015+0.003526j
[2025-08-19 12:13:54] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -83.727402-0.026283j
[2025-08-19 12:14:03] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -83.745617-0.022615j
[2025-08-19 12:14:12] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -83.653641-0.050038j
[2025-08-19 12:14:22] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -83.718126+0.024458j
[2025-08-19 12:14:31] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -83.541448-0.021955j
[2025-08-19 12:14:41] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -83.564647-0.014337j
[2025-08-19 12:14:50] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -83.593343-0.011153j
[2025-08-19 12:14:59] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -83.640744+0.000920j
[2025-08-19 12:15:09] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -83.534100-0.037250j
[2025-08-19 12:15:18] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -83.644230-0.023013j
[2025-08-19 12:15:27] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -83.673956-0.007172j
[2025-08-19 12:15:37] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -83.707587+0.013351j
[2025-08-19 12:15:46] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -83.711080-0.007311j
[2025-08-19 12:15:55] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -83.731190-0.013635j
[2025-08-19 12:16:05] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -83.710755+0.003880j
[2025-08-19 12:16:14] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -83.789377+0.007461j
[2025-08-19 12:16:23] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -83.705785+0.038375j
[2025-08-19 12:16:33] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -83.801851-0.005794j
[2025-08-19 12:16:42] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -83.644606+0.003566j
[2025-08-19 12:16:51] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -83.733984-0.004620j
[2025-08-19 12:17:01] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -83.750981-0.006853j
[2025-08-19 12:17:10] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -83.793993-0.008001j
[2025-08-19 12:17:19] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -83.816292-0.028177j
[2025-08-19 12:17:29] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -83.787305-0.009951j
[2025-08-19 12:17:38] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -83.784934-0.009698j
[2025-08-19 12:17:48] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -83.754644-0.004338j
[2025-08-19 12:17:57] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -83.790340-0.000388j
[2025-08-19 12:18:06] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -83.764750+0.000919j
[2025-08-19 12:18:16] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -83.785850-0.007870j
[2025-08-19 12:18:25] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -83.644447-0.009659j
[2025-08-19 12:18:34] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -83.641108+0.008813j
[2025-08-19 12:18:44] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -83.711819-0.003763j
[2025-08-19 12:18:53] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -83.649512-0.008928j
[2025-08-19 12:19:02] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -83.675317+0.009045j
[2025-08-19 12:19:12] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -83.765373+0.009151j
[2025-08-19 12:19:21] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -83.725213+0.002369j
[2025-08-19 12:19:30] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -83.605014+0.013367j
[2025-08-19 12:19:40] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -83.614677+0.001583j
[2025-08-19 12:19:49] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -83.653058+0.032417j
[2025-08-19 12:19:58] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -83.507777+0.022063j
[2025-08-19 12:20:08] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -83.566599-0.001160j
[2025-08-19 12:20:17] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -83.731259+0.006904j
[2025-08-19 12:20:27] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -83.747895+0.013371j
[2025-08-19 12:20:36] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -83.623663+0.024457j
[2025-08-19 12:20:45] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -83.548381+0.027660j
[2025-08-19 12:20:55] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -83.616701+0.006655j
[2025-08-19 12:21:04] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -83.653713+0.006439j
[2025-08-19 12:21:13] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -83.620304+0.029019j
[2025-08-19 12:21:23] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -83.554921+0.037741j
[2025-08-19 12:21:32] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -83.536244+0.004232j
[2025-08-19 12:21:41] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -83.528060+0.013970j
[2025-08-19 12:21:51] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -83.651668+0.004349j
[2025-08-19 12:22:00] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -83.584696-0.004473j
[2025-08-19 12:22:09] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -83.595187+0.014236j
[2025-08-19 12:22:19] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -83.767210+0.017105j
[2025-08-19 12:22:28] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -83.701693+0.010104j
[2025-08-19 12:22:37] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -83.915684+0.006288j
[2025-08-19 12:22:47] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -83.833866+0.019043j
[2025-08-19 12:22:56] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -83.771973+0.025518j
[2025-08-19 12:23:05] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -83.796212+0.008910j
[2025-08-19 12:23:15] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -83.679827-0.001066j
[2025-08-19 12:23:24] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -83.714635-0.011453j
[2025-08-19 12:23:33] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -83.637816+0.005376j
[2025-08-19 12:23:43] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -83.726590+0.008689j
[2025-08-19 12:23:52] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -83.696880+0.021182j
[2025-08-19 12:24:01] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -83.838814-0.020578j
[2025-08-19 12:24:11] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -83.825214+0.006850j
[2025-08-19 12:24:20] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -83.812615-0.012552j
[2025-08-19 12:24:29] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -83.737966-0.003293j
[2025-08-19 12:24:39] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -83.779850-0.007231j
[2025-08-19 12:24:48] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -83.900460+0.019328j
[2025-08-19 12:24:58] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -83.861300+0.015147j
[2025-08-19 12:25:07] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -83.852211+0.007909j
[2025-08-19 12:25:16] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -83.912408-0.015750j
[2025-08-19 12:25:26] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -83.760947+0.025851j
[2025-08-19 12:25:35] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -83.934069-0.002816j
[2025-08-19 12:25:44] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -83.772470-0.005585j
[2025-08-19 12:25:54] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -84.000758+0.009483j
[2025-08-19 12:25:54] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-19 12:26:03] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -83.887636-0.000853j
[2025-08-19 12:26:12] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -83.826034-0.030470j
[2025-08-19 12:26:22] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -83.865196+0.020333j
[2025-08-19 12:26:31] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -83.761264+0.012212j
[2025-08-19 12:26:40] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -83.745784+0.013472j
[2025-08-19 12:26:50] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -83.791066+0.006375j
[2025-08-19 12:26:59] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -83.771220-0.020484j
[2025-08-19 12:27:08] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -83.709799-0.005618j
[2025-08-19 12:27:18] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -83.808604+0.004362j
[2025-08-19 12:27:27] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -83.759668+0.016660j
[2025-08-19 12:27:36] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -83.856925+0.005988j
[2025-08-19 12:27:46] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -83.894142+0.002746j
[2025-08-19 12:27:55] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -83.798519-0.003441j
[2025-08-19 12:28:04] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -83.735998+0.006549j
[2025-08-19 12:28:14] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -83.669672-0.015194j
[2025-08-19 12:28:23] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -83.774663-0.008037j
[2025-08-19 12:28:33] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -83.722104-0.007685j
[2025-08-19 12:28:42] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -83.695884-0.008212j
[2025-08-19 12:28:51] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -83.755843+0.013072j
[2025-08-19 12:29:01] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -83.701953+0.005447j
[2025-08-19 12:29:10] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -83.624502-0.007829j
[2025-08-19 12:29:19] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -83.729201-0.006494j
[2025-08-19 12:29:29] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -83.548521-0.030036j
[2025-08-19 12:29:38] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -83.569817-0.007897j
[2025-08-19 12:29:47] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -83.611155+0.013583j
[2025-08-19 12:29:57] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -83.662507-0.004190j
[2025-08-19 12:30:06] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -83.678124+0.001790j
[2025-08-19 12:30:15] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -83.772728-0.010261j
[2025-08-19 12:30:25] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -83.630930-0.000904j
[2025-08-19 12:30:34] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -83.592720-0.007620j
[2025-08-19 12:30:43] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -83.771424-0.004089j
[2025-08-19 12:30:53] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -83.853542-0.001299j
[2025-08-19 12:31:02] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -83.842850+0.015709j
[2025-08-19 12:31:11] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -83.655988+0.001006j
[2025-08-19 12:31:21] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -83.763875+0.008915j
[2025-08-19 12:31:30] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -83.783990+0.012102j
[2025-08-19 12:31:39] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -83.905889+0.006447j
[2025-08-19 12:31:49] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -83.996110-0.009062j
[2025-08-19 12:31:58] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -83.936868+0.012469j
[2025-08-19 12:32:07] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -83.689621+0.002037j
[2025-08-19 12:32:17] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -83.795179+0.018855j
[2025-08-19 12:32:26] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -83.819092+0.015519j
[2025-08-19 12:32:35] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -83.815526+0.008145j
[2025-08-19 12:32:45] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -83.797704-0.014676j
[2025-08-19 12:32:54] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -83.879370+0.020882j
[2025-08-19 12:33:04] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -83.827756-0.011129j
[2025-08-19 12:33:13] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -83.917355-0.001979j
[2025-08-19 12:33:22] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -83.870451-0.007673j
[2025-08-19 12:33:32] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -83.755715+0.002584j
[2025-08-19 12:33:41] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -83.793074-0.002659j
[2025-08-19 12:33:50] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -83.814617-0.004845j
[2025-08-19 12:34:00] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -83.801242-0.005934j
[2025-08-19 12:34:09] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -83.860053-0.002934j
[2025-08-19 12:34:18] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -83.788079+0.009556j
[2025-08-19 12:34:28] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -83.803743-0.006731j
[2025-08-19 12:34:37] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -83.861068+0.002746j
[2025-08-19 12:34:46] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -83.858009-0.006479j
[2025-08-19 12:34:56] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -83.803770-0.006932j
[2025-08-19 12:35:05] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -83.813239-0.000321j
[2025-08-19 12:35:15] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -83.705557-0.009632j
[2025-08-19 12:35:24] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -83.712452-0.015615j
[2025-08-19 12:35:33] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -83.668675-0.005174j
[2025-08-19 12:35:43] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -83.699402-0.016957j
[2025-08-19 12:35:52] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -83.709590-0.002124j
[2025-08-19 12:36:01] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -83.592715+0.013426j
[2025-08-19 12:36:11] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -83.711777+0.006757j
[2025-08-19 12:36:20] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -83.783776+0.010003j
[2025-08-19 12:36:29] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -83.722798+0.017822j
[2025-08-19 12:36:39] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -83.768232+0.024028j
[2025-08-19 12:36:48] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -83.715088+0.017893j
[2025-08-19 12:36:58] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -83.784894+0.006238j
[2025-08-19 12:37:07] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -83.732946+0.020636j
[2025-08-19 12:37:16] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -83.798834+0.010127j
[2025-08-19 12:37:26] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -83.686713+0.013062j
[2025-08-19 12:37:35] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -83.652002-0.001080j
[2025-08-19 12:37:44] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -83.709981+0.029103j
[2025-08-19 12:37:54] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -83.819331+0.012436j
[2025-08-19 12:38:03] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -83.678597+0.009107j
[2025-08-19 12:38:12] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -83.768223+0.020646j
[2025-08-19 12:38:22] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -83.624380+0.000088j
[2025-08-19 12:38:31] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -83.681055+0.017107j
[2025-08-19 12:38:40] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -83.731885-0.004232j
[2025-08-19 12:38:50] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -83.787402+0.014115j
[2025-08-19 12:38:59] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -83.884678-0.013123j
[2025-08-19 12:39:08] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -83.749538-0.000085j
[2025-08-19 12:39:18] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -83.915532-0.001015j
[2025-08-19 12:39:27] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -83.806136+0.022764j
[2025-08-19 12:39:36] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -83.654209-0.010219j
[2025-08-19 12:39:46] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -83.620979+0.012011j
[2025-08-19 12:39:55] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -83.593439+0.003087j
[2025-08-19 12:40:04] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -83.725925+0.013312j
[2025-08-19 12:40:14] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -83.663071+0.007500j
[2025-08-19 12:40:23] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -83.526330+0.019653j
[2025-08-19 12:40:33] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -83.612858+0.004590j
[2025-08-19 12:40:42] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -83.770119-0.012270j
[2025-08-19 12:40:51] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -83.807543+0.007061j
[2025-08-19 12:41:01] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -83.737206-0.018151j
[2025-08-19 12:41:10] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -83.715360+0.001905j
[2025-08-19 12:41:19] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -83.828405-0.004655j
[2025-08-19 12:41:29] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -83.813387+0.002398j
[2025-08-19 12:41:29] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-19 12:41:38] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -83.831804-0.000209j
[2025-08-19 12:41:47] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -83.819713-0.019875j
[2025-08-19 12:41:57] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -83.764603+0.005274j
[2025-08-19 12:42:06] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -83.838977-0.009510j
[2025-08-19 12:42:15] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -83.718514-0.004949j
[2025-08-19 12:42:25] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -83.691867+0.006276j
[2025-08-19 12:42:34] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -83.684991-0.039918j
[2025-08-19 12:42:43] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -83.700769+0.015299j
[2025-08-19 12:42:53] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -83.839368+0.009691j
[2025-08-19 12:43:02] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -83.866804-0.003778j
[2025-08-19 12:43:11] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -84.050209-0.006344j
[2025-08-19 12:43:21] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -84.082187+0.009821j
[2025-08-19 12:43:30] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -84.002852+0.002309j
[2025-08-19 12:43:39] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -83.829777-0.027606j
[2025-08-19 12:43:49] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -83.858496-0.007180j
[2025-08-19 12:43:58] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -83.704363-0.008986j
[2025-08-19 12:44:07] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -84.023238-0.018028j
[2025-08-19 12:44:17] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -83.944205+0.000176j
[2025-08-19 12:44:26] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -83.924548-0.001672j
[2025-08-19 12:44:35] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -83.885141-0.015554j
[2025-08-19 12:44:45] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -83.950173+0.008870j
[2025-08-19 12:44:54] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -83.972124+0.005341j
[2025-08-19 12:45:04] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -83.783352-0.007780j
[2025-08-19 12:45:13] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -83.837216+0.009293j
[2025-08-19 12:45:22] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -83.808949+0.007275j
[2025-08-19 12:45:32] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -83.920588-0.005175j
[2025-08-19 12:45:41] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -83.771771+0.003889j
[2025-08-19 12:45:50] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -83.906855-0.000860j
[2025-08-19 12:46:00] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -83.880121-0.008529j
[2025-08-19 12:46:09] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -83.928018+0.002991j
[2025-08-19 12:46:18] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -83.878537-0.020902j
[2025-08-19 12:46:28] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -83.731289-0.027051j
[2025-08-19 12:46:37] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -83.694451+0.005691j
[2025-08-19 12:46:46] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -83.677194+0.001874j
[2025-08-19 12:46:56] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -83.672584-0.012381j
[2025-08-19 12:47:05] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -83.687704-0.019577j
[2025-08-19 12:47:14] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -83.723987-0.014343j
[2025-08-19 12:47:24] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -83.849354-0.009526j
[2025-08-19 12:47:33] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -83.900923+0.003934j
[2025-08-19 12:47:43] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -83.925546+0.000364j
[2025-08-19 12:47:52] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -83.700510-0.002028j
[2025-08-19 12:48:01] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -83.862779-0.006582j
[2025-08-19 12:48:11] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -83.845911-0.016044j
[2025-08-19 12:48:20] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -83.824016-0.024304j
[2025-08-19 12:48:29] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -83.902621+0.009096j
[2025-08-19 12:48:39] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -83.881579-0.006604j
[2025-08-19 12:48:48] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -83.685782-0.011221j
[2025-08-19 12:48:58] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -83.760980+0.006254j
[2025-08-19 12:49:07] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -83.752757+0.001887j
[2025-08-19 12:49:16] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -83.752473-0.011524j
[2025-08-19 12:49:26] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -83.842774-0.026802j
[2025-08-19 12:49:35] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -83.841056-0.004283j
[2025-08-19 12:49:44] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -83.828751+0.019160j
[2025-08-19 12:49:54] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -83.869216-0.005089j
[2025-08-19 12:50:03] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -83.811849-0.010790j
[2025-08-19 12:50:12] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -83.797678+0.015659j
[2025-08-19 12:50:22] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -83.725192-0.002146j
[2025-08-19 12:50:31] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -83.786749+0.007863j
[2025-08-19 12:50:40] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -83.857464+0.008742j
[2025-08-19 12:50:50] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -83.751338-0.008794j
[2025-08-19 12:50:59] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -83.776385+0.001263j
[2025-08-19 12:51:08] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -83.987192+0.003420j
[2025-08-19 12:51:18] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -83.754840+0.034256j
[2025-08-19 12:51:27] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -83.825084-0.006211j
[2025-08-19 12:51:36] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -83.746456-0.011430j
[2025-08-19 12:51:46] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -83.751742-0.016858j
[2025-08-19 12:51:55] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -83.764275-0.019925j
[2025-08-19 12:52:04] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -83.827268+0.000728j
[2025-08-19 12:52:14] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -83.629979+0.011736j
[2025-08-19 12:52:23] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -83.744208+0.029120j
[2025-08-19 12:52:33] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -83.662005+0.029733j
[2025-08-19 12:52:42] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -83.804832-0.004522j
[2025-08-19 12:52:51] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -83.618570-0.005325j
[2025-08-19 12:53:01] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -83.713503+0.000198j
[2025-08-19 12:53:10] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -83.692341-0.003831j
[2025-08-19 12:53:19] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -83.644182+0.001704j
[2025-08-19 12:53:29] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -83.691701+0.021869j
[2025-08-19 12:53:38] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -83.677656+0.007170j
[2025-08-19 12:53:47] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -83.768499-0.001874j
[2025-08-19 12:53:57] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -83.752912-0.023194j
[2025-08-19 12:54:06] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -83.791754-0.029502j
[2025-08-19 12:54:15] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -83.910987+0.006488j
[2025-08-19 12:54:25] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -83.830438-0.003673j
[2025-08-19 12:54:34] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -83.831340+0.008777j
[2025-08-19 12:54:43] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -83.709200+0.005961j
[2025-08-19 12:54:53] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -83.662128+0.017672j
[2025-08-19 12:55:02] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -83.759063+0.001645j
[2025-08-19 12:55:11] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -83.857136-0.034681j
[2025-08-19 12:55:21] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -83.635666-0.042773j
[2025-08-19 12:55:30] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -83.880047-0.061047j
[2025-08-19 12:55:40] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -83.712894+0.029841j
[2025-08-19 12:55:49] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -83.831943+0.037726j
[2025-08-19 12:55:58] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -83.946034-0.012895j
[2025-08-19 12:56:08] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -83.922778+0.038781j
[2025-08-19 12:56:17] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -83.802855-0.030698j
[2025-08-19 12:56:26] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -83.771583-0.021373j
[2025-08-19 12:56:36] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -83.704995-0.028154j
[2025-08-19 12:56:45] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.771823+0.019864j
[2025-08-19 12:56:54] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -83.825226-0.007451j
[2025-08-19 12:57:04] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -83.856855-0.018929j
[2025-08-19 12:57:04] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-19 12:57:13] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -83.741052+0.014378j
[2025-08-19 12:57:22] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -83.854638+0.012983j
[2025-08-19 12:57:32] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -83.845188+0.044884j
[2025-08-19 12:57:41] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -83.925888-0.037027j
[2025-08-19 12:57:50] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -83.819764-0.004463j
[2025-08-19 12:58:00] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -83.728032-0.010166j
[2025-08-19 12:58:09] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -83.755481-0.010535j
[2025-08-19 12:58:19] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -83.735106-0.051393j
[2025-08-19 12:58:28] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -83.910116-0.026100j
[2025-08-19 12:58:37] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -83.883021+0.007385j
[2025-08-19 12:58:47] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -83.942803-0.009968j
[2025-08-19 12:58:56] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -83.953447-0.011851j
[2025-08-19 12:59:05] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -83.875820+0.038209j
[2025-08-19 12:59:15] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -83.870103+0.022818j
[2025-08-19 12:59:24] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -83.813256+0.057007j
[2025-08-19 12:59:33] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -83.854648-0.030846j
[2025-08-19 12:59:43] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -83.823601-0.032427j
[2025-08-19 12:59:52] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -83.909754+0.018545j
[2025-08-19 13:00:01] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -83.964690-0.017085j
[2025-08-19 13:00:11] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -83.959204-0.014168j
[2025-08-19 13:00:20] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -83.855724-0.003230j
[2025-08-19 13:00:29] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -83.785092-0.019587j
[2025-08-19 13:00:39] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -83.729829-0.015737j
[2025-08-19 13:00:48] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -83.800230-0.006306j
[2025-08-19 13:00:58] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -83.713369-0.026149j
[2025-08-19 13:01:07] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -83.863708-0.010009j
[2025-08-19 13:01:16] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -83.760301+0.016944j
[2025-08-19 13:01:26] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -83.872244-0.040727j
[2025-08-19 13:01:35] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -83.760042-0.029119j
[2025-08-19 13:01:44] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -83.816786-0.028262j
[2025-08-19 13:01:54] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -83.824805+0.035149j
[2025-08-19 13:02:03] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -83.806568+0.013217j
[2025-08-19 13:02:12] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -83.829499+0.004620j
[2025-08-19 13:02:22] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -83.839590+0.008013j
[2025-08-19 13:02:31] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -83.752729-0.008457j
[2025-08-19 13:02:40] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -83.663959-0.013424j
[2025-08-19 13:02:50] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -83.738716-0.033064j
[2025-08-19 13:02:59] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -83.660179-0.004080j
[2025-08-19 13:03:08] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -83.638836+0.014671j
[2025-08-19 13:03:18] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -83.764223+0.029863j
[2025-08-19 13:03:27] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -83.646833+0.019058j
[2025-08-19 13:03:36] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -83.814176+0.012867j
[2025-08-19 13:03:46] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -83.774783+0.011747j
[2025-08-19 13:03:55] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -83.907295+0.002635j
[2025-08-19 13:04:05] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -83.769764-0.015600j
[2025-08-19 13:04:14] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -83.813955+0.007510j
[2025-08-19 13:04:23] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -83.796448+0.012334j
[2025-08-19 13:04:33] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -83.816097+0.012336j
[2025-08-19 13:04:42] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -83.849565-0.005416j
[2025-08-19 13:04:51] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -83.667441-0.008049j
[2025-08-19 13:04:51] ✅ Training completed | Restarts: 2
[2025-08-19 13:04:51] ============================================================
[2025-08-19 13:04:51] Training completed | Runtime: 9855.9s
[2025-08-19 13:05:05] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-19 13:05:05] ============================================================
