#!/bin/bash

echo "=== PBS权限问题诊断脚本 ==="

# 检查用户组
echo "1. 用户组信息:"
groups $USER

# 检查PBS配置
echo ""
echo "2. PBS服务器信息:"
qstat -B 2>/dev/null || echo "无法连接PBS服务器"

# 检查队列权限
echo ""
echo "3. 测试不同队列权限:"

# 测试标准队列
echo "测试标准队列..."
qsub -q qintel_wfly -l select=1:ncpus=1,walltime=00:01:00 -N test-auth -j oe /bin/echo "test" 2>&1
if [ $? -eq 0 ]; then
    echo "✅ qintel_wfly队列可用"
else
    echo "❌ qintel_wfly队列不可用"
fi

# 测试GPU队列
echo "测试GPU队列..."
qsub -q gpu_v100 -l select=1:ncpus=1:ngpus=1,walltime=00:01:00 -N test-auth -j oe /bin/echo "test" 2>&1
if [ $? -eq 0 ]; then
    echo "✅ gpu_v100队列可用"
else
    echo "❌ gpu_v100队列不可用"
fi

echo ""
echo "4. 建议解决方案:"
echo "a) 联系系统管理员确认您的项目ID"
echo "b) 请求访问gpu_h200_pinaki队列的权限"
echo "c) 或者暂时使用可用的队列进行测试"
echo "d) 检查您的账户是否在正确的工作组中"



